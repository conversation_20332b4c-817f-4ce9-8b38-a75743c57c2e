name: 95octane-deps

services:
  firebase-emulator:
    image: and<PERSON><PERSON><PERSON>/firebase-tools:latest
    pull_policy: missing
    restart: unless-stopped
    command: firebase --project prod-95octane-app emulators:start --import=/home/<USER>/data/firebase --export-on-exit=/home/<USER>/data/firebase
    ports:
      - "4000:4000" # Emulator Suite UI
      - "4400:4400" # Emulator Hub
      - "4500:4500" # Other Reserved Port
      # - "5000:5000" # Firebase Hosting
      - "5001:5001" # Cloud Functions
      - "8080:8080" # Cloud Firestore
      - "8085:8085" # Pub/Sub
      - "9000:9000" # Realtime Database
      - "9099:9099" # Authentication
      - "9199:9199" # Cloud Storage for Firebase
      - "9299:9299" # Eventarc
    volumes:
      - "${HOME95}/data/emulator:/home/<USER>/data/firebase"
      - "${HOME95}/apiService/firebase.json:/home/<USER>/firebase.json"
      - "${HOME95}/apiService/.firebaserc:/home/<USER>/.firebaserc"
      - "${HOME95}/apiService/firestore.indexes.json:/home/<USER>/firestore.indexes.json"
      - "${HOME95}/apiService/firestore.rules:/home/<USER>/firestore.rules"
      - "${HOME95}/apiService/storage.rules:/home/<USER>/storage.rules"
    networks:
      - firebase-network
    stop_grace_period: 1m
    environment:
      FORCE_COLOR: "true"
      DATA_DIRECTORY: data
      CHOKIDAR_USEPOLLING: "true"

  temporal:
    image: temporalio/server:latest
    pull_policy: missing
    restart: unless-stopped
    entrypoint: temporal
    command: server start-dev --ip 0.0.0.0 --db-filename=/etc/temporal/data/temporal.db --ui-port=8233 --metrics-port=8234
    networks:
      - temporal-network
    ports:
      - 7233:7233
      - 8233:8233
      - 8234:8234
    volumes:
      - "${HOME95}/data/temporal:/etc/temporal/data"

  grafana:
    image: grafana/otel-lgtm
    pull_policy: missing
    restart: unless-stopped
    networks:
      - grafana-network
    ports:
      - 3000:3000
      - 4317:4317
      - 4318:4318

networks:
  temporal-network:
    driver: bridge
    name: temporal-network
  firebase-network:
    driver: bridge
    name: firebase-network
  grafana-network:
    driver: bridge
    name: grafana-network
