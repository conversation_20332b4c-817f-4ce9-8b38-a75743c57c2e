variable "BUILD_NO" {
  default = "$BUILD_NO"
}

variable "RELEASE_TYPE" {
  default = "$RELEASE_TYPE"
}

variable "IMAGE_PREFIX" {
  default = "$IMAGE_PREFIX"
}

# Base target for shared configuration
target "docker-base" {
  platforms = ["linux/amd64"]
  pull = true
  dockerfile = "service/dockerfile"
  args = {
    RELEASE_TYPE = RELEASE_TYPE
  }
}

# Service target
target "95service" {
  inherits = ["docker-base"]
  context = "./"
  tags = ["${IMAGE_PREFIX}/service:${RELEASE_TYPE}-v${BUILD_NO}"]
}

# Worker target
target "95worker" {
  inherits = ["docker-base"]
  context = "./"
  tags = ["${IMAGE_PREFIX}/worker:${RELEASE_TYPE}-v${BUILD_NO}"]
}

# Groups for individual builds
group "service" {
  targets = ["95service"]
}

group "worker" {
  targets = ["95worker"]
}
