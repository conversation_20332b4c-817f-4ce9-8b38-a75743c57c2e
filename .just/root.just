[group('backend')]
[doc('Run the development server for service & worker')]
@serve:
  echo -e "{{GREEN + BOLD}}Running development server for service...{{NORMAL}}"
  just service-serve

[group('backend')]
[doc('Run Lint for all targets')]
@lint:
  # echo "Run Lint for all targets..."
  echo -e "{{GREEN + BOLD}}Linting root ...{{NORMAL}}"
  npx eslint --color --fix package.json
  echo -e "{{GREEN + BOLD}}Linting service ...{{NORMAL}}"
  npx eslint --color --fix service/

[group('backend')]
[doc('Run service development server')]
@dev: service-dev

[group('backend')]
[doc('Run service tests')]
@test: service-test

[group('backend')]
[doc('Run service test coverage')]
@coverage: service-coverage
