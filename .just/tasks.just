FIRESTORE_EMULATOR_HOST := "localhost:8080"

[group('tasks')]
[doc('Import test data to Firestore (local)')]
@import-local:
	echo -e "{{GREEN + BOLD}}Import test data to Firestore (local) ...{{NORMAL}}"
	cd tasks && FIRESTORE_EMULATOR_HOST={{FIRESTORE_EMULATOR_HOST}} pnpm run import

[group('tasks')]
[doc('Import test data to Firestore (cloud)')]
@import-prod:
	echo -e "{{GREEN + BOLD}}Import test data to Firestore (cloud) ...{{NORMAL}}"
	cd tasks && FIRESTORE_EMULATOR_HOST="" pnpm run import

[group('tasks')]
[doc('Generate routes for all rides in "rides" folder')]
@generate-routes:
	echo -e "{{GREEN + BOLD}}Generate routes for all rides in "rides" folder ...{{NORMAL}}"
	cd tasks && FIRESTORE_EMULATOR_HOST="" pnpm run getroutes
