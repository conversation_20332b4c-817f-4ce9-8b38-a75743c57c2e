SERVICE_APP_TYPE := 'service'
WORKER_APP_TYPE := 'worker'
IMAGE_PREFIX := "asia-southeast1-docker.pkg.dev/prod-95octane-app/docker-sg"
BRANCH := `git rev-parse --abbrev-ref HEAD`
# RELEASE_TYPE := if "{{BRANCH}}" == "main" { "prod" } else { "dev" }
RELEASE_TYPE := "prod"
SERVICE_VERSION := `grep '"version":' ./service/package.json | head -n 1 | sed 's/"version": //' | sed 's/[\",[:blank:]]//g'`
SERVICE_BUILD_NO := `grep '"version":' ./service/package.json | head -n 1 | sed 's/"version": //' | sed 's/[\",[:blank:]]//g' | sed 's/.*+//'`
WORKER_VERSION := `grep '"version":' ./worker/package.json | head -n 1 | sed 's/"version": //' | sed 's/[\",[:blank:]]//g'`
WORKER_BUILD_NO := `grep '"version":' ./worker/package.json | head -n 1 | sed 's/"version": //' | sed 's/[\",[:blank:]]//g' | sed 's/.*+//'`

[group("release")]
[doc('Release the service (dev/prod)')]
@release-service: (_release SERVICE_APP_TYPE SERVICE_BUILD_NO SERVICE_VERSION)

[group("release")]
[doc('Release the worker (dev/prod)')]
@release-worker: (_release WORKER_APP_TYPE WORKER_BUILD_NO WORKER_VERSION)

[group("release")]
[doc('Build the service docker image')]
@image-service: (_build-image SERVICE_APP_TYPE SERVICE_BUILD_NO)

[group("release")]
[doc('Run the service docker image')]
@run-service:
  echo -e "{{GREEN + BOLD}}Running image: {{IMAGE_PREFIX}}/{{SERVICE_APP_TYPE}}:{{RELEASE_TYPE}}-v{{SERVICE_BUILD_NO}} ... {{NORMAL}}"
  docker run --rm -p 8888:9999 --env LOG_LEVEL="debug" {{IMAGE_PREFIX}}/{{SERVICE_APP_TYPE}}:{{RELEASE_TYPE}}-v{{SERVICE_BUILD_NO}}


@_release APP_TYPE BUILD_NO VERSION: (_release-tag APP_TYPE BUILD_NO) (_build-image APP_TYPE BUILD_NO) (_deploy-image APP_TYPE BUILD_NO)

@_validate APP_TYPE: _docker-check
  echo -en "{{GREEN + BOLD}}Checking for uncommitted changes ... {{NORMAL}}"
  if [[ $(git status --porcelain) ]]; then echo -e "{{RED}}FAILED{{NORMAL}}"; exit 1; fi
  echo -e "{{GREEN}}OK{{NORMAL}}"
  echo -en "{{GREEN + BOLD}}Checking for unpushed commits ... {{NORMAL}}"
  if [[ $(git log --branches --not --remotes) ]]; then echo -e "{{RED}}FAILED{{NORMAL}}"; exit 1; fi
  echo -e "{{GREEN}}OK{{NORMAL}}"
  echo -en "{{GREEN + BOLD}}Checking for existing tag ... {{NORMAL}}"
  if [[ $(git describe --exact-match --tags HEAD 2>&1 | egrep "^{{RELEASE_TYPE}}-{{APP_TYPE}}") ]]; then echo -e "{{RED}}FAILED{{NORMAL}}"; exit 1; fi
  echo -e "{{GREEN}}OK{{NORMAL}}"

@_build-image APP_TYPE BUILD_NO:
  echo -e "{{GREEN + BOLD}}Clean local dist folder ... {{NORMAL}}"
  -rm -rf ./{{APP_TYPE}}/dist
  echo -e "{{GREEN + BOLD}}Building image: {{APP_TYPE}}:{{RELEASE_TYPE}}-v{{BUILD_NO}} ... {{NORMAL}}"
  IMAGE_PREFIX={{IMAGE_PREFIX}} BUILD_NO={{BUILD_NO}} RELEASE_TYPE={{RELEASE_TYPE}} docker buildx bake --file ./utils/docker-bake.hcl {{APP_TYPE}}

@_release-tag APP_TYPE BUILD_NO: (_validate APP_TYPE)
  echo -en "{{GREEN + BOLD}}Tagging: {{APP_TYPE}}-{{RELEASE_TYPE}}-v{{BUILD_NO}} ... {{NORMAL}}"
  if ! git tag "{{APP_TYPE}}-{{RELEASE_TYPE}}-v{{BUILD_NO}}" 2>/dev/null; then echo -e "{{RED}}DUPLICATE | FAILED{{NORMAL}}"; exit 1; fi
  echo -e "{{GREEN}}OK{{NORMAL}}"
  echo -e "{{GREEN + BOLD}}Pushing tag to remote ... {{NORMAL}}"
  if ! git push origin "{{APP_TYPE}}-{{RELEASE_TYPE}}-v{{BUILD_NO}}"; then echo -e "{{RED}}FAILED{{NORMAL}}"; exit 1; fi
  echo -e "{{GREEN}}OK{{NORMAL}}"

@_deploy-image IMAGE_NAME BUILD_NO:
  echo -e "{{GREEN + BOLD}}Deploying image to Google Cloud Run ... {{NORMAL}}"
  gcloud run services update {{RELEASE_TYPE}}-{{IMAGE_NAME}} --image "{{IMAGE_PREFIX}}/{{IMAGE_NAME}}:{{RELEASE_TYPE}}-v{{BUILD_NO}}" --region asia-southeast1 --no-use-http2

@_docker-check:
  echo -en "{{GREEN + BOLD}}Checking docker status ... {{NORMAL}}"
  if ! docker info >/dev/null 2>&1; then echo -e "{{RED}}FAILED{{NORMAL}}"; exit 1; fi
  echo -e "{{GREEN}}RUNNING{{NORMAL}}"
