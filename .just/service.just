[group('service')]
[doc('Start dependencies & run the development server')]
@service-serve:
  echo -e "{{GREEN + BOLD}}Running development server...{{NORMAL}}"
  cd service && pnpm run serve

[group('service')]
[doc('Run the development server')]
@service-dev:
  echo -e "{{GREEN + BOLD}}Running development server...{{NORMAL}}"
  cd service && pnpm run dev

[group('service')]
[doc('Build the production server')]
@service-build:
  echo -e "{{GREEN + BOLD}}Building production server...{{NORMAL}}"
  cd service && pnpm run build:dev

[group('service')]
[doc('Run the production server')]
@service-start:
  echo -e "{{GREEN + BOLD}}Running production server...{{NORMAL}}"
  cd service && pnpm run start

[group('service')]
[doc('Run the lint')]
@service-lint:
  echo -e "{{GREEN + BOLD}}Running lint...{{NORMAL}}"
  cd service && pnpm run lint

[group('service')]
[doc('Run the test')]
@service-test:
  echo -e "{{GREEN + BOLD}}Running test...{{NORMAL}}"
  cd service && pnpm run test

[group('service')]
[doc('Run the test coverage')]
@service-coverage:
  echo -e "{{GREEN + BOLD}}Running test coverage...{{NORMAL}}"
  cd service && pnpm run test:coverage
