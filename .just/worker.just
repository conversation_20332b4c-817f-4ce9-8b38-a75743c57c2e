[group('worker')]
[doc('Run the development server')]
@worker:
  echo -e "{{GREEN + BOLD}}Running worker...{{NORMAL}}"
  cd worker && pnpm run serve

[group('worker')]
[doc('Build the production server')]
@worker-build:
  echo -e "{{GREEN + BOLD}}Building worker...{{NORMAL}}"
  cd worker && pnpm run build:dev

[group('worker')]
[doc('Run the production server')]
@worker-start:
  echo -e "{{GREEN + BOLD}}Running worker...{{NORMAL}}"
  cd worker && pnpm run start

[group('worker')]
[doc('Run the lint')]
@worker-lint:
  echo -e "{{GREEN + BOLD}}Running lint...{{NORMAL}}"
  cd worker && pnpm run lint
