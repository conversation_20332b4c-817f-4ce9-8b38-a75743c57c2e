# 95octane Backend Monorepo

This is a monorepo for the 95octane backend services.

## 🏗️ Architecture

- **`service`**: Fastify API service
- **`worker`**: Temporal-based background worker
- **`tasks`**: Ad-hoc scripts for data management
- **`utils`**: Docker configurations and development utilities

## 🚀 Tech Stack

- [NodeJS](https://nodejs.org/) - Runtime environment
- [TypeScript](https://www.typescriptlang.org/) - Type-safe development
- [Fastify](https://www.fastify.io/) - Web framework
- [Zod](https://zod.dev/) - Schema validation
- [Firebase](https://firebase.google.com/) - Backend services
- [Temporal](https://temporal.io/) - Workflow orchestration
- [Webpack](https://webpack.js.org/) - Module bundling

## 🛠️ Development

### Quick Start

```bash
# Install dependencies
pnpm install

# Start development servers
pnpm run serve

# Start individual services
pnpm run service  # API service only
pnpm run worker   # Background worker only

# Run tests
pnpm run test

# Run linting
pnpm run lint
```

## 🔧 Configuration

### Environment Variables

- `NODE_ENV`: Environment (development/production/test)
- `LOG_LEVEL`: Logging level (silent/error/warn/info/debug)
- `USE_EMULATOR`: Use Firebase emulator (true/false)
- `MINIMUM_APP_VERSION`: Minimum supported app version
- `HOST`: Server host (default: 0.0.0.0)
- `PORT`: Server port (default: 8888)

## 📚 Documentation

### Core Documentation

- **[Project Structure](./PROJECT_STRUCTURE.md)** - Detailed project structure and architecture
- **[Coding Standards](./CODING_STANDARDS.md)** - Code style and best practices

### Additional Resources

- **[Docker Configuration](./utils/)** - Container configurations
- **[Testing Guide](./service/src/_testUtils/)** - Testing utilities and patterns
