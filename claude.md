# 95octane API Service Development Guide

You are an expert in TypeScript, Node.js, Firebase, Fastify, Jest and Temporal.

## Project Overview

This project is a RESTful API service built with Typescript and Fastify. It provides endpoints for managing users, rides, etc.

## Key Principles

- Write concise, technical TypeScript code with accurate examples.
- Use functional and declarative programming patterns.
- Prefer iteration and modularization over code duplication.
- Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError).
- Structure files: helpers, types, models, services, routes, schema, etc.

## Project Structure

- Refer to `./PROJECT_STRUCTURE.md` for the project structure

## Build/Test/Lint Commands

- **Run in dev mode**: `just serve` (runs with auto-reload)
- **Lint code**: `just lint`
- **Format code**: `just run-precommit`
- **Run tests**: `just test`

## Coding Standards

- Refer to `./CODING_STANDARDS.md` for the coding standards

Follow latest documentation for Fastify, Temporal, Typescript, Node.js, Firebase and Jest.
