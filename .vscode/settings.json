{
  "[javascript]": {
    "editor.defaultFormatter": "rvest.vs-code-prettier-eslint"
  },
  "[json]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[markdown]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[typescript]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "diffEditor.ignoreTrimWhitespace": false,
  "diffEditor.renderSideBySide": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "editor.formatOnSave": true,
  "editor.quickSuggestions": {
    "comments": true,
    "other": true,
    "strings": true
  },
  "editor.suggest.insertMode": "replace",
  "eslint.enable": true,
  "eslint.format.enable": true,
  "eslint.lintTask.enable": true,
  "eslint.lintTask.options": "-c eslint.config.mjs .",
  "eslint.options": {
    "extensions": [
      ".js",
      ".jsx",
      ".ts",
      ".tsx",
      ".vue",
      ".html",
      ".md"
    ]
  },
  "eslint.run": "onSave",
  "eslint.useFlatConfig": true,
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "html",
    "markdown",
    "vue",
    "yaml"
  ],
  "eslint.workingDirectories": [
    {
      "mode": "auto"
    }
  ],
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.expand": false,
  "explorer.fileNesting.patterns": {
    ".gitignore": ".gitkeep,.pre-commit-config.yaml,analysis_options.yaml,git-conventional-commits.yaml,.gitleaks.toml,",
    "dockerfile": ".dockerignore,dockerfile.dev",
    "firebase.json": "firebase-debug.json,.firebaserc,*.rules,firestore.indexes.json",
    "package.json": "package-lock.json,eslint.config.js,.eslintrc.js,tsconfig.json,tsconfig.dev.json,eslint.config.mjs,.prettierrc.yaml,.firebaserc,webpack.config.mjs,nodemon.json,waitOn.json,jest.config.mjs,wrangler.jsonc,worker-configuration.d.ts,pnpm-lock.yaml,pnpm-workspace.yaml,vitest.config.ts,vite.config.ts,",
    ".envrc": ".env,.env.*,.nvmrc,",
    "readme.md": "claude.md,.cursorrules,CODING_STANDARDS.md,PROJECT_STRUCTURE.md"
  },
  "files.associations": {
    "package.json": "jsonc",
    ".env": "shellscript",
    ".env.*": "shellscript",
    ".envrc": "shellscript",
    "firebase.json": "jsonc",
  },
  "files.autoGuessEncoding": true,
  "files.exclude": {
    "**/*.log": true,
    "**/dist/": true,
    "**/node_modules/": true,
    "**/ui-debug.log": true
  },
  "outline.showKeys": true,
  "prettier.enable": false,
  "todo-tree.general.tags": [
    "TODO",
    "FIXME",
    "HACK",
    "REVIEW",
    "NOTE",
    "REFACTOR",
    "REMOVE"
  ],
  "todo-tree.highlights.defaultHighlight": {
    "hideFromActivityBar": true,
    "hideFromStatusBar": false,
    "type": "text-and-comment"
  }
}
