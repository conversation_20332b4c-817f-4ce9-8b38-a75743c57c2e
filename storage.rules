// DONOT update the rules directly in the Firebase console.
// Instead, update the rules in the `flutterApp` repo and then commit the chantes to `main` branch.

rules_version = '2';

// Craft rules based on data in your Firestore database
service firebase.storage {
  match /b/{bucket}/o {
    match /u/{userId}/{fileId} {
      allow read: if request.auth != null;
      allow write: if request.auth.uid == userId;
    }
    match /routes/r/{rideId}/{fileId} {
      allow read: if request.auth != null;
      allow write: if false;
    }
    match /routes/u/{userId}/{fileId} {
      allow read: if request.auth == userId;
      allow write: if false;
    }
  }
}
