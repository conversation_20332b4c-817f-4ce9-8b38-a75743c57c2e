import type { PlatformName } from "@/_shared/common.schema";
import type { FastifyInstance, LightMyRequestResponse } from "fastify";
import { config } from "@/_shared/config";
import { buildApp } from "@/_shared/fastify";
import { generateId } from "@/_shared/nanoid";

export const EmptyTestResponse: Record<string, unknown> = {};

export type RequestOptions = {
  method: "GET" | "POST";
  path: string;
  auth: boolean | "fake";
  body?: unknown;
};
class TestAppInstance {
  private app: FastifyInstance | null = null;
  private authToken: string | null = null;
  private traceId: string = generateId();

  private async initApp(): Promise<FastifyInstance> {
    this.app = await buildApp();
    return this.app;
  }

  public setAuthToken(token: string) {
    this.authToken = token;
  }

  private async getHeaders(auth: boolean | "fake"): Promise<Record<string, string>> {
    const headers: Record<string, string> = {};
    const platform: PlatformName = Math.random() < 0.5 ? "iOS" : "android";
    headers["Content-Type"] = "application/json";
    headers.Accept = "application/json";
    headers["X-Trace-Id"] = this.traceId;
    headers["X-Platform-Type"] = "mobile";
    headers["X-Platform-Name"] = platform;
    headers["X-App-Version"] = config.minAppVersion.toString();
    if (auth === true) {
      headers.Authorization = `Bearer ${this.authToken}`;
    }
    else if (auth === "fake") {
      headers.Authorization = `Bearer fake-token`;
    }
    return headers;
  }

  public async inject(opts: RequestOptions): Promise<LightMyRequestResponse> {
    if (!this.app) {
      this.app = await this.initApp();
    }
    const response = await this.app.inject({
      body: opts.body ? JSON.stringify(opts.body) : undefined,
      headers: await this.getHeaders(opts.auth),
      method: opts.method,
      url: opts.path,
    });
    if (response) {
      return response;
    }
    throw new Error("No response received");
  }
}

export const testAppInstance = new TestAppInstance();
