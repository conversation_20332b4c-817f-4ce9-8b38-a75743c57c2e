import { FirebaseService } from "@/_testUtils/firebase";

export class Cleanup {
  static async deleteUserRides(userId: string) {
    const db = FirebaseService.getFirestore();
    const rides = await db
      .collection("users")
      .doc(userId)
      .collection("rides")
      .get();
    for (const ride of rides.docs) {
      await ride.ref.delete();
    }
  }

  static async deleteUser(userId: string) {
    const db = FirebaseService.getFirestore();
    await db.collection("users").doc(userId).delete();
  }

  static async deleteRide(rideId: string) {
    const db = FirebaseService.getFirestore();
    await db.collection("rides").doc(rideId).delete();
  }
}
