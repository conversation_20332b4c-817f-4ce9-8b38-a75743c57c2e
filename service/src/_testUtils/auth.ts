/* eslint-disable node/no-process-env */
import type {
  User,
} from "@/_testUtils/firebase";

import {
  FirebaseService,
  getIdToken,
  signInWithEmailAndPassword,
} from "@/_testUtils/firebase";

export type { User };

export class AuthClient {
  private auth = FirebaseService.getAuth();
  private user: User | null = null;

  async getToken() {
    const userEmail = process.env.TEST_USER_EMAIL;
    const userPassword = process.env.TEST_USER_PASSWORD;
    if (!userEmail || !userPassword) {
      throw new Error("TEST_USER_EMAIL or TEST_USER_PASSWORD is not set");
    }
    const user = await signInWithEmailAndPassword(
      this.auth,
      userEmail,
      userPassword,
    );
    if (!user.user) {
      throw new Error("Failed to sign in with email and password");
    }
    this.user = user.user;
    const token = await getIdToken(this.user, true);
    return token;
  }

  getUser() {
    if (!this.user) {
      throw new Error("User not found");
    }
    return this.user;
  }
}
