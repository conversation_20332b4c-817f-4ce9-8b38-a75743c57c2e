/* eslint-disable node/no-process-env */
/* eslint-disable import/no-internal-modules */
import type { FirebaseApp } from "firebase/app";
import type { Auth } from "firebase/auth";

import admin from "firebase-admin";
import { initializeApp } from "firebase/app";
import { connectAuthEmulator, getAuth } from "firebase/auth";

export { getIdToken, signInWithEmailAndPassword, type User } from "firebase/auth";

export class FirebaseService {
  private static initialized: boolean = false;
  private static client: FirebaseApp;
  private static auth: Auth;
  private static firestore: admin.firestore.Firestore;

  private static initialize() {
    if (this.initialized) {
      return;
    }

    if (!admin.apps.length) {
      admin.initializeApp({}, "test");
    }

    this.client = initializeApp({
      apiKey: process.env.FIREBASE_API_KEY as string,
      authDomain: process.env.FIREBASE_AUTH_DOMAIN as string,
      projectId: process.env.FIREBASE_PROJECT_ID as string,
    }, {
      automaticDataCollectionEnabled: false,
      name: "test",
    });
    this.auth = getAuth(this.client);
    connectAuthEmulator(
      this.auth,
      `http://${process.env.FIREBASE_AUTH_EMULATOR_HOST}`,
      {
        disableWarnings: true,
      },
    );
    this.firestore = admin.firestore();
    // this.firestore.settings({
    //   ignoreUndefinedProperties: true,
    //   host: "127.0.0.1",
    //   port: 8080,
    //   ssl: false,
    // });
    this.initialized = true;
  }

  public static getAuth() {
    this.initialize();
    return this.auth;
  }

  public static getFirestore() {
    this.initialize();
    return this.firestore;
  }
}
