import type { User, UserAltDetails, UserSettings } from "@/modules/user/user.schema";

import { UserRepo } from "@/modules/user/user.repo";

export class UserService {
  static async get(input: User): Promise<User> {
    return await UserRepo.get(input);
  }

  // static async save(user: User): Promise<void> {
  //   await UserRepo.save(user);
  // }

  static async saveSettings(
    userId: string,
    settings: UserSettings,
  ): Promise<void> {
    await UserRepo.saveSettings(userId, settings);
  }

  static async saveNotificationToken(
    userId: string,
    token: string | null,
  ): Promise<void> {
    await UserRepo.saveNotificationToken(userId, token);
  }

  static async saveAltDetails(
    userId: string,
    altDetails: UserAltDetails,
  ): Promise<void> {
    await UserRepo.saveAltDetails(userId, altDetails);
  }
}
