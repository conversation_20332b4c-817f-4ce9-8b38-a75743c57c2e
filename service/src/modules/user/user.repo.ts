import type { DocumentData } from "@/_shared/firebase";
import type { User, UserAltDetails, UserSettings } from "@/modules/user/user.schema";

import { MyError, StatusCodes } from "@/_shared/error";
import {
  convertDatesToTimestamps,
  convertTimestampsToDates,

  FieldValue,
  getCollection,
  getDocument,
  setDocument,
  updateDocument,
} from "@/_shared/firebase";
import {
  UserSchema,
} from "@/modules/user/user.schema";

export class UserRepo {
  static async get(input: User): Promise<User> {
    // First save input to firestore
    // Use set method since the user may not exist in firestore if first time login
    await this.save(input);
    // Fetch user from firestore
    const userDoc = await getDocument(`users/${input.id}`);
    /* v8 ignore next 10 */
    if (!userDoc) {
      throw new MyError(
        "warn",
        "UserRepo.get",
        "USER_NOT_FOUND",
        StatusCodes.NOT_FOUND,
        new Error("User not found"),
        { userId: input.id },
      );
    }
    // Convert timestamps to dates
    const userData = convertTimestampsToDates(userDoc as DocumentData);
    const ridesDoc = await getCollection(`users/${input.id}/rides`);
    // Fetch rides subscribed by user
    userData.rides = ridesDoc.map(ride => ride.id);
    // Parse user document to user object
    const parsedUser = UserSchema.parse(userData);
    // Return parsed user object
    return parsedUser;
  }

  static async save(user: User): Promise<void> {
    // Save user in firestore
    await setDocument(`users/${user.id}`, convertDatesToTimestamps(user));
  }

  static async saveSettings(
    userId: string,
    settings: UserSettings,
  ): Promise<void> {
    // Save settings in firestore
    await updateDocument(`users/${userId}`, {
      settings,
      updatedAt: FieldValue.serverTimestamp(),
    });
  }

  static async saveNotificationToken(
    userId: string,
    token: string | null,
  ): Promise<void> {
    // Save notification token to firestore
    await updateDocument(`users/${userId}`, {
      notificationToken: token,
      updatedAt: FieldValue.serverTimestamp(),
    });
  }

  static async saveAltDetails(
    userId: string,
    altDetails: UserAltDetails,
  ): Promise<void> {
    // Save user alt details to firestore
    await updateDocument(`users/${userId}`, {
      ...altDetails,
      updatedAt: FieldValue.serverTimestamp(),
    });
  }
}
