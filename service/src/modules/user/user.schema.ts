import {
  DateTimeSchema,
  LocationSchema,
} from "@/_shared/common.schema";

import { z } from "zod";

const providerEnum = z.enum(["password", "phone", "google.com", "apple.com"]);
const userTypeEnum = z.enum(["beta", "subscriber", "trial", "free"]);
const PhoneRegEx: RegExp
  = /^(\+)?[1-9]\d{0,3}[\s.-]?\(?[1-9]\d{0,3}\)?[\s.-]?\d{1,4}[\s.-]?\d{1,4}[\s.-]?\d{1,9}$/;

export const UserSettingsSchema = z.object({
  homeLocation: LocationSchema.nullable(),
  notifications: z.boolean().default(true),
  shareLocation: z.boolean().default(true),
});

export const UserAltSchema = z.object({
  altName: z.string().nullable(),
  altPhotoURL: z.string().nullable(),
});

export const UserNotificationTokenSchema = z.object({
  token: z.string().min(10).nullable(),
});

export const UserConfigSchema = z.object({
  config: z.object({
    appSHA1: z.string().nullable(),
    revenueCatAPIKey: z.string(),
    sendCrashlyticsData: z.boolean(),
    urlAboutUs: z.string(),
    urlPrivacy: z.string(),
    urlTerms: z.string(),
  }),
  sunrise: z.string().nullable(),
  sunset: z.string().nullable(),
});

export const UserSchema = z.object({
  altName: z.string().min(4).nullable().default(null),
  altPhotoURL: z
    .union([z.literal("").transform(() => null), z.string().url()])
    .nullable()
    .default(null),
  createdAt: DateTimeSchema.default(new Date().toISOString()),
  email: z.string().email().optional().nullable().default(null),
  id: z.string().min(10),
  isAnonymous: z.boolean().default(false),
  isEmailVerified: z.boolean().default(false),
  lastSignInAt: DateTimeSchema.default(new Date().toISOString()),
  name: z.string().min(10).optional().nullable().default(null),
  notificationToken: z.string().min(10).nullable().default(null),
  phoneNumber: z.string().regex(PhoneRegEx).optional().nullable().default(null),
  photoURL: z
    .union([z.literal("").transform(() => null), z.string().url()])
    .optional()
    .nullable()
    .default(null),
  provider: z.array(providerEnum).default([]),
  rides: z.string().array().default([]),
  settings: UserSettingsSchema.default({
    homeLocation: null,
    notifications: true,
    shareLocation: true,
  }),
  subscriptionExpiryAt: z.string().datetime().nullable().default(null),
  type: userTypeEnum.default("free"),
  updatedAt: DateTimeSchema.default(new Date().toISOString()),
});

export const UserRequestSchema = z.object({
  lat: z.number(),
  lng: z.number(),
  notificationToken: z.string().nullable(),
});

export const UserResponseSchema = z.object({
  config: UserConfigSchema,
  user: UserSchema,
});

export type User = z.infer<typeof UserSchema>;
export type UserSettings = z.infer<typeof UserSettingsSchema>;
export type UserAltDetails = z.infer<typeof UserAltSchema>;
export type UserNotificationToken = z.infer<typeof UserNotificationTokenSchema>;
export type UserConfig = z.infer<typeof UserConfigSchema>;
export type UserRequest = z.infer<typeof UserRequestSchema>;
export type UserResponse = z.infer<typeof UserResponseSchema>;
