import type { FastifyInstance } from "fastify";
import type { ZodTypeProvider } from "fastify-type-provider-zod";

import {
  SuccessResponseSchema,
  UserIdParamSchema,
} from "@/_shared/common.schema";
import { StatusCodes } from "@/_shared/error";
import {
  getUser,
  saveAltDetails,
  saveNotificationToken,
  saveSettings,
} from "@/modules/user/user.api";
import {
  UserAltSchema,
  UserNotificationTokenSchema,
  UserRequestSchema,
  UserResponseSchema,
  UserSettingsSchema,
} from "@/modules/user/user.schema";

export function setupUserRoutes(app: FastifyInstance) {
  // Get user
  app.withTypeProvider<ZodTypeProvider>().post("/user/:userId", {
    handler: getUser,
    schema: {
      body: UserRequestSchema,
      params: UserIdParamSchema,
      response: {
        [StatusCodes.OK]: UserResponseSchema,
      },
    },
  });

  // // Save user
  // app.withTypeProvider<ZodTypeProvider>().post("/user", {
  //   handler: saveUser,
  //   schema: {
  //     body: UserSchema,
  //     response: {
  //       [StatusCodes.OK]: SuccessResponseSchema,
  //     },
  //   },
  // });

  // Save user settings
  app.withTypeProvider<ZodTypeProvider>().post("/user/:userId/settings", {
    handler: saveSettings,
    schema: {
      body: UserSettingsSchema,
      params: UserIdParamSchema,
      response: {
        [StatusCodes.OK]: SuccessResponseSchema,
      },
    },
  });

  // Save Notification Token
  app
    .withTypeProvider<ZodTypeProvider>()
    .post("/user/:userId/notification-token", {
      handler: saveNotificationToken,
      schema: {
        body: UserNotificationTokenSchema,
        params: UserIdParamSchema,
        response: {
          [StatusCodes.OK]: SuccessResponseSchema,
        },
      },
    });

  // Save Alt Details
  app.withTypeProvider<ZodTypeProvider>().post("/user/:userId/alt", {
    handler: saveAltDetails,
    schema: {
      body: UserAltSchema,
      params: UserIdParamSchema,
      response: {
        [StatusCodes.OK]: SuccessResponseSchema,
      },
    },
  });
}
