import { EmptyTestResponse, testAppInstance } from "@/_testUtils/app";
import { afterEach, beforeEach, describe, expect, test } from "vitest";

describe("/init", () => {
  let testResponse = EmptyTestResponse;
  const app = testAppInstance;
  beforeEach(() => {
    testResponse = EmptyTestResponse;
  });
  afterEach((context) => {
    context.onTestFailed(() => {
      console.debug("API Response for failed test:", JSON.stringify(testResponse, null, 2));
    });
  });
  test("init", async () => {
    const response = await app.inject({
      auth: false,
      body: {},
      method: "POST",
      path: "/init",
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(200);
    expect(testResponse.userEnabled).toBe(true || false);
  });

  test("healthcheck", async () => {
    const response = await app.inject({
      auth: false,
      method: "GET",
      path: "/healthcheck",
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(200);
    expect(testResponse.success).toBe(true);
  });
});
