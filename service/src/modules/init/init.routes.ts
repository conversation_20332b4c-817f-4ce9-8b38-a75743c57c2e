import type { FastifyInstance } from "fastify";
import type { Zod<PERSON>ype<PERSON>rovider } from "fastify-type-provider-zod";

import { StatusCodes } from "@/_shared/error";
import { getInitSettings } from "@/modules/init/init.api";
import { InitResponseSchema } from "@/modules/init/init.schema";

export function setupInitRoutes(app: FastifyInstance) {
  // Get init settings
  app.withTypeProvider<ZodTypeProvider>().post("/init", {
    config: {
      auth: false,
    },
    handler: getInitSettings,
    schema: {
      response: {
        [StatusCodes.OK]: InitResponseSchema,
      },
    },
  });
}
