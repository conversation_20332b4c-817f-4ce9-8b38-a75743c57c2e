import type { DocumentData } from "@/modules/ride/ride.repo";
import type {
  Participant,
  Ride,
  RideRequest,
  RiderLocation,
  ShareLocationRequest,
} from "@/modules/ride/ride.schema";

import { RideRepo } from "@/modules/ride/ride.repo";
import {
  RideSchema,
} from "@/modules/ride/ride.schema";

// TODO: https://developers.google.com/maps/documentation/routes/client-libraries#node.js

export class RideService {
  static async findAllByUser(userId: string): Promise<Ride[]> {
    const rides = await RideRepo.findAllByUser(userId);
    const parsedRides = rides
      .map((ride: DocumentData) => {
        const parsedRide = RideSchema.safeParse(ride);
        /* v8 ignore next 6 */
        if (!parsedRide.success) {
          console.error(
            `RideService.findAllByUser: ride:${JSON.stringify(ride)}, error:${parsedRide.error.toString()}`,
          );
          return null;
        }
        return parsedRide.data;
      })
      .filter((ride: Ride | null): ride is Ride => ride !== null);
    return parsedRides;
  }

  static async find(rideId: string): Promise<Ride> {
    return await RideRepo.find(rideId);
  }

  static async create(userId: string, ride: RideRequest): Promise<Ride> {
    return await RideRepo.create(userId, ride);
  }

  static async update(
    userId: string,
    rideId: string,
    ride: RideRequest,
  ): Promise<void> {
    await RideRepo.update(userId, rideId, ride);
  }

  static async rsvpRide(
    rideId: string,
    participant: Participant,
  ): Promise<void> {
    await RideRepo.rsvpRide(rideId, participant);
  }

  static async shareLocation(
    userId: string,
    rideId: string,
    location: ShareLocationRequest,
  ): Promise<RiderLocation[]> {
    const participants = await RideRepo.shareLocation(userId, rideId, location);
    return participants;
  }
}
