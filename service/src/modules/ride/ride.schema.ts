import { DateTimeSchema } from "@/_shared/common.schema";

import { z } from "zod";

const BreakpointEnum = z.enum([
  "origin",
  "destination",
  "additionalDestination",
  "meetingPoint",
  "haltPoint",
  "restaurant",
  "fuelStation",
  "other",
]);

const ParticipantStatusEnum = z.enum(["yes", "no", "maybe"]);

export const ShareLocationRequestSchema = z.object({
  lat: z.number(),
  lng: z.number(),
});

export const RiderLocationSchema = z.object({
  id: z.string().min(10),
  lat: z.number(),
  lng: z.number(),
  updatedAt: DateTimeSchema,
});

export const ShareLocationResponseSchema = z.object({
  riders: z.array(RiderLocationSchema),
});

const LocationSchema = z.object({
  id: z.string().min(6),
  latitude: z.number(),
  longitude: z.number(),
  placeId: z.string().min(6).nullable(),
  title: z.string().min(3).max(100),
  type: BreakpointEnum,
});

export const ParticipantSchema = z.object({
  displayName: z.string().min(3).max(100),
  id: z.string().min(10),
  joiningLocationId: z.string().min(6),
  photoUrl: z.string().url().nullable(),
  status: ParticipantStatusEnum,
});

export const RideSchema = z.object({
  adminIds: z.array(z.string().min(10)),
  breakpointsFrom: z.array(LocationSchema).default([]),
  breakpointsTo: z.array(LocationSchema),
  createdAt: DateTimeSchema.default(new Date().toISOString()),
  creatorId: z.string().min(10),
  description: z.string().min(10).max(400),
  endDate: DateTimeSchema,
  endLocation: LocationSchema,
  id: z.string().min(20).max(30),
  participants: z.array(ParticipantSchema).default([]).optional(),
  posterUrl: z.string().url().nullable(),
  startDate: DateTimeSchema,
  startLocation: LocationSchema,
  title: z.string().min(10).max(100),
  updatedAt: DateTimeSchema.default(new Date().toISOString()),
});

export const RideRequestSchema = RideSchema.omit({
  adminIds: true,
  createdAt: true,
  creatorId: true,
  id: true,
  participants: true,
  updatedAt: true,
});

export const RidesSchema = z.object({
  rides: z.array(RideSchema).optional(),
});

export const ParticipantsSchema = z.object({
  participants: z.array(ParticipantSchema.optional()),
});

export const CreateRideResponseSchema = z.object({
  data: z.object({
    ride: RideSchema,
  }),
  success: z.literal(true),
});

export type Ride = z.infer<typeof RideSchema>;
export type RideRequest = z.infer<typeof RideRequestSchema>;
export type Rides = z.infer<typeof RidesSchema>;
export type Participant = z.infer<typeof ParticipantSchema>;
export type ShareLocationRequest = z.infer<typeof ShareLocationRequestSchema>;
export type RiderLocation = z.infer<typeof RiderLocationSchema>;
export type ShareLocationResponse = z.infer<typeof ShareLocationResponseSchema>;
export type Participants = z.infer<typeof ParticipantsSchema>;
export type CreateRideResponse = z.infer<typeof CreateRideResponseSchema>;
