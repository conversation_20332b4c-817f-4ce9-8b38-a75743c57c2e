import type { FastifyInstance } from "fastify";
import type { ZodTypeProvider } from "fastify-type-provider-zod";

import {
  RideIdParamSchema,
  SuccessResponseSchema,
  UserIdParamSchema,
} from "@/_shared/common.schema";
import { StatusCodes } from "@/_shared/error";
import {
  CreateRideResponseSchema,
  ParticipantSchema,
  RideRequestSchema,
  RideSchema,
  RidesSchema,
  ShareLocationRequestSchema,
  ShareLocationResponseSchema,
} from "@/modules/ride/ride.schema";
import {
  createRide,
  getRide,
  getRidesByUser,
  rsvpRide,
  shareLocation,
  updateRide,
} from "@/modules/ride/rides.api";

export function setupRideRoutes(app: FastifyInstance) {
  // Get all rides by user
  app.withTypeProvider<ZodTypeProvider>().get("/rides/:userId", {
    handler: getRidesByUser,
    schema: {
      params: UserIdParamSchema,
      response: {
        [StatusCodes.OK]: RidesSchema,
      },
    },
  });

  // Get a ride by Ride Id
  app.withTypeProvider<ZodTypeProvider>().get("/ride/:rideId", {
    handler: getRide,
    schema: {
      params: RideIdParamSchema,
      response: {
        [StatusCodes.OK]: RideSchema,
      },
    },
  });

  // Create a ride
  app.withTypeProvider<ZodTypeProvider>().post("/ride", {
    handler: createRide,
    schema: {
      body: RideRequestSchema,
      response: {
        [StatusCodes.OK]: CreateRideResponseSchema,
      },
    },
  });

  // Update a ride
  app.withTypeProvider<ZodTypeProvider>().post("/ride/:rideId", {
    handler: updateRide,
    schema: {
      body: RideRequestSchema,
      params: RideIdParamSchema,
      response: {
        [StatusCodes.OK]: SuccessResponseSchema,
      },
    },
  });

  // RSVP to a ride
  app.withTypeProvider<ZodTypeProvider>().post("/ride/:rideId/rsvp", {
    handler: rsvpRide,
    schema: {
      body: ParticipantSchema,
      params: RideIdParamSchema,
      response: {
        [StatusCodes.OK]: SuccessResponseSchema,
      },
    },
  });

  // Share location
  app.withTypeProvider<ZodTypeProvider>().post("/ride/:rideId/location", {
    handler: shareLocation,
    schema: {
      body: ShareLocationRequestSchema,
      params: RideIdParamSchema,
      response: {
        [StatusCodes.OK]: ShareLocationResponseSchema,
      },
    },
  });

  // TODO: Delete Ride
}
