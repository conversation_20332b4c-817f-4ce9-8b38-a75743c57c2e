import type {
  DocumentData,
} from "@/_shared/firebase";
import type {
  Participant,
  Ride,
  RideRequest,
  RiderLocation,
  ShareLocationRequest,
} from "@/modules/ride/ride.schema";

import { MyError, StatusCodes } from "@/_shared/error";
import {
  convertDatesToTimestamps,
  convertTimestampsToDates,
  FieldPath,
  FieldValue,
  getCollection,
  getDocument,
  setDocument,
  updateDocument,
} from "@/_shared/firebase";
import { generateId } from "@/_shared/nanoid";
import {
  RiderLocationSchema,
  RideSchema,
} from "@/modules/ride/ride.schema";

export type { DocumentData };

export class RideRepo {
  static async findAllByUser(userId: string): Promise<Ride[]> {
    // Fetch rides from user's rides collection
    const userRidesCollection = await getCollection(`users/${userId}/rides`);
    // Check if user has rides
    /* v8 ignore next 6 */
    if (!userRidesCollection || userRidesCollection.length === 0) {
      console.warn(
        `RideRepo.findAllByUser: userId:${userId}, User has no rides`,
      );
      return [];
    }
    // Generate list of ride ids
    const ridesList = userRidesCollection.map(doc => doc.id);
    /* v8 ignore next 6 */
    if (ridesList.length === 0) {
      console.warn(
        `RideRepo.findAllByUser: userId:${userId}, ridesList is empty`,
      );
      return [];
    }
    // Fetch rides which are subscribed to by the user & have not ended
    // Do not throw error if ride is not found, simply log it and continue
    const rides: Ride[] = [];
    for (const rideId of ridesList) {
      const ride = await RideRepo.find(rideId);
      if (ride) {
        rides.push(ride);
      }
    }
    return rides;
  }

  static async find(rideId: string): Promise<Ride> {
    // Fetch ride from firestore
    const rideDoc = await getDocument(`rides/${rideId}`);
    if (!rideDoc) {
      throw new MyError(
        "warn",
        "RideRepo.find",
        "RIDE_NOT_FOUND",
        StatusCodes.NOT_FOUND,
        new Error("Ride not found"),
        { rideId },
      );
    }
    // Convert timestamps to dates
    const rideData = convertTimestampsToDates(rideDoc as DocumentData);
    // Check if ride has ended
    if (new Date(rideData.endDate) <= new Date()) {
      throw new MyError(
        "warn",
        "RideRepo.find",
        "RIDE_ENDED",
        StatusCodes.GONE,
        new Error("Ride has ended"),
        { endDate: rideData.endDate, rideId },
      );
    }
    // Add empty breakpointsTo if not present
    /* v8 ignore next 3 */
    if (!rideData.breakpointsTo) {
      rideData.breakpointsTo = [];
    }
    // Add empty breakpointsFrom if not present
    if (!rideData.breakpointsFrom) {
      rideData.breakpointsFrom = [];
    }
    // Fetch participants
    const participantsCollection = await getCollection(
      `rides/${rideId}/participants`,
    );
    // Check for participants and add them to ride data
    if (participantsCollection && participantsCollection.length > 0) {
      rideData.participants = participantsCollection.map(doc =>
        convertTimestampsToDates(doc as DocumentData),
      );
    }
    else {
      rideData.participants = [];
    }
    // Parse ride data
    const parsedRide = RideSchema.safeParse(rideData);
    /* v8 ignore next 10 */
    if (!parsedRide.success) {
      throw new MyError(
        "error",
        "RideRepo.find.parse",
        "FAILED_TO_PARSE_RIDE",
        StatusCodes.INTERNAL_SERVER_ERROR,
        parsedRide.error,
        { rideId },
      );
    }
    return parsedRide.data;
  }

  private static async save(ride: Ride): Promise<void> {
    // Save ride to firestore
    await setDocument(`rides/${ride.id}`, convertDatesToTimestamps(ride));
  }

  static async create(userId: string, ride: RideRequest): Promise<Ride> {
    // Generate Ride Id
    const rideId = generateId(21);
    // Fetch ride from firestore
    const rideDoc = await getDocument(`rides/${rideId}`);
    /* v8 ignore next 10 */
    if (rideDoc) {
      throw new MyError(
        "warn",
        "RideRepo.create",
        "DUPLICATE_RIDE_ID",
        StatusCodes.INTERNAL_SERVER_ERROR,
        new Error("Ride already exists"),
        { rideId },
      );
    }
    // Ride exists, validate user is admin or ride owner
    const rideCreatorId: string = userId;
    const rideAdminIds: string[] = [userId];
    const rideToSave: Ride = {
      ...ride,
      adminIds: rideAdminIds,
      createdAt: new Date().toISOString(),
      creatorId: rideCreatorId,
      id: rideId,
      updatedAt: new Date().toISOString(),
    };
    this.save(rideToSave);
    return rideToSave;
  }

  static async update(
    userId: string,
    rideId: string,
    ride: RideRequest,
  ): Promise<void> {
    // Ride saving is tricky, first we need to validate
    // whether the ride is owned by the user or user is an admin

    // Fetch ride from firestore
    const rideDoc = await getDocument(`rides/${rideId}`);
    /* v8 ignore next 10 */
    if (!rideDoc) {
      throw new MyError(
        "warn",
        "RideRepo.update",
        "RIDE_NOT_FOUND",
        StatusCodes.NOT_FOUND,
        new Error("Ride not found"),
        { rideId },
      );
    }
    // validate user is creator & admin
    const rideCreatorId: string | undefined = rideDoc.creatorId;
    const rideAdminIds: string[] | undefined = rideDoc.adminIds;
    /* v8 ignore next 10 */
    if (!rideCreatorId || !rideAdminIds) {
      throw new MyError(
        "warn",
        "RideRepo.update",
        "UNEXPECTED_ERROR",
        StatusCodes.INTERNAL_SERVER_ERROR,
        new Error("Ride missing properties creatorId or adminIds"),
        { rideId },
      );
    }
    /* v8 ignore next 10 */
    if (rideCreatorId !== userId) {
      throw new MyError(
        "warn",
        "RideRepo.update",
        "UNAUTHORIZED",
        StatusCodes.UNAUTHORIZED,
        new Error("User is not the creator of the ride"),
        { rideCreatorId, userId },
      );
    }
    // if (!rideAdminIds.includes(userId)) {
    //   throw new MyError(
    //     "warn",
    //     "RideRepo.update",
    //     "UNAUTHORIZED",
    //     StatusCodes.UNAUTHORIZED,
    //     new Error("User is not the admin of the ride"),
    //     { rideAdminIds, userId },
    //   );
    // }
    // Create a new ride object with updated properties
    const rideToSave: Ride = {
      ...ride,
      adminIds: rideAdminIds,
      createdAt: convertTimestampsToDates<string>(rideDoc.createdAt),
      creatorId: rideCreatorId,
      id: rideId,
      updatedAt: new Date().toISOString(),
    };
    this.save(rideToSave);
  }

  static async rsvpRide(
    rideId: string,
    participant: Participant,
  ): Promise<void> {
    // Validate ride exists & has not ended
    const ride = await RideRepo.find(rideId);
    // Validate joiningLocationId is in ride
    if (
      ride.startLocation.id !== participant.joiningLocationId
      && !ride.breakpointsTo.some(
        waypoint => waypoint.id === participant.joiningLocationId,
      )
    ) {
      throw new MyError(
        "warn",
        "RideRepo.rsvpRide",
        "INVALID_JOINING_LOCATION",
        StatusCodes.BAD_REQUEST,
        new Error("Invalid joining location"),
        {
          joiningLocationId: participant.joiningLocationId,
          rideId,
          userId: participant.id,
        },
      );
    }
    // Add ride to user's rides collection
    await setDocument(`users/${participant.id}/rides/${rideId}`, {
      id: rideId,
      updatedAt: FieldValue.serverTimestamp(),
    });
    // Add participant to ride
    await setDocument(`rides/${rideId}/participants/${participant.id}`, {
      displayName: participant.displayName,
      id: participant.id,
      joiningLocationId: participant.joiningLocationId,
      photoUrl: participant.photoUrl,
      status: participant.status,
      updatedAt: FieldValue.serverTimestamp(),
    });
  }

  static async shareLocation(
    userId: string,
    rideId: string,
    location: ShareLocationRequest,
  ): Promise<RiderLocation[]> {
    // Store shared location to ride's participants collection
    await updateDocument(`rides/${rideId}/participants/${userId}`, {
      location: {
        lat: location.lat,
        lng: location.lng,
        updatedAt: FieldValue.serverTimestamp(),
      },
    });
    // Fetch other participant's location
    const ridersCol = await getCollection(
      `rides/${rideId}/participants`,
      query => query.where(FieldPath.documentId(), "!=", userId),
    );
    const ridersDoc = ridersCol.map(doc =>
      convertTimestampsToDates(doc as DocumentData),
    );
    // Parse participants, at the same time filter out the self
    // & other participants who have not shared their location
    const riders: RiderLocation[] = [];
    for (const riderDoc of ridersDoc) {
      // Skip participants who have not shared their location
      /* v8 ignore next 3 */
      if (!riderDoc.location) {
        continue;
      }
      // Parse participant
      const response = {
        id: riderDoc.id,
        lat: riderDoc.location.lat,
        lng: riderDoc.location.lng,
        updatedAt: riderDoc.location.updatedAt,
      };
      riders.push(RiderLocationSchema.parse(response));
    }
    return riders;
  }
}
