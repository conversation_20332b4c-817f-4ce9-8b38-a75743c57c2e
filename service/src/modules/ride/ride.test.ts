import type { User } from "@/_testUtils/auth";
import { EmptyTestResponse, testAppInstance } from "@/_testUtils/app";
import { AuthClient } from "@/_testUtils/auth";
import { Cleanup } from "@/_testUtils/cleanup";
import { sleep } from "@/_testUtils/common";
import * as createRide from "@/_testUtils/data/createRide.json";
import * as updateRide from "@/_testUtils/data/updateRide.json";
import { RideSchema } from "@/modules/ride/ride.schema";
import { afterEach, beforeAll, beforeEach, describe, expect, test } from "vitest";

describe("/ride", () => {
  const app = testAppInstance;
  let testResponse = EmptyTestResponse;
  const authClient = new AuthClient();
  let user: User | null = null;
  let newRideId: string | undefined;

  beforeAll(async () => {
    app.setAuthToken(await authClient.getToken());
    user = authClient.getUser();
    if (user) {
      await Cleanup.deleteUser(user.uid);
    }
  });

  afterEach((context) => {
    context.onTestFailed(() => {
      console.debug("API Response for failed test:", JSON.stringify(testResponse, null, 2));
    });
  });

  beforeEach(() => {
    testResponse = EmptyTestResponse;
  });

  test("createRide", async () => {
    const response = await app.inject({
      auth: true,
      body: createRide,
      method: "POST",
      path: `/ride`,
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(200);
    expect(testResponse.success).toBe(true);
    const rideData = (testResponse.data as Record<string, unknown>).ride as Record<string, unknown>;
    expect(rideData).toBeDefined();
    const result = RideSchema.safeParse(rideData);
    if (!result.success) {
      console.debug("Validation errors:", result.error);
    }
    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    newRideId = rideData.id as string;
    expect(newRideId).toBeDefined();
    expect(typeof newRideId).toBe("string");
    expect(newRideId.length > 10).toBe(true);
  });

  test("updateRide", async () => {
    await sleep(1000);
    const response = await app.inject({
      auth: true,
      body: updateRide,
      method: "POST",
      path: `/ride/${newRideId}`,
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(200);
    expect(testResponse.success).toBe(true);
  });

  test("getRides (invalid user id)", async () => {
    const response = await app.inject({
      auth: true,
      method: "GET",
      path: `/rides/123`,
    });
    testResponse = EmptyTestResponse;
    expect(response.statusCode).toBe(401);
  });

  test("getRides (no rides found)", async () => {
    const response = await app.inject({
      auth: true,
      method: "GET",
      path: `/ride/${user?.uid}`,
    });
    testResponse = EmptyTestResponse;
    expect(response.statusCode).toBe(404);
  });

  test("rsvp (valid: 1)", async () => {
    const response = await app.inject({
      auth: true,
      body: {
        displayName: user?.displayName,
        id: `${user?.uid}`,
        joiningLocationId: "waypoint-1",
        photoUrl: user?.photoURL,
        status: "yes",
      },
      method: "POST",
      path: "ride/Fswr3pXUaj29unaSZbDue/rsvp",
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(200);
    expect(testResponse.success).toBe(true);
  });

  test("rsvp (valid: 2)", async () => {
    const response = await app.inject({
      auth: true,
      body: {
        displayName: user?.displayName,
        id: `${user?.uid}`,
        joiningLocationId: "waypoint-2",
        photoUrl: user?.photoURL,
        status: "yes",
      },
      method: "POST",
      path: "ride/LkqJ87h4g5f2vBn9mD0c/rsvp",
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(200);
    expect(testResponse.success).toBe(true);
  });

  test("rsvp (invalid participant)", async () => {
    const response = await app.inject({
      auth: true,
      body: {
        displayName: user?.displayName,
        id: "invalid-participant-id",
        joiningLocationId: "invalid-waypoint",
        photoUrl: user?.photoURL,
        status: "yes",
      },
      method: "POST",
      path: "ride/LkqJ87h4g5f2vBn9mD0c/rsvp",
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(401);
  });

  test("rsvp (invalid location)", async () => {
    const response = await app.inject({
      auth: true,
      body: {
        displayName: user?.displayName,
        id: `${user?.uid}`,
        joiningLocationId: "invalid-waypoint",
        photoUrl: user?.photoURL,
        status: "yes",
      },
      method: "POST",
      path: "ride/LkqJ87h4g5f2vBn9mD0c/rsvp",
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(400);
    expect(testResponse.code).toBe("INVALID_JOINING_LOCATION");
  });

  test("rsvp (ride not found)", async () => {
    const response = await app.inject({
      auth: true,
      body: {
        displayName: user?.displayName,
        id: `${user?.uid}`,
        joiningLocationId: "invalid-waypoint",
        photoUrl: user?.photoURL,
        status: "yes",
      },
      method: "POST",
      path: "ride/fake-ride-id/rsvp",
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(404);
    expect(testResponse.code).toBe("RIDE_NOT_FOUND");
  });

  test("rsvp (ride ended)", async () => {
    const response = await app.inject({
      auth: true,
      body: {
        displayName: user?.displayName,
        id: `${user?.uid}`,
        joiningLocationId: "invalid-waypoint",
        photoUrl: user?.photoURL,
        status: "yes",
      },
      method: "POST",
      path: "ride/e8f9g0h1i2j3k4l5m6n7/rsvp",
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(410);
    expect(testResponse.code).toBe("RIDE_ENDED");
  });

  test("getRides", async () => {
    const response = await app.inject({
      auth: true,
      method: "GET",
      path: `/rides/${user?.uid}`,
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(200);
    expect(testResponse.rides).toBeDefined();
    expect(testResponse.rides).toHaveLength(2);
  });

  test("getRide (not found)", async () => {
    const response = await app.inject({
      auth: true,
      method: "GET",
      path: "/ride/fake-ride-id",
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(404);
    expect(testResponse.code).toBe("RIDE_NOT_FOUND");
  });

  test("getRide (ride ended)", async () => {
    const response = await app.inject({
      auth: true,
      method: "GET",
      path: "/ride/e8f9g0h1i2j3k4l5m6n7",
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(410);
    expect(testResponse.code).toBe("RIDE_ENDED");
  });

  test("getRide", async () => {
    const response = await app.inject({
      auth: true,
      method: "GET",
      path: "/ride/d6e7f8g9h0i1j2k3l4m5",
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(200);
    expect(testResponse.id).toBeDefined();
    expect(testResponse.id).toBe("d6e7f8g9h0i1j2k3l4m5");
  });

  test("share location", async () => {
    const response = await app.inject({
      auth: true,
      body: {
        lat: 12.81004,
        lng: 77.65002,
      },
      method: "POST",
      path: "/ride/Fswr3pXUaj29unaSZbDue/location",
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(200);
    expect(testResponse.riders).toBeDefined();
  });

  test("share location (unauthorized)", async () => {
    const response = await app.inject({
      auth: false,
      body: {
        lat: 12.81004,
        lng: 77.65002,
      },
      method: "POST",
      path: "/ride/Fswr3pXUaj29unaSZbDue/location",
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(401);
  });
});
