import { z } from "zod";

const DirectionEnumSchema = z.enum(["TO", "FROM"]);

// Basic reusable schemas
const LatLngSchema = z.object({
  latitude: z.number(),
  longitude: z.number(),
});

const RoadFeatureStateEnum = z.enum([
  "ROAD_FEATURE_STATE_UNSPECIFIED",
  "EXISTS",
  "DOES_NOT_EXIST",
]);

const viewportSchema = z.object({
  high: LatLngSchema,
  low: LatLngSchema,
});

const PolylinePointIndexSchema = z.object({
  endIndex: z.number(),
  startIndex: z.number(),
});

const LocationSchema = z.object({
  latLng: LatLngSchema,
});

const PolylineSchema = z.object({
  encodedPolyline: z.string(),
});

const LocalizedValueSchema = z.object({
  languageCode: z.string().optional(),
  text: z.string(),
});

const NavigationInstructionSchema = z.object({
  instructions: z.string(),
  maneuver: z.string(),
});

const FlyoverInfoSchema = z.object({
  flyoverPresence: RoadFeatureStateEnum.default(
    "ROAD_FEATURE_STATE_UNSPECIFIED",
  ),
  polylinePointIndex: PolylinePointIndexSchema.optional(),
});

const NarrowRoadInfoSchema = z.object({
  narrowRoadPresence: RoadFeatureStateEnum.default(
    "ROAD_FEATURE_STATE_UNSPECIFIED",
  ),
  polylinePointIndex: PolylinePointIndexSchema.optional(),
});

const PolylineDetailsSchema = z.object({
  flyoverInfo: z.array(FlyoverInfoSchema).optional(),
  narrowRoadInfo: z.array(NarrowRoadInfoSchema).optional(),
});

// Step schema
const StepSchema = z.object({
  distanceMeters: z.number(),
  endLocation: LocationSchema,
  localizedValues: z.object({
    distance: LocalizedValueSchema,
    staticDuration: LocalizedValueSchema,
  }),
  navigationInstruction: NavigationInstructionSchema,
  polyline: PolylineSchema,
  startLocation: LocationSchema,
  staticDuration: z.string(),
  travelMode: z.string(),
});

// Leg schema
const LegSchema = z.object({
  distanceMeters: z.number(),
  endLocation: LocationSchema,
  localizedValues: z.object({
    distance: LocalizedValueSchema,
    staticDuration: LocalizedValueSchema,
  }),
  polyline: PolylineSchema,
  startLocation: LocationSchema,
  staticDuration: z.string(),
  steps: z.array(StepSchema),
});

// Route schema
export const RouteSchema = z.object({
  description: z.string().optional(),
  distanceMeters: z.number(),
  legs: z.array(LegSchema),
  localizedValues: z.object({
    distance: LocalizedValueSchema,
    staticDuration: LocalizedValueSchema,
  }),
  polyline: PolylineSchema,
  polylineDetails: PolylineDetailsSchema.optional(),
  routeLabels: z.array(z.string()),
  staticDuration: z.string(),
  viewport: viewportSchema,
  warnings: z.array(z.string()).optional(),
});

export const RouteResponseSchema = z.object({
  route: RouteSchema,
});

export type Route = z.infer<typeof RouteSchema>;
export type RouteResponse = z.infer<typeof RouteResponseSchema>;
export type DirectionEnum = z.infer<typeof DirectionEnumSchema>;
