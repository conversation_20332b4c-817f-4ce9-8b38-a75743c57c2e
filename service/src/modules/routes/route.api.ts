import type { DirectionEnum, RouteResponse } from "@/modules/routes/route.schema";

import type { FastifyRequest } from "fastify";

import { RouteService } from "@/modules/routes/route.service";

export async function getRoute(req: FastifyRequest): Promise<RouteResponse> {
  const { direction, rideId } = req.params as {
    rideId: string;
    direction: DirectionEnum;
  };
  const route = await RouteService.getRoute(rideId, direction);
  return { route };
}
