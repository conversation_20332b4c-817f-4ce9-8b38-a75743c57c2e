import type { FastifyInstance } from "fastify";
import type { ZodTypeProvider } from "fastify-type-provider-zod";

import { RouteParamsSchema } from "@/_shared/common.schema";
import { StatusCodes } from "@/_shared/error";
import { getRoute } from "@/modules/routes/route.api";
import { RouteResponseSchema } from "@/modules/routes/route.schema";

export function setupRouteRoutes(app: FastifyInstance) {
  // Get route for a ride
  app.withTypeProvider<ZodTypeProvider>().get("/route/:rideId/:direction", {
    handler: getRoute,
    schema: {
      params: RouteParamsSchema,
      response: {
        [StatusCodes.OK]: RouteResponseSchema,
      },
    },
  });
}
