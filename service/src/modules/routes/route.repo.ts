import type {
  DirectionEnum,
  Route,
} from "@/modules/routes/route.schema";

import { MyError, StatusCodes } from "@/_shared/error";
import { getDocument } from "@/_shared/firebase";
import {
  RouteSchema,
} from "@/modules/routes/route.schema";

export class RouteRepo {
  static async getRoute(
    rideId: string,
    direction: DirectionEnum,
  ): Promise<Route> {
    const routeDoc = await getDocument(`rides/${rideId}/routes/${direction}-0`);
    if (!routeDoc) {
      throw new MyError(
        "warn",
        "RouteRepo.getRoute",
        "ROUTE_NOT_FOUND",
        StatusCodes.NOT_FOUND,
        new Error("Route not found"),
        { direction, rideId },
      );
    }
    const parsedRoute = RouteSchema.safeParse(routeDoc);
    /* v8 ignore next 10 */
    if (!parsedRoute.success) {
      throw new MyError(
        "error",
        "RouteRepo.getRoute",
        "INVALID_ROUTE",
        StatusCodes.INTERNAL_SERVER_ERROR,
        parsedRoute.error,
        { direction, rideId },
      );
    }
    return parsedRoute.data;
  }
}
