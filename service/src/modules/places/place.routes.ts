import type { FastifyInstance } from "fastify";
import type { ZodTypeProvider } from "fastify-type-provider-zod";

import { searchPlaces } from "@/modules/places/place.api";
import {
  PlacesSearchQuerySchema,
  PlacesSearchResponseSchema,
} from "@/modules/places/place.schema";

export function setupPlaceRoutes(app: FastifyInstance) {
  // Search for places using Google Places API (Autocomplete)
  app.withTypeProvider<ZodTypeProvider>().get("/places/search", {
    handler: searchPlaces,
    schema: {
      querystring: PlacesSearchQuerySchema,
      response: {
        200: PlacesSearchResponseSchema,
      },
    },
  });
}
