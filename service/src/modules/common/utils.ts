import type { FastifyRequest } from "fastify";
import type { UserRecord } from "firebase-admin/auth";

import { MyError, StatusCodes } from "@/_shared/error";

export function isAuthenticated(req: FastifyRequest, userId?: string): UserRecord {
  if (req.user === undefined || (userId && req.user?.uid !== userId)) {
    throw new MyError(
      "warn",
      "utils.isAuthenticated",
      "UNAUTHORIZED",
      StatusCodes.UNAUTHORIZED,
      new Error("Unauthorized"),
      { url: req.url, userId, userRecord: req.user },
    );
  }
  return req.user;
}
