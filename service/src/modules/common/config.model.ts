import type { PlatformName } from "@/_shared/common.schema";

export function getClientConfig(platform: PlatformName) {
  /* v8 ignore next 15 */
  return {
    appSHA1:
      platform === "android"
        ? "6e:cc:1f:ac:0e:59:0d:84:b9:d1:9b:b5:24:f5:8a:f1:01:06:f9:15"
        : null,
    revenueCatAPIKey:
      platform === "android"
        ? "goog_ABjPsfHKwHJwgtQqNTGxuOSiHwz"
        : "appl_wXUElUbdQkEVMNfTLgcepfHqzYr",
    sendCrashlyticsData: true,
    urlAboutUs: "https://95octane.app/static/about-us",
    urlPrivacy: "https://95octane.app/static/privacy-policy",
    urlTerms: "https://95octane.app/static/tnc",
  };
}
