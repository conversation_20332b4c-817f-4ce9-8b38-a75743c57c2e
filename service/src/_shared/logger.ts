import type { LoggerOptions } from "pino";

import { config } from "@/_shared/config";

import pino from "pino";

export const loggerConfig: LoggerOptions = {
  formatters: {
    level: (label) => {
      return {
        level: label,
      };
    },
  },
  level: config.logLevel,
  transport:
    config.isEnvDevelopment || config.isEnvTest
      ? {
          options: {
            colorize: !!(config.isEnvDevelopment || config.isEnvTest),
            colorizeObjects: !!(config.isEnvDevelopment || config.isEnvTest),
            ignore: config.isEnvDevelopment || config.isEnvTest ? "pid,hostname" : undefined,
            levelFirst: true,
            singleLine: true,
            translateTime: "SYS:hh:MM:ss tt",
          },
          target: "pino-pretty",
        }
      : undefined,
};

export const logger = pino(loggerConfig);
// Redirect all console methods to use Fastify's logger
// console.log = (...args) => logger.info(args.join(' '));
console.error = (...args) => logger.error(args.join(" "));
console.warn = (...args) => logger.warn(args.join(" "));
console.info = (...args) => logger.info(args.join(" "));
console.debug = (...args) => logger.debug(args.join(" "));
