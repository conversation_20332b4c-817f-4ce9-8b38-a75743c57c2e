import { z } from "zod";

// Schema for Location object
export const LocationSchema = z.object({
  lat: z.number().describe("Latitude"),
  lng: z.number().describe("Longitude"),
});

// Schema for userId parameter
export const UserIdParamSchema = z.object({
  userId: z.string(),
});
// Schema for rideId parameter
export const RideIdParamSchema = z.object({
  rideId: z.string(),
});
// Schema for route parameter
export const RouteParamsSchema = z.object({
  direction: z.enum(["to", "from"]),
  rideId: z.string(),
});

// Schema for empty body
export const EmptyBodySchema = z.object({});
// Schema for Success Response
export const SuccessResponseSchema = z.object({
  success: z.literal(true),
});
// Schema for Failure Response
export const FailureResponseSchema = z.object({
  error: z.object({
    code: z.string(),
    message: z.string(),
  }),
  success: z.literal(false),
});

export const ErrorResponseSchema = z.object({
  code: z.string(),
  message: z.string().optional(),
  stack: z.string().optional(),
  status: z.number(),
});

export const DateTimeSchema = z.string().datetime({ offset: true });
// export const DateTimeSchema = z
//   .union([z.date(), z.string()])
//   .refine(
//     (val) => {
//       if (val instanceof Date) {
//         return true;
//       }
//       // Try to parse the date string
//       const date = new Date(val);
//       return !Number.isNaN(date.getTime());
//     },
//     {
//       message: "Invalid datetime format. Expected ISO 8601 or RFC 2822 format",
//     },
//   )
//   .transform(val => new Date(val).toISOString());

const PlatformNameSchema = z.enum([
  "android",
  "iOS",
  "iPadOS",
]);

const PlatformTypeSchema = z.enum(["mobile", "tablet", "web"]);

export const HeadersSchema = z.object({
  "accept": z.literal("application/json"),
  "authorization": z.string().optional(),
  "content-type": z.string().startsWith("application/json"),
  "x-app-check-token": z.string().optional(),
  "x-app-version": z.coerce.number().int().min(1),
  "x-platform-name": PlatformNameSchema,
  "x-platform-type": PlatformTypeSchema,
  "x-trace-id": z.string().min(10).max(100),
});

export type SuccessResponse = z.infer<typeof SuccessResponseSchema>;
export type FailureResponse = z.infer<typeof FailureResponseSchema>;
export type ErrorResponse = z.infer<typeof ErrorResponseSchema>;
export type PlatformName = z.infer<typeof PlatformNameSchema>;
export type PlatformType = z.infer<typeof PlatformTypeSchema>;
