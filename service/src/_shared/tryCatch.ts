// Types for the result object with discriminated union
type Success<T> = {
  result: T;
  error: null;
};

type Failure<E> = {
  result: null;
  error: E;
};

type Result<T, E = Error> = Success<T> | Failure<E>;

// tryCatch function (async)
export async function tryCatch<T, E = Error>(
  promise: Promise<T>,
): Promise<Result<T, E>> {
  try {
    const result = await promise;
    return { error: null, result };
  }
  catch (error) {
    console.error(`tryCatch: error occurred: ${JSON.stringify(error)} !!!`);
    return { error: error as E, result: null };
  }
}

// tryCatch function (sync)
export function tryCatchSync<T, E = Error>(fn: () => T): Result<T, E> {
  try {
    const result = fn();
    return { error: null, result };
  }
  catch (error) {
    return { error: error as E, result: null };
  }
}
