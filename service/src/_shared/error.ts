import type { FastifyError } from "fastify";

import { StatusCodes } from "http-status-codes";
import { ZodError } from "zod";
import { fromError } from "zod-validation-error";

export { StatusCodes };

export class MyError implements FastifyError {
  name: string;
  code: string;
  statusCode: number;
  ctx: Record<string, unknown>;
  message: string;
  errorType: string;
  stack?: string;
  cause?: Error;

  constructor(
    errorType: "info" | "warn" | "error",
    name: string,
    code: string,
    statusCode: StatusCodes,
    error: unknown,
    ctx: Record<string, unknown>,
  ) {
    this.name = name;
    this.code = code;
    this.statusCode = statusCode;
    this.message = this.errorToMessage(error);
    this.ctx = ctx;
    this.errorType = errorType;

    // Capture the stack trace
    Error.captureStackTrace(this, this.constructor);

    // If the original error has a stack trace, store it as the cause
    if (error instanceof Error) {
      this.cause = error;

      // If no stack was captured (rare), use the original error's stack
      if (!this.stack && error.stack) {
        this.stack = error.stack;
      }
    }
  }

  private errorToMessage(error: unknown): string {
    if (error instanceof Error) {
      return error.message;
    }

    if (error instanceof ZodError) {
      return fromError(error).toString();
    }

    if (typeof error === "string") {
      return error;
    }

    return JSON.stringify(error);
  }

  /**
   * Create a MyError from an existing Error, preserving its stack trace
   */
  static fromError(
    error: unknown,
    errorType: "info" | "warn" | "error" = "error",
    name: string = "ApplicationError",
    code: string = "INTERNAL_ERROR",
    statusCode: StatusCodes = StatusCodes.INTERNAL_SERVER_ERROR,
    ctx: Record<string, unknown> = {},
  ): MyError {
    const myError = new MyError(errorType, name, code, statusCode, error, ctx);

    // For better stack trace preservation when wrapping errors
    if (error instanceof Error && error.stack) {
      const originalStack = error.stack;
      const currentStack = myError.stack?.split("\n").slice(1).join("\n") || "";

      myError.stack = `${myError.name}: ${myError.message}\n${currentStack}\nCaused by: ${originalStack}`;
    }

    return myError;
  }

  toJSON() {
    return {
      cause: this.cause instanceof Error ? this.cause.stack : this.cause,
      code: this.code,
      ctx: this.ctx,
      errorType: this.errorType,
      message: this.message,
      name: this.name,
      stack: this.stack,
      statusCode: this.statusCode,
    };
  }

  toString() {
    return `[${this.statusCode}, ${this.code}], ${this.stack?.toString()}`;
  }
}
