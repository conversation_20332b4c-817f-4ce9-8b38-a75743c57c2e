import { z } from "zod";

export const ConfigSchema = z.object({
  env: z.enum(["development", "production", "test"]).default("production"),
  googleApplicationCredentials: z.string().optional(),
  host: z.string().default("0.0.0.0"),
  isEnvDevelopment: z.boolean().default(false),
  isEnvTest: z.boolean().default(false),
  logLevel: z.enum(["silent", "error", "warn", "info", "debug"]).default("warn"),
  minAppVersion: z.number().int().min(1).default(1),
  port: z.number().default(8888),
  useEmulator: z.boolean().default(false),
}).strict();
