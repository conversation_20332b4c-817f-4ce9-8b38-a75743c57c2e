FROM chainguard/node:latest AS base

ARG RELEASE_TYPE
USER root
WORKDIR /app
ENV PNPM_HOME=/app/pnpm
ENV PATH="${PNPM_HOME}:${PATH}"

RUN npm install --global pnpm@latest

COPY package.json pnpm-lock.yaml* pnpm-workspace.yaml* ./
COPY service/package.json ./service/

FROM base AS build-deps

RUN pnpm install --frozen-lockfile

COPY ./service ./service/
RUN cd service && pnpm run build:${RELEASE_TYPE}

FROM base AS runtime-deps
ARG RELEASE_TYPE

RUN pnpm install --prod --frozen-lockfile
USER node

WORKDIR /app

# Copy the build artifacts
COPY --from=build-deps /app/service/dist /app/service/dist

ENV HOST=0.0.0.0
ENV PORT=8888
ENV NODE_ENV=${RELEASE_TYPE}

EXPOSE 8888
CMD ["--enable-source-maps", "./service/dist/app.js"]
