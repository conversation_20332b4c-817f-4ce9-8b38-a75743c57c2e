import path from "node:path";
import { env } from "node:process";
import { fileURLToPath } from "node:url";
import TerserPlugin from "terser-webpack-plugin";
import TsconfigPathsPlugin from "tsconfig-paths-webpack-plugin";
import webpack from "webpack";
import nodeExternals from "webpack-node-externals";

const { IgnorePlugin } = webpack;
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const isDevelopment = env.NODE_ENV === "development";
const isProduction = env.NODE_ENV === "production";

/** @type {import('webpack').Configuration} */
export default {
  // Persistent caching for faster rebuilds
  cache: {
    type: "filesystem",
    cacheDirectory: path.resolve(__dirname, ".webpack-cache"),
    buildDependencies: {
      config: [__filename],
    },
  },
  devtool: isDevelopment ? "eval-cheap-module-source-map" : "source-map",
  entry: "./src/main.ts",
  experiments: {
    outputModule: true,
  },
  externals: [
    nodeExternals({
      importType: "module",
    }),
  ],
  infrastructureLogging: {
    level: isDevelopment ? "info" : "warn",
  },
  mode: env.NODE_ENV || "production",
  module: {
    rules: [
      {
        exclude: /node_modules/,
        test: /\.ts$/,
        use: [
          {
            loader: "ts-loader",
            options: {
              configFile: path.resolve(__dirname, "tsconfig.json"),
              transpileOnly: isDevelopment,
              // Enable faster incremental builds
              experimentalWatchApi: true,
              // Use project references for better performance
              projectReferences: true,
              // Faster compilation in development
              compilerOptions: isDevelopment ? {
                incremental: true,
                tsBuildInfoFile: path.resolve(__dirname, ".tsbuildinfo"),
              } : undefined,
            },
          },
        ],
      },
    ],
  },
  optimization: {
    minimize: !isDevelopment,
    minimizer: [
      new TerserPlugin({
        extractComments: false,
        parallel: true, // Enable parallel processing
        terserOptions: {
          format: {
            comments: false,
          },
          module: true,
          compress: {
            drop_console: isProduction, // Remove console logs in production
          },
        },
      }),
    ],
    // Better chunk splitting for caching
    splitChunks: isDevelopment ? false : {
      chunks: "all",
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: "vendors",
          chunks: "all",
        },
      },
    },
  },
  output: {
    chunkFormat: "module",
    clean: true,
    environment: {
      module: true,
    },
    filename: "app.js",
    library: {
      type: "module",
    },
    module: true,
    path: path.resolve(__dirname, "dist"),
  },
  plugins: [
    new IgnorePlugin({
      resourceRegExp: /\.(test|spec)\.ts$|vitest\.config\.ts$/,
    }),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"),
    },
    extensions: [".ts", ".js", ".json"],
    plugins: [
      new TsconfigPathsPlugin({
        configFile: "./tsconfig.json",
      }),
    ],
  },
  stats: {
    children: false,
    chunkModules: false,
    chunks: false,
    colors: true,
    modules: false,
  },
  target: "node",
  watchOptions: {
    aggregateTimeout: 200, // Reduced for faster rebuilds
    ignored: /node_modules/,
    poll: isDevelopment ? false : 1000, // Use native watching in development
  },
  // Performance monitoring
  performance: {
    hints: isDevelopment ? false : "warning",
    maxEntrypointSize: 512000,
    maxAssetSize: 512000,
  },
};
