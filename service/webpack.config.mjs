import path from "node:path";
import { env } from "node:process";
import { fileURLToPath } from "node:url";
import TerserPlugin from "terser-webpack-plugin";
import TsconfigPathsPlugin from "tsconfig-paths-webpack-plugin";
import webpack from "webpack";
import nodeExternals from "webpack-node-externals";

const { IgnorePlugin } = webpack;
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const isDevelopment = env.NODE_ENV === "development";

/** @type {import('webpack').Configuration} */
export default {
  devtool: "source-map",
  entry: "./src/main.ts",
  experiments: {
    outputModule: true,
  },
  externals: [
    nodeExternals({
      importType: "module",
    }),
  ],
  infrastructureLogging: {
    level: isDevelopment ? "info" : "warn",
  },
  mode: env.NODE_ENV || "production",
  module: {
    rules: [
      {
        exclude: /node_modules/,
        test: /\.ts$/,
        use: [
          {
            loader: "ts-loader",
            options: {
              configFile: path.resolve(__dirname, "tsconfig.json"),
              transpileOnly: isDevelopment,
            },
          },
        ],
      },
    ],
  },
  optimization: {
    minimize: !isDevelopment,
    minimizer: [
      new TerserPlugin({
        extractComments: false,
        terserOptions: {
          format: {
            comments: false,
          },
          module: true,
        },
      }),
    ],
  },
  output: {
    chunkFormat: "module",
    clean: true,
    environment: {
      module: true,
    },
    filename: "app.js",
    library: {
      type: "module",
    },
    module: true,
    path: path.resolve(__dirname, "dist"),
  },
  plugins: [
    new IgnorePlugin({
      resourceRegExp: /\.(test|spec)\.ts$|vitest\.config\.ts$/,
    }),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"),
    },
    extensions: [".ts", ".js", ".json"],
    plugins: [
      new TsconfigPathsPlugin({
        configFile: "./tsconfig.json",
      }),
    ],
  },
  stats: {
    children: false,
    chunkModules: false,
    chunks: false,
    colors: true,
    modules: false,
  },
  target: "node",
  watchOptions: {
    aggregateTimeout: 300,
    ignored: /node_modules/,
    poll: 1000,
  },
};
