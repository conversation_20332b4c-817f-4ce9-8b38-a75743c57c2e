{
  "compilerOptions": {
    "incremental": true, // Faster subsequent builds
    "composite": false,
    "target": "ES2020",
    "baseUrl": ".",
    "rootDir": ".",
    "module": "ES2020",
    "moduleResolution": "Bundler",
    "paths": {
      "@/*": ["src/*"]
    },
    "resolveJsonModule": true,
    "strict": true,
    "noImplicitOverride": true, // Better OOP support
    "noUncheckedIndexedAccess": true, // Additional type safety
    "declaration": false,
    "declarationMap": false,
    "outDir": "./dist",
    "sourceMap": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    "skipLibCheck": true
  },
  "include": ["**/*.ts", "**/*.json"],
  "exclude": ["node_modules", "dist"]
}
