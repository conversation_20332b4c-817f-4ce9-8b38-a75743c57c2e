/* eslint-disable import/no-unresolved */
import { defineConfig } from "vitest/config";

export default defineConfig({
  clearScreen: true,
  logLevel: "info",
  resolve: {
    alias: {
      "@": "/src",
    },
  },
  test: {
    coverage: {
      ignoreEmptyLines: true,
      include: [
        "src/modules/**/*.ts",
      ],
      reporter: ["text", "json"],
    },
    env: {
      LOG_LEVEL: "info",
      NODE_ENV: "test",
    },
    environment: "node",
    include: ["**/*.test.ts"],
    onConsoleLog: (log, type) => {
      if (type === "stderr") {
        console.error(log);
      }
      console.info(log);
    },
    // testTimeout: 10000,
  },
});
