{"name": "apiservice", "version": "0.0.1+8", "description": "API service for 95octane", "type": "module", "main": "dist/service.js", "scripts": {"build:dev": "webpack --node-env development", "build:prod": "webpack --node-env production", "check": "pnpm run lint && pnpm run build:prod", "clean": "rm -rf dist", "deps": "just start-deps", "dev": "pnpm run lint:fix && wait-on --config waitOn.json && tsx watch src/main.ts", "lint": "eslint --color", "lint:fix": "eslint --color --fix", "serve": "pnpm run deps && pnpm run dev", "simulate:prod": "pnpm run deps && pnpm run build:prod && wait-on --config waitOn.json && NODE_ENV=development node dist/service.js", "test": "wait-on --config waitOn.json && vitest", "test:coverage": "wait-on --config waitOn.json && vitest run --coverage"}, "dependencies": {"@fastify/cors": "latest", "@fastify/helmet": "latest", "axios": "latest", "fastify": "latest", "fastify-plugin": "latest", "fastify-type-provider-zod": "latest", "firebase-admin": "latest", "http-status-codes": "latest", "nanoid": "latest", "pino": "latest", "zod": "latest", "zod-validation-error": "latest"}, "devDependencies": {"@types/node": "latest", "@vitest/coverage-v8": "latest", "firebase": "latest", "pino-pretty": "latest", "terser-webpack-plugin": "latest", "ts-loader": "latest", "tsconfig-paths-webpack-plugin": "latest", "tsx": "latest", "typescript": "latest", "vitest": "latest", "wait-on": "latest", "webpack": "latest", "webpack-cli": "latest", "webpack-node-externals": "latest"}}