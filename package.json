{"name": "@95octane/backend", "private": true, "scripts": {"lint": "eslint --color --ext .json,.ts,.js .", "lint:fix": "eslint --fix --color --ext .json,.ts,.js .", "serve": "concurrently \"pnpm run service\" \"pnpm run worker\"", "service": "cd service && pnpm run dev", "worker": "cd worker && pnpm run dev"}, "devDependencies": {"@antfu/eslint-config": "latest", "concurrently": "latest", "eslint": "latest", "eslint-plugin-format": "latest"}, "packageManager": "pnpm@10.10.0"}