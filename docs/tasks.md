# Tasks

Tasks are adhoc scripts that can be run to perform specific tasks.

## Here is a list of tasks that can be run

### Import

This task will import all the rides from `testData/rides` & `testData/routes` folders into the database.

There are two options when running the task:

- `just import-local` - This will import the rides & routes to the local database in firebase emulator.
- `just import-prod` - This will import the rides & routes to the production database in firebase.

### Generate Routes

This task will generate the routes for all the rides in the `rides` folder.

Simply run `just generate-routes` to generate the routes for all the rides in the `rides` folder.
