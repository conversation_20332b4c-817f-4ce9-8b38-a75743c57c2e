# Documentation

- [Service (API)](service.md)
- [Tasks](tasks.md)

## Setup

Local setup has few steps:

### Install all dependencies

#### First all tools from homebrew

> NOTES:
>
> - We are using `nvm` to manage node versions.
> - We are using `direnv` to manage environment variables.

```sh
brew install --formulae just nvm direnv
brew install --cask google-cloud-sdk
```

#### Then install node

```sh
nvm install "node" -b
nvm install-latest-npm
```

#### Then install all the tools from npm

```sh
npm install --global firebase-tools@latest prettier@latest
```

#### Then install pre-commit

```sh
pipx install pre-commit
```

#### Finally install the project dependencies

```sh
just setup
```

## Just receipes

Simply run `just` to see the list of receipes.

## Environment variables

To enable `direnv` for the folder, run the following command:

```sh
direnv allow
```

Now the environment variables configured in `.envrc` will be automatically loaded as soon as you enter the folder.
