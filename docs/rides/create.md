# Create a Ride

App should allow a user to create or update a ride.

## Rules

- A user who creates the ride is the ride owner and 'creatorId' field will be set to the user's id.
- A user can update a ride ONLY if they are the ride owner or if they are an admin of the ride.
- A ride can be updated only if it is not started yet.
- There can be no more than 8 breakpoints, this is the limit set by Google Maps.
- A ride can be of the following types:
  - Private: This type of ride is invisible and can only be RSVP'ed if the user has the link to the ride. Creator can share the link with other users via phone's sharing options.
  - Public (in future releases): This type of ride is visible to all users and will be displayed in the discovery section of the app. This is a feature to be developed in future.
  - Group Only (in future releases): This type of ride is visible to all users who are in the group where the creator has shared the ride. This is a feature to be developed in future.
- User should have option to save locations in their favorites for future use.

## Steps to create a ride

- App should mimic the flow from Google Maps where the user can search for starting point, destination, and other breakpoints.
- While the user is searching & selecting all the locations, the interface should show the map with the route.
- While selecting location, user should also select the type of location it is, like halt, meeting point, fuel station, etc.

## API's needed for creating a ride

- A create ride API which will take all the ride details, save it in the database and return back all the ride details so that the app can cache and display it.
- API to search for locations using Google Places API (Place Autocomplete).
- API to fetch location / place details using Google Places API (Place Details).
- API to fetch route details using Google Maps Directions API.
- API to save location to favorites.
- API to remove location from favorites.

## Dataset

We need some datasets for certain features.

- `Country Codes (ccTLD)`: This is used as input to some Google API's. This data is available at <https://github.com/datasets/country-codes/blob/main/data/country-codes.csv>
- `GeoJSON`: This is used to limit the scope of search for locations. This data is available at <https://github.com/datasets/geo-countries/blob/main/data/countries.geojson>
- `Datahub.io`: Free datasets for various categories are available at <https://datahub.io/>
