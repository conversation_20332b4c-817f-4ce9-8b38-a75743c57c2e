# Service (API)

## First API called by the app even before the user is logged in

```http
POST /init
```

> `Auth`: false

This API is used to get the initial settings. As of now only `success` is returned as boolean indicating that the app can allow test user to login. A test user can login by simply double clicking on the logo on Login screen. When this API returns `success` as true, the app will allow the user to login by double clicking on the logo otherwise clicking on the logo will have no effect.

## API to get user details

```http
POST /user/:userId
```

> `Auth`: true

This API is used to get the user details. The request body contains the following fields:

- `lat` - Latitude of the user
- `lng` - Longitude of the user
- `platform` - Platform of the user (ios or android)
- `user` - User details (as returned by Firebase Auth's User object)
