{
  "compilerOptions": {
    "incremental": true, // Faster subsequent builds
    "composite": true,
    "target": "ES2020",
    "baseUrl": ".",
    "rootDir": "./src/",
    "module": "ES2020",
    "moduleResolution": "Bundler",
    "paths": {
      "@/*": ["src/*"]
    },
    "resolveJsonModule": true,
    "strict": true,
    "noImplicitOverride": true, // Better OOP support
    "noUncheckedIndexedAccess": true, // Additional type safety
    "declaration": true,
    "outDir": "./dist",
    "sourceMap": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    "skipLibCheck": true,
    // Build performance optimizations
    "tsBuildInfoFile": "./.tsbuildinfo",
    "assumeChangesOnlyAffectDirectDependencies": true
  },
  "include": ["src/**/*.ts"],
  "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]
}
