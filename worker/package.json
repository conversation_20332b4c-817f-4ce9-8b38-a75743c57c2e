{"name": "@95octane/worker", "version": "0.0.1+1", "description": "Worker for 95octane", "type": "module", "main": "dist/worker.js", "scripts": {"build:dev": "webpack --node-env development --watch", "build:prod": "webpack --node-env production", "build:analyze": "webpack --node-env production --analyze", "clean": "rm -rf dist .webpack-cache .tsbuildinfo", "clean:cache": "rm -rf .webpack-cache .tsbuildinfo", "deps": "just start-deps", "dev": "concurrently \"pnpm run build:dev\" \"pnpm run nodemon\"", "lint": "eslint \"src/**/*.ts\" --fix", "nodemon": "wait-on tcp:9099 tcp:8080 tcp:9199 tcp:7233 dist/worker.js && nodemon --enable-source-maps dist/worker.js", "serve": "pnpm run deps && pnpm run dev"}, "dependencies": {"@temporalio/activity": "latest", "@temporalio/worker": "latest", "@temporalio/workflow": "latest", "axios": "latest", "firebase-admin": "latest", "zod": "latest"}, "devDependencies": {"@types/node": "latest", "nodemon": "latest", "pino-pretty": "latest", "terser-webpack-plugin": "latest", "ts-loader": "latest", "tsconfig-paths-webpack-plugin": "latest", "typescript": "latest", "wait-on": "latest", "webpack": "latest", "webpack-cli": "latest", "webpack-node-externals": "latest"}}