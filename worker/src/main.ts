import { fileURLToPath } from "node:url";
import { config } from "@/_shared/config";
import * as activities from "@/activities";
import { NativeConnection, Worker } from "@temporalio/worker";

async function run() {
  const temporalConnection = await NativeConnection.connect(
    config.TEMPORAL_CONNECTION,
  );
  const worker = await Worker.create({
    connection: temporalConnection,
    workflowsPath: fileURLToPath(new URL("./workflows", import.meta.url)),
    activities,
    taskQueue: config.TASK_QUEUE,
    namespace: config.NAMESPACE,
  });

  await worker.run();
}

run().catch((err) => {
  console.error(err);
  process.exit(1);
});
