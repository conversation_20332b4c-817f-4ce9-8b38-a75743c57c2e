import { env } from "node:process";
import { z } from "zod";

const configSchema = z.object({
  NODE_ENV: z.enum(["development", "production"]).default("production"),
  TEMPORAL_CONNECTION: z.object({
    address: z.string().default("localhost:7233"),
    tls: z.boolean().default(false),
  }),
  NAMESPACE: z.string().default("default"),
  TASK_QUEUE: z.string().default("task-queue"),
  GOOGLE_APPLICATION_CREDENTIALS: z.string().optional(),
});

const parsed = configSchema.safeParse({
  NODE_ENV: env.NODE_ENV,
  TEMPORAL_CONNECTION: {
    address: env.TEMPORAL_CONNECTION_ADDRESS,
    tls: env.TEMPORAL_CONNECTION_TLS === "true",
  },
  GOOGLE_APPLICATION_CREDENTIALS: env.GOOGLE_APPLICATION_CREDENTIALS,
});

if (parsed.success) {
  console.log("✅ Valid environment variables");
  console.log("NODE_ENV:", parsed.data.NODE_ENV);
}
else {
  console.error("❌ Invalid environment variables:", parsed.error.toString());
  process.exit(1);
}

export const config = parsed.data;
