import path from "node:path";
import { env } from "node:process";
import { fileURLToPath } from "node:url";
import TerserPlugin from "terser-webpack-plugin";
import TsconfigPathsPlugin from "tsconfig-paths-webpack-plugin";
import nodeExternals from "webpack-node-externals";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const isDevelopment = env.NODE_ENV === "development";
const isProduction = env.NODE_ENV === "production";

/** @type {import('webpack').Configuration} */
export default {
  target: "node",
  mode: env.NODE_ENV || "production",
  entry: "./src/main.ts",
  // Persistent caching for faster rebuilds
  cache: {
    type: "filesystem",
    cacheDirectory: path.resolve(__dirname, ".webpack-cache"),
    buildDependencies: {
      config: [__filename],
    },
  },
  experiments: {
    outputModule: true,
  },
  externals: [
    nodeExternals({
      importType: "module",
    }),
  ],
  module: {
    rules: [
      {
        test: /\.ts$/,
        use: [
          {
            loader: "ts-loader",
            options: {
              transpileOnly: isDevelopment,
              configFile: path.resolve(__dirname, "tsconfig.json"),
              // Enable faster incremental builds
              experimentalWatchApi: true,
              // Use project references for better performance
              projectReferences: true,
              // Faster compilation in development
              compilerOptions: isDevelopment ? {
                incremental: true,
                tsBuildInfoFile: path.resolve(__dirname, ".tsbuildinfo"),
              } : undefined,
            },
          },
        ],
        exclude: /node_modules/,
      },
    ],
  },
  resolve: {
    extensions: [".ts", ".js", ".json"],
    plugins: [
      new TsconfigPathsPlugin({
        configFile: "./tsconfig.json",
      }),
    ],
    alias: {
      "@": path.resolve(__dirname, "src"),
    },
  },
  output: {
    filename: "worker.js",
    path: path.resolve(__dirname, "dist"),
    clean: true,
    module: true,
    chunkFormat: "module",
    library: {
      type: "module",
    },
    environment: {
      module: true,
    },
  },
  optimization: {
    minimize: !isDevelopment,
    minimizer: [
      new TerserPlugin({
        parallel: true, // Enable parallel processing
        terserOptions: {
          format: {
            comments: false,
          },
          module: true,
          compress: {
            drop_console: isProduction, // Remove console logs in production
          },
        },
        extractComments: false,
      }),
    ],
  },
  watchOptions: {
    ignored: /node_modules/,
    aggregateTimeout: 200, // Reduced for faster rebuilds
    poll: isDevelopment ? false : 1000, // Use native watching in development
  },
  infrastructureLogging: {
    level: isDevelopment ? "info" : "error",
  },
  stats: {
    colors: true,
    modules: false,
    children: false,
    chunks: false,
    chunkModules: false,
  },
  devtool: isDevelopment ? "eval-cheap-module-source-map" : "source-map",
  // Performance monitoring
  performance: {
    hints: isDevelopment ? false : "warning",
    maxEntrypointSize: 512000,
    maxAssetSize: 512000,
  },
};
