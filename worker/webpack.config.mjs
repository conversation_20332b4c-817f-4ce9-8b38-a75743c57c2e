import path from "node:path";
import { env } from "node:process";
import { fileURLToPath } from "node:url";
import TerserPlugin from "terser-webpack-plugin";
import TsconfigPathsPlugin from "tsconfig-paths-webpack-plugin";
import nodeExternals from "webpack-node-externals";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const isDevelopment = env.NODE_ENV === "development";

/** @type {import('webpack').Configuration} */
export default {
  target: "node",
  mode: env.NODE_ENV || "production",
  entry: "./src/main.ts",
  experiments: {
    outputModule: true,
  },
  externals: [
    nodeExternals({
      importType: "module",
    }),
  ],
  module: {
    rules: [
      {
        test: /\.ts$/,
        use: [
          {
            loader: "ts-loader",
            options: {
              transpileOnly: isDevelopment,
              configFile: path.resolve(__dirname, "tsconfig.json"),
            },
          },
        ],
        exclude: /node_modules/,
      },
    ],
  },
  resolve: {
    extensions: [".ts", ".js", ".json"],
    plugins: [
      new TsconfigPathsPlugin({
        configFile: "./tsconfig.json",
      }),
    ],
    alias: {
      "@": path.resolve(__dirname, "src"),
    },
  },
  output: {
    filename: "worker.js",
    path: path.resolve(__dirname, "dist"),
    clean: true,
    module: true,
    chunkFormat: "module",
    library: {
      type: "module",
    },
    environment: {
      module: true,
    },
  },
  optimization: {
    minimize: !isDevelopment,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          format: {
            comments: false,
          },
          module: true,
        },
        extractComments: false,
      }),
    ],
  },
  watchOptions: {
    ignored: /node_modules/,
    aggregateTimeout: 300,
    poll: 1000,
  },
  infrastructureLogging: {
    level: isDevelopment ? "info" : "error",
  },
  stats: {
    colors: true,
    modules: false,
    children: false,
    chunks: false,
    chunkModules: false,
  },
  devtool: isDevelopment ? "source-map" : false,
};
