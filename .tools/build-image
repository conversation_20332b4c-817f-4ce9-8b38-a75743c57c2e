#! /usr/bin/env bash

set -e

IMAGE_NAME="api-service"

echo -en "Checking branch name ... "
BRANCH=$(git rev-parse --abbrev-ref HEAD)
if [[ ${BRANCH} == "develop" ]]; then
  RELEASE_TYPE="dev"
elif [[ ${BRANCH} == "main" ]]; then
  RELEASE_TYPE="prod"
else
  echo -e "\033[0;31mERROR\033[0m"
  echo -e "\033[0;33mBranch name '${BRANCH}' is not allowed, please switch to 'develop' or 'main' branch and try again.\033[0m"
  exit 1
fi
echo -e "\033[0;32m${RELEASE_TYPE}\033[0m"

echo -en "Checking release ... "
VERSION=$(grep '"version":' ./package.json | head -n 1 | sed 's/"version": //' | sed 's/[\",[:blank:]]//g')
RELEASE=${RELEASE_TYPE}-v$(echo $VERSION | sed 's/.*+//')
echo -e "\033[0;32m${RELEASE}\033[0m"

# Build the image
echo "Building image for release: ${RELEASE}..."
docker build --platform linux/amd64 --tag "${IMAGE_NAME}:latest" --build-arg RELEASE_TYPE=${RELEASE_TYPE} .

# Tag the image
echo "Tagging image for release: ${RELEASE}..."
docker tag "${IMAGE_NAME}:latest" "asia-east1-docker.pkg.dev/prod-95octane-app/docker-registry/${IMAGE_NAME}:${RELEASE}"
