#! /usr/bin/env bash

set -e

IMAGE_NAME="api-service"

echo -en "Checking branch name ... "
BRANCH=$(git rev-parse --abbrev-ref HEAD)
if [[ ${BRANCH} == "develop" ]]; then
  RELEASE_TYPE="dev"
elif [[ ${BRANCH} == "main" ]]; then
  RELEASE_TYPE="prod"
else
  echo -e "\033[0;31mERROR\033[0m"
  echo -e "\033[0;33mBranch name '${BRANCH}' is not allowed, please switch to 'develop' or 'main' branch and try again.\033[0m"
  exit 1
fi
echo -e "\033[0;32m${RELEASE_TYPE}\033[0m"

echo -en "Checking release ... "
VERSION=$(grep '"version":' ./package.json | head -n 1 | sed 's/"version": //' | sed 's/[\",[:blank:]]//g')
RELEASE=${RELEASE_TYPE}-v$(echo $VERSION | sed 's/.*+//')
echo -e "\033[0;32m${RELEASE}\033[0m"

# Push the image
echo "Pushing image to registry..."
docker push "asia-east1-docker.pkg.dev/prod-95octane-app/docker-registry/${IMAGE_NAME}:${RELEASE}"

# Release the image in Google Cloud Run
echo "Releasing image in Google Cloud Run..."
gcloud run services update ${RELEASE_TYPE}-${IMAGE_NAME} --image "asia-east1-docker.pkg.dev/prod-95octane-app/docker-registry/${IMAGE_NAME}:${RELEASE}" --region asia-east1 --no-use-http2
