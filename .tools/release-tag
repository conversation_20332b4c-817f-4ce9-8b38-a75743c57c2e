#! /usr/bin/env bash

# Check if argument is one of the allowed values
if [[ ! "service" =~ (^|[[:space:]])"$1"($|[[:space:]]) ]]; then
  echo -e "Usage: release-tag <app-type>\n\nWhere <app-type> is 'service'"
  exit 1
fi

# Function to test if the version follows N.N.N+N format
function check_version_format() {
  if [[ $1 =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\+[0-9]{1,3}$ ]]; then
    return 0
  else
    return 1
  fi
}

# Function to check if any uncommitted changes exist
function has_uncommitted_changes() {
  if [[ $(git status --porcelain) ]]; then
    return 0
  else
    return 1
  fi
}

# Function to check if any untracked files exist
function has_untracked_files() {
  if [[ $(git ls-files --others --exclude-standard) ]]; then
    return 0
  else
    return 1
  fi
}

# Function to check if any unpushed commits exist
function has_unpushed_commits() {
  if [[ $(git log --branches --not --remotes) ]]; then
    return 0
  else
    return 1
  fi
}

# Check if the latest commit has tag
echo -en "Checking for duplicate release ... "
if git describe --exact-match --tags HEAD >/dev/null 2>&1 | egrep "^$1"; then
  echo -e "\033[0;31mERROR\033[0m"
  echo -e "\033[0;33mLatest commit already has a tag, please commit your changes and try again.\033[0m"
  exit 1
fi
echo -e "\033[0;32mOK\033[0m"

# Check if version in functions/package.json follows our versioning format
echo -en "Checking if version in service/package.json is in correct format ... "
VERSION=$(grep '"version":' ./service/package.json | head -n 1 | sed 's/"version": //' | sed 's/[\",[:blank:]]//g')
if ! check_version_format ${VERSION}; then
  echo -e "\033[0;31mERROR\033[0m"
  echo -e "\033[0;33mVersion in package.json '${VERSION}' does not follow 'major.minor.release+build (1.2.3+4)' format, please update it and try again.\033[0m"
  exit 1
fi
echo -e "\033[0;32mOK\033[0m"

# Check the branch and set release type to "dev" if branch is "develop" or "prod" if branch is "main"
echo -en "Checking branch name ... "
BRANCH=$(git rev-parse --abbrev-ref HEAD)
if [[ ${BRANCH} == "develop" ]]; then
  RELEASE_TYPE="dev"
  echo -e "\033[0;32mOK\033[0m"
elif [[ ${BRANCH} == "main" ]]; then
  RELEASE_TYPE="prod"
  echo -e "\033[0;32mOK\033[0m"
else
  echo -e "\033[0;31mERROR\033[0m"
  echo -e "\033[0;33mBranch name '${BRANCH}' is not allowed, please switch to 'develop' or 'main' branch and try again.\033[0m"
  exit 1
fi

# Generate tag for release
echo -en "Generating tag for $1 release ... "
APPTYPE="$1"
TAG=${RELEASE_TYPE}-${APPTYPE}-v${VERSION}
echo -e "\033[0;32mOK\033[0m"

# Check if tag exists already
echo -en "Checking tag ${TAG} ... "
if git rev-parse ${TAG} >/dev/null 2>&1; then
  echo -e "\033[0;31mERROR\033[0m"
  echo -e "\033[0;33mTag ${TAG} already exists, please update the version in pubspec.yaml and try again.\033[0m"
  exit 1
fi
echo -e "\033[0;32mOK\033[0m"

# Check if any untracked files exist
echo -en "Checking if any untracked files exist ... "
if has_untracked_files; then
  echo -e "\033[0;31mERROR\033[0m"
  echo -e "\033[0;33mUntracked files exist, please commit or remove them and try again.\033[0m"
  exit 1
fi
echo -e "\033[0;32mOK\033[0m"

# Check if any uncommitted changes exist
echo -en "Checking if any uncommitted changes exist ... "
if has_uncommitted_changes; then
  echo -e "\033[0;31mERROR\033[0m"
  echo -e "\033[0;33mUncommitted changes exist, please commit or remove them and try again.\033[0m"
  exit 1
fi
echo -e "\033[0;32mOK\033[0m"

# Check if any unpushed commits exist
echo -en "Checking if any unpushed commits exist ... "
if has_unpushed_commits; then
  echo -e "\033[0;31mERROR\033[0m"
  echo -e "\033[0;33mUnpushed commits exist, please push them and try again.\033[0m"
  exit 1
fi
echo -e "\033[0;32mOK\033[0m"

# Tag the release
echo -en "Tagging ${TAG} ... \033[0;31m"
if ! git tag --sign --message "Release ${TAG}" ${TAG}; then
  echo -e "\033[0m"
  exit 1
fi
echo -e "\033[0;32mOK\033[0m"

# Push the tag to remote
echo -en "Pushing tag to remote ... "
if ! git push origin ${TAG}; then
  echo -e "\033[0;31mFAIL!!!\033[0m"
  exit 1
fi
echo -e "\033[0;32mDone!\033[0m"

exit 0
