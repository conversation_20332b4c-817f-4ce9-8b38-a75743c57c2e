### Development Environment

### Reload if secrets changes
watch_file ./.env.dev.secrets

### Load secrets kept in '.env.dev.secrets' file. Note that this will be ignored by git.
source_env ./.env.dev.secrets

### Load other environment variables
export NODE_ENV="development"
export CLOUDSDK_CORE_PROJECT="prod-95octane-app"
export PORT=8888
export HOST=127.0.0.1
export TEST_USER_EMAIL="<EMAIL>"
export TEST_USER_PASSWORD="BfCd@1029"
export HOME95="${HOME}/Projects/github.com/95octane"
export LOG_LEVEL="debug"

### Minimum app version
export MINIMUM_APP_VERSION="76"

### OpenTelemetry
export OTEL_ENDPOINT='localhost:4318'
export OTEL_API_KEY=''
# export OTEL_ENDPOINT='https://ingress.coralogixsg.com:443'

### To connect with production firebase
# export GOOGLE_APPLICATION_CREDENTIALS=$(expand_path ../secrets/gcp/95-fb-adminsdk.json)

### To connect with emulator
export USE_EMULATOR="true"
export FIREBASE_AUTH_EMULATOR_HOST="127.0.0.1:9099"
export FIREBASE_AUTH_DOMAIN="prod-95octane-app.firebaseapp.com"
export FIREBASE_PROJECT_ID="prod-95octane-app"
export FIREBASE_STORAGE_EMULATOR_HOST="127.0.0.1:9199"
export FIRESTORE_EMULATOR_HOST="127.0.0.1:8080"
export GOOGLE_APPLICATION_CREDENTIALS=""
