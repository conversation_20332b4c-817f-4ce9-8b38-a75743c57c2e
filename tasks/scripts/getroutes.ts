import * as fs from "node:fs";
import * as path from "node:path";
import axios from "axios";

type Location = {
  placeId: string;
  title: string;
  type: string;
  direction?: string;
};

type RideData = {
  startLocation: Location;
  endLocation: Location;
  breakpoints: Location[];
};

async function getRoutes(
  origin: string,
  destination: string,
  waypoints: string[],
) {
  const url = "https://routes.googleapis.com/directions/v2:computeRoutes";

  const data = {
    origin: {
      placeId: origin,
    },
    destination: {
      placeId: destination,
    },
    intermediates: waypoints.map(placeId => ({
      placeId,
    })),
    travelMode: "DRIVE",
    computeAlternativeRoutes: true,
    routeModifiers: {
      avoidTolls: false,
      avoidHighways: false,
      avoidFerries: false,
    },
    languageCode: "en-US",
    units: "METRIC",
  };

  const response = await axios.post(url, data, {
    headers: {
      "Content-Type": "application/json",
      "X-Goog-Api-Key": process.env.GOOGLE_MAPS_API_KEY,
      "X-Goog-FieldMask": "*",
    },
  });

  return response.data;
}

async function processFile(filePath: string, outputPath: string) {
  const content = await readFile(filePath, "utf8");
  const data = JSON.parse(content);
  const ride: RideData = Object.values(data)[0] as RideData;

  // Get waypoints with direction "To"
  const waypoints = ride.breakpoints
    .filter(bp => bp.direction === "To")
    .map(bp => bp.placeId);

  const routeResponse = await getRoutes(
    ride.startLocation.placeId,
    ride.endLocation.placeId,
    waypoints,
  );

  await writeFile(outputPath, JSON.stringify(routeResponse, null, 2));
}

async function processAllFiles() {
  try {
    const files = await readdir(inputFolder);
    const jsonFiles = files.filter(file => path.extname(file) === ".json");

    for (const file of jsonFiles) {
      console.log(`Processing ${file}...`);
      await processFile(
        path.join(inputFolder, file),
        path.join(outputFolder, file),
      );
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  catch (error) {
    console.error("Error:", error);
  }
}

const readFile = fs.promises.readFile;
const readdir = fs.promises.readdir;
const writeFile = fs.promises.writeFile;
const inputFolder = "./testData/rides";
const outputFolder = "./testData/routes";

if (!fs.existsSync(outputFolder)) {
  fs.mkdirSync(outputFolder);
}

// Get arguments after the script name
const commandLineArgs = process.argv.slice(2);

if (commandLineArgs.length > 1) {
  const rideFileName = commandLineArgs[0];

  processFile(
    path.join(inputFolder, rideFileName),
    path.join(outputFolder, rideFileName),
  );
}
else {
  processAllFiles();
}
