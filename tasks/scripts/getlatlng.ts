import * as fs from "node:fs";
import * as path from "node:path";
import { promisify } from "node:util";
import axios from "axios";

const readdir = promisify(fs.readdir);
const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);

type Location = {
  placeId: string;
  title: string;
  latitude: number;
  longitude: number;
};

type RideData = {
  startLocation: Location;
  endLocation: Location;
  breakpoints: Location[];
};

async function getCoordinates(
  placeId: string,
): Promise<{ lat: number; lng: number }> {
  try {
    const url = `https://maps.googleapis.com/maps/api/place/details/json`;
    const response = await axios.get(url, {
      params: {
        place_id: placeId,
        fields: "geometry/location",
        key: process.env.GOOGLE_MAPS_API_KEY,
      },
    });

    if (
      response.data.status === "OK"
      && response.data.result?.geometry?.location
    ) {
      return response.data.result.geometry.location;
    }
    else {
      console.error(`Failed to get coordinates for placeId: ${placeId}`);
      console.error("API Response:", response.data);
      return { lat: 0, lng: 0 }; // Default values
    }
  }
  catch (error) {
    console.error(`Error fetching coordinates for placeId: ${placeId}`, error);
    return { lat: 0, lng: 0 }; // Default values
  }
}

async function processFile(filePath: string, outputPath: string) {
  const content = await readFile(filePath, "utf8");
  const data = JSON.parse(content);
  const ride: RideData = Object.values(data)[0] as RideData;

  const locations = [ride.startLocation, ride.endLocation, ...ride.breakpoints];

  for (const location of locations) {
    const coords = await getCoordinates(location.placeId);
    location.latitude = coords.lat;
    location.longitude = coords.lng;
    await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay
  }

  await writeFile(outputPath, JSON.stringify(data, null, 2));
}

async function processAllFiles() {
  try {
    const inputFolder = "./testData/rides";
    const outputFolder = "./testData/processed";

    // Create output folder if it doesn't exist
    if (!fs.existsSync(outputFolder)) {
      fs.mkdirSync(outputFolder);
    }

    const files = await readdir(inputFolder);
    const jsonFiles = files.filter(file => path.extname(file) === ".json");

    for (const file of jsonFiles) {
      console.log(`Processing ${file}...`);
      await processFile(
        path.join(inputFolder, file),
        path.join(outputFolder, file),
      );
    }
  }
  catch (error) {
    console.error("Error processing files:", error);
  }
}

processAllFiles();
