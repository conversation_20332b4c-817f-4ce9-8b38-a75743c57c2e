/* eslint-disable @typescript-eslint/no-explicit-any */
import { initializeApp } from "firebase-admin/app";
import { getFirestore, Timestamp } from "firebase-admin/firestore";
import * as fs from "node:fs";
import * as path from "node:path";

function createFutureDateWithTime(sourceDateTime: Date, daysToAdd: number): Date {
  // Create future date from today
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + daysToAdd);

  // Create new date with future date and source time
  return new Date(
    futureDate.getFullYear(),
    futureDate.getMonth(),
    futureDate.getDate(),
    sourceDateTime.getHours(),
    sourceDateTime.getMinutes(),
    sourceDateTime.getSeconds(),
    sourceDateTime.getMilliseconds()
  );
}

function getFilesByPattern(
  folderPath: string,
  startsWith: string,
  endsWith: string,
): string[] {
  try {
    const files = fs.readdirSync(folderPath);
    const rideFiles = files.filter(
      file => file.startsWith(startsWith) && file.endsWith(endsWith),
    );
    return rideFiles.map(file => path.join(folderPath, file));
  }
  catch (error) {
    console.error("Error reading folder:", error);
    return [];
  }
}

// Initialize Firebase Admin SDK
initializeApp();
const db = getFirestore();

async function importDocument(
  collectionId: string,
  docId: string,
  docData: any,
) {
  try {
    // Adjust the startDate & endDate to be in the future
    const addDays: number = docData["addDays"] || 0;
    if (addDays > 0) {
      const startDate = docData["startDate"];
      const endDate = docData["endDate"];
      docData["startDate"] = createFutureDateWithTime(new Date(startDate), addDays).toISOString();
      docData["endDate"] = createFutureDateWithTime(new Date(endDate), addDays).toISOString();
    }
    delete docData["addDays"];
    // Import data into Firestore
    for (const field in docData) {
      if (typeof docData[field] === "string" && isDateString(docData[field])) {
        docData[field] = Timestamp.fromDate(new Date(docData[field]));
      }
    }

    docData.createdAt = Timestamp.now();
    docData.updatedAt = Timestamp.now();

    await db.collection(collectionId).doc(docId).set(docData, { merge: true });

    console.log(`/${collectionId}/${docId} imported successfully!`);
  }
  catch (error) {
    console.error(`Error importing rideId (/${collectionId}/${docId}):`, error);
  }
}

async function importSubDocument(
  collectionId: string,
  docId: string,
  subCollectionId: string,
  subDocId: string,
  docData: any,
) {
  try {
    // Import data into Firestore
    // for (const field in docData) {
    //   if (typeof docData[field] === 'string' && isDateString(docData[field])) {
    //     docData[field] = admin.firestore.Timestamp.fromDate(new Date(docData[field]));
    //   }
    // }

    await db
      .collection(collectionId)
      .doc(docId)
      .collection(subCollectionId)
      .doc(subDocId)
      .set(docData, { merge: true });

    console.log(
      `/${collectionId}/${docId}/${subCollectionId}/${subDocId} imported successfully!`,
    );
  }
  catch (error) {
    console.error(
      `Error importing rideId (/${collectionId}/${docId}/${subCollectionId}/${subDocId}):`,
      error,
    );
  }
}

// Helper function to check if a string represents a valid date
function isDateString(str: string): boolean {
  const date = new Date(str);
  return !isNaN(date.getTime()) && date > new Date("1 Jan 2024"); // Check if the date is valid
}

// Import all rides
const ridefiles = getFilesByPattern("./testData/rides", "ride-", ".json");
ridefiles.forEach((file) => {
  console.log(`Importing file: ${file}`);

  // Import Rides
  const fileContent = JSON.parse(fs.readFileSync(file, "utf-8"));
  const rideId = Object.keys(fileContent)[0];
  const rideObject = fileContent[rideId];
  console.log(`Importing RideId: ${rideId} (${file})`);
  // console.log(rideObject);
  importDocument("rides", rideId, rideObject);

  // Import Routes
  const routeFile = file.replace("/rides/", "/routes/");
  const routeFileContent = JSON.parse(fs.readFileSync(routeFile, "utf-8"));
  const routeObject = routeFileContent.routes[0];
  console.log(`Importing route for RideId: ${rideId} (${file})`);
  // console.log(routeObject);
  importSubDocument("rides", rideId, "routes", "to-0", routeObject);
});
