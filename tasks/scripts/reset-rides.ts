import { initializeApp } from "firebase-admin/app";
import { getFirestore } from "firebase-admin/firestore";

// Initialize Firebase Admin SDK
initializeApp();
const db = getFirestore();

async function getRides(): Promise<string[]> {
  const ridesCol = await db.collection("rides").get();
  const rideIds: string[] = [];
  ridesCol.forEach((doc) => {
    rideIds.push(doc.id);
  });
  return rideIds;
}

async function deleteRides(rideIds: string[], userId: string) {
  for (const rideId of rideIds) {
    await db
      .collection("rides")
      .doc(rideId)
      .collection("participants")
      .doc(userId)
      .delete();
    await db
      .collection("users")
      .doc(userId)
      .collection("rides")
      .doc(rideId)
      .delete();
  }
}

// Import all rides
async function run(userId: string) {
  const rides = await getRides();
  await deleteRides(rides, userId);
  console.log("Done!");
}

// <PERSON><PERSON><PERSON>'s User Id: bFVAtFWvKoW4R4QMnNTfNfRDwl02
// Viraj's User Id: U3LiJkfdlDfpkFEzwsc5eFiqoCQ2
// Test95octane User Id: 7i241hVEbscnqDLmDPVXeNb78gh1
run("7i241hVEbscnqDLmDPVXeNb78gh1");
