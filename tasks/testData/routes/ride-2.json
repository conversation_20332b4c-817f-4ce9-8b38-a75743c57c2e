{"routes": [{"legs": [{"distanceMeters": 18952, "duration": "2921s", "staticDuration": "2921s", "polyline": {"encodedPolyline": "_henAgptxMt@{EHm@DUNBETYxAw@hF[xBYjBG\\a@dCe@|Ci@dE_@tCE|D@PCfAGpEHRL\\h@`Ad@v@TXnBpCd@t@xApBVZrBfCT\\`Al<PERSON>h@p@`@\\VLJJtCfCb@XJDp@HTAPAj@I`AKbBUbKqAlB[hBUfAO`@`@PZWPa@^MLe@j@U\\Sl@GRYdBMfACPGl@K`B?v@PrANhA`@bADjAF`AFrA@NTlBn@pAdBzA|BnB`CjB|DbD|ArANLTZLVLbANfBFf@Lj@VvBJxBBhAElAMrBMrBS~DAtB@f@B~@MbBu@lCYdBWnAk@dBk@fBM~@?t@Lx@JrAH|ABj@RfF?VB|@Bz@Z|G?VDb@Fj@Jb@Tz@~@pDX~@FT^hAVr@h@pBh@tCDTH`@^`BDRDRZxAFVRt@n@vBj@lCXpB@TFbA?HBdC@fA@XNrA^pCRxADX`@lCFZH`@Xr@JVJPZf@BHRRp@fALR^n@LTNVn@n@HFj@\\RLZPt@b@v@t@Vb@Rb@Tt@`@`BLv@Lf@f@zBt@nCF^|@vEJl@`@`EXdDHz@D\\?FD`@P|AT~BPx@FPVp@LX^|@t@tAZj@LTRZh@x@RVtB~BTV@Dt@hARd@HTL^BLBNXxAL`@BHLTPRTT^\\FHRZXd@FHRHDFFDPTBDNXz@~Af@|@BDZd@DJNPZh@`AfBb@hABJJf@Lh@FVLV`@f@pAjAv@`ABDrBxB\\`@p@~@Zd@JVr@pAt@zA^r@^`AFHZ`AP~@Hd@N|@Nv@DR`AlEJ`@L^~AxEdAxCf@lAVf@n@fAZh@JPFHDHjAvART~@z@XVpAfA`@\\d@^FB`At@JJRNRLZNZTXNZRbAp@xBdAdAp@LFNHfAr@ZTTPVTVTZVHHd@`@RPlAhAf@j@NRPZJRVd@Zj@DJNVJNFJn@z@jAdAhA|@`@d@DJP\\FN@D~@nDp@hCDTDd@@v@?RCj@CxA?V?X?R?@?XBn@JnABVDh@B^TfDRrBV`BT|@DJR`AD\\DPDJt@zC^zANl@b@hAvAxBfAtABBHJX^`@l@j@r@zC`D|@lA~CvFz@lAv@h@n@XrAj@JFp@b@j@h@HL\\l@FNL\\Lh@Pv@Nh@TdATf@PVHJf@f@x@p@n@h@FDl@h@JJx@~@t@bAXb@T^Td@PZRVT`@DFBD\\h@JN^h@h@x@VZvB`C|A~ABDDBdEdEXZb@h@Zn@Pd@p@tBd@pAJ\\f@pAd@hADHFL^v@JRHRLVLV^p@T^BDf@d@p@h@`@T`Ah@x@l@XVv@fAh@v@NPRVf@f@v@j@l@^tEjBX`@n@b@tA~@NNdAlAdA|A|@bBtAdCl@~@|@fAZXbBvAJJ`B~Az@|@JHJLj@r@x@v@HF~@ZlBb@hB^d@Jh@LJF^T^d@Rd@Lt@@t@AbAA`B@fBFh@Jd@DLh@jBXdBJ^FXLl@n@bCn@|BPj@N`@b@jA\\bAr@tADFHPRZb@t@tBlDLRHN`@jAF`@Hl@@Jb@zCBNDNXbAb@bAJNp@~@jAxAFH\\l@PZd@~@`AxAh@n@`@ZRPt@b@l@XdAj@~C|AZPdAv@l@n@LJTXDFJNj@q@"}, "startLocation": {"latLng": {"latitude": 12.975524499999999, "longitude": 77.6065958}}, "endLocation": {"latLng": {"latitude": 12.901739899999999, "longitude": 77.4691213}}, "steps": [{"distanceMeters": 149, "staticDuration": "33s", "polyline": {"encodedPolyline": "_henAgptxMt@{EHm@"}, "startLocation": {"latLng": {"latitude": 12.975524499999999, "longitude": 77.6065958}}, "endLocation": {"latLng": {"latitude": 12.9751993, "longitude": 77.6079304}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head east on Mahatma Gandhi Rd toward Brigade Rd/Kamaraj Rd"}, "localizedValues": {"distance": {"text": "0.1 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 982, "staticDuration": "265s", "polyline": {"encodedPolyline": "_fenAqxtxMDUNBETYxAw@hF[xBYjBG\\a@dCe@|Ci@dE_@tCE|D@PCfAGpE"}, "startLocation": {"latLng": {"latitude": 12.9751993, "longitude": 77.6079304}}, "endLocation": {"latLng": {"latitude": 12.976652099999999, "longitude": 77.5993408}}, "navigationInstruction": {"maneuver": "UTURN_RIGHT", "instructions": "Make a U-turn at Brigade Rd/Kamaraj Rd\nPass by NAVRATHAN JEWELLERS (on the left in 400m)"}, "localizedValues": {"distance": {"text": "1.0 km"}, "staticDuration": {"text": "4 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 760, "staticDuration": "142s", "polyline": {"encodedPolyline": "aoenA{bsxMHRL\\h@`Ad@v@TXnBpCd@t@xApBVZrBfCT\\`AlAh@p@`@\\VLJJtCfCb@X"}, "startLocation": {"latLng": {"latitude": 12.976652099999999, "longitude": 77.5993408}}, "endLocation": {"latLng": {"latitude": 12.972193299999999, "longitude": 77.5940826}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "Continue onto Kasturba Rd"}, "localizedValues": {"distance": {"text": "0.8 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 562, "staticDuration": "97s", "polyline": {"encodedPolyline": "esdnA_brxMJDp@HTAPAj@I`AKbBUbKqAlB[hBUfAO"}, "startLocation": {"latLng": {"latitude": 12.972193299999999, "longitude": 77.5940826}}, "endLocation": {"latLng": {"latitude": 12.967248, "longitude": 77.5949771}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "Continue onto Vittal Mallya Rd"}, "localizedValues": {"distance": {"text": "0.6 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 778, "staticDuration": "142s", "polyline": {"encodedPolyline": "itcnAsgrxM`@`@PZWPa@^MLe@j@U\\Sl@GRYdBMfACPGl@K`B?v@PrANhA`@bADjAF`AFrA@NTlBn@pA"}, "startLocation": {"latLng": {"latitude": 12.967248, "longitude": 77.5949771}}, "endLocation": {"latLng": {"latitude": 12.967298999999999, "longitude": 77.5883881}}, "navigationInstruction": {"maneuver": "TURN_SHARP_RIGHT", "instructions": "Sharp right onto Fort Rd/Raja <PERSON> Rd\nContinue to follow Raja <PERSON> Rd"}, "localizedValues": {"distance": {"text": "0.8 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 15684, "staticDuration": "2234s", "polyline": {"encodedPolyline": "stcnAm~pxMdBzA|BnB`CjB|DbD|ArANLTZLVLbANfBFf@Lj@VvBJxBBhAElAMrBMrBS~DAtB@f@B~@MbBu@lCYdBWnAk@dBk@fBM~@?t@Lx@JrAH|ABj@RfF?VB|@Bz@Z|G?VDb@Fj@Jb@Tz@~@pDX~@FT^hAVr@h@pBh@tCDTH`@^`BDRDRZxAFVRt@n@vBj@lCXpB@TFbA?HBdC@fA@XNrA^pCRxADX`@lCFZH`@Xr@JVJPZf@BHRRp@fALR^n@LTNVn@n@HFj@\\RLZPt@b@v@t@Vb@Rb@Tt@`@`BLv@Lf@f@zBt@nCF^|@vEJl@`@`EXdDHz@D\\?FD`@P|AT~BPx@FPVp@LX^|@t@tAZj@LTRZh@x@RVtB~BTV@Dt@hARd@HTL^BLBNXxAL`@BHLTPRTT^\\FHRZXd@FHRHDFFDPTBDNXz@~Af@|@BDZd@DJNPZh@`AfBb@hABJJf@Lh@FVLV`@f@pAjAv@`ABDrBxB\\`@p@~@Zd@JVr@pAt@zA^r@^`AFHZ`AP~@Hd@N|@Nv@DR`AlEJ`@L^~AxEdAxCf@lAVf@n@fAZh@JPFHDHjAvART~@z@XVpAfA`@\\d@^FB`At@JJRNRLZNZTXNZRbAp@xBdAdAp@LFNHfAr@ZTTPVTVTZVHHd@`@RPlAhAf@j@NRPZJRVd@Zj@DJNVJNFJn@z@jAdAhA|@`@d@DJP\\FN@D~@nDp@hCDTDd@@v@?RCj@CxA?V?X?R?@?XBn@JnABVDh@B^TfDRrBV`BT|@DJR`AD\\DPDJt@zC^zANl@b@hAvAxBfAtABBHJX^`@l@j@r@zC`D|@lA~CvFz@lAv@h@n@XrAj@JFp@b@j@h@HL\\l@FNL\\Lh@Pv@Nh@TdATf@PVHJf@f@x@p@n@h@FDl@h@JJx@~@t@bAXb@T^Td@PZRVT`@DFBD\\h@JN^h@h@x@VZvB`C|A~ABDDBdEdEXZb@h@Zn@Pd@p@tBd@pAJ\\f@pAd@hADHFL^v@JRHRLVLV^p@T^BDf@d@p@h@`@T`Ah@x@l@XVv@fAh@v@NPRVf@f@v@j@l@^tEjBX`@n@b@tA~@NNdAlAdA|A|@bBtAdCl@~@|@fAZXbBvAJJ`B~Az@|@JHJLj@r@x@v@HF~@ZlBb@hB^d@Jh@LJF^T^d@Rd@Lt@@t@AbAA`B@fBFh@Jd@DLh@jBXdBJ^FXLl@n@bCn@|BPj@N`@b@jA\\bAr@tADFHPRZb@t@tBlDLRHN`@jAF`@Hl@@Jb@zCBNDNXbAb@bAJNp@~@jAxAFH\\l@PZd@~@`AxAh@n@`@ZRPt@b@l@XdAj@~C|AZPdAv@l@n@LJTXDFJN"}, "startLocation": {"latLng": {"latitude": 12.967298999999999, "longitude": 77.5883881}}, "endLocation": {"latLng": {"latitude": 12.901962, "longitude": 77.46887029999999}}, "navigationInstruction": {"maneuver": "ROUNDABOUT_LEFT", "instructions": "At Corporation Cir, take the 1st exit onto Mysore Rd\nPass by the church (on the right)"}, "localizedValues": {"distance": {"text": "15.7 km"}, "staticDuration": {"text": "37 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 37, "staticDuration": "8s", "polyline": {"encodedPolyline": "g|vmAmsywMj@q@"}, "startLocation": {"latLng": {"latitude": 12.901962, "longitude": 77.46887029999999}}, "endLocation": {"latLng": {"latitude": 12.901739899999999, "longitude": 77.4691213}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left\nDestination will be on the right"}, "localizedValues": {"distance": {"text": "37 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "19.0 km"}, "duration": {"text": "49 mins"}, "staticDuration": {"text": "49 mins"}}}, {"distanceMeters": 67099, "duration": "3261s", "staticDuration": "3261s", "polyline": {"encodedPolyline": "{zvmA_uywMk@p@~@xAb@t@zAlCtBfE`@t@|AlCT\\v@vAjBzCdAfBPZd@t@jCnE^h@h@t@~@dAXVt@l@\\VrDfC`@XnDdCp@d@LH`Ar@lBvAl@h@j@h@h@n@PXp@jAPXl@hAbBxC~ArCj@~@T\\BBvCdEp@z@\\\\b@b@RRb@^vDtC`D`Ch@^fAv@jHnFXRh@`@jDfCnBhA`Af@~@b@lG|CdAh@dAh@bCrAl@\\lBdA`Af@d@Vf@X`@T|@f@zDtBpAv@^VjA`Ab@^z@x@zBjCjBjCPVX`@`AxAX`@RVjBnCNRb@d@\\^z@n@j@^|Ar@~Aj@hF`B`DbAlDjAxDrAr@ZfAh@VNpCpBZXrB`Bz@p@dDfCj@d@fBrA`@\\XTZTPLj@b@xCzBpCxBzFxEn@d@^Vv@z@v@z@~@bAnCjDRXT^dAlALL~AxArDhC`B~@~@f@dAd@fC`AtAd@~@XnBj@NDxErBhB`ApBdAnAj@rBx@bA`@p@RbBh@bCv@hC~@bElA~Af@zErAdBf@PFXJb@RdA`@j@Z^Tt@d@xC`Cj@n@t@l@|@x@hB~AzClCrAlAbCzBrAnA\\ZhAbAXRZV^VNJ|Ax@fChAvBz@vBz@d@Rl@Tx@Zp@Xp@Xx@\\bBr@zB|@v@XnCp@vB^xCX~AJh@Bt@HnC^hARhARrATfARxCj@|FdAfEr@nAR|@L^DXDhCZbCZfB`@d@NjAb@|Av@`Al@fAx@XV`AbAXZTZh@t@r@rAP^N^j@dBT`A`@hCFpA@v@B|@DlF@x@BrABnA\\fTFfD@XBtBLhJD~BF~B@l@B|BHvENvFRlDP|ADd@Hf@n@~Dp@tC^pA|@nCr@lBpBdEz@~A`ChD`ApAdCnCdGnFzDhDdI`HTTtCfCr@l@jKdJhDfCrA~@fCtAnClAbC`AfElAjDv@`El@xD^fBF\\BhBDrCBnJ@dC@V?hA@tACrGCjC@`@?t@@~@@dA@zC@z@@pA@`FFnADlBRf@Hb@Hp@NpAb@~@^dB|@zAhAv@l@dJnHxApATPzBbC`AvAf@z@fAbC^v@JVXp@zF|MXn@lB`EP^N^hApCXv@HPxBnGvApDf@jA`AhBR\\n@lAjGrKfFjJXh@l@rAn@`B|@hCPh@`AvDv@zDp@rF^|DdAtOb@zGFfAD`@RtBnAjRHvAL~ADb@Db@Z|Dd@tCxAtFf@vAr@tCl@~AVl@^r@BDfA|Ap@x@`BlBhE`Fj@l@|AhB~AbBdD|CpG`G^ZdHrGhEzDlBfBd@`@LLfClCf@p@zAtB~@vAbClDl@x@NVPVhEjGZ^pArAnBzA\\TrHfFdC~AJFzJxGl@`@bAr@zAjAnBfBpBvBhB~B`AxAnBnD~B`G^hA`@~AtA|GX`Bj@bDrAlIxA~Ht@~EjApGj@`DdBxJBJJn@Hd@`ArFxArI|BpMVrBJbBFbCC~COhCOnAYzAk@zBe@`BSt@aAbDM^Ur@iAvDu@fCWz@yGzTUx@g@`BuB`H{@|CM`@wFdRgDvK}CnK{BvHuB`HwDhMcAvD]tB[dCQrCGzBJbDb@fFr@zDrAvEN^|AbDr@hAjBjClBrBXXf@b@nEdDzEdDFB`IvFvBzAnIdGbKdHnDjCfD|B\\V`CtBnBvBzBnCbBlBfAnA~@fAfNfPvRlUdCtCjC`DlCtCx@n@`BjAdEhClEdCjAr@~A`A`CrApDzBlEjC~@j@hLxGnBlArBlAzAz@jI|ElFbDzRlLxQrKxDtBtI`EfF`C|At@rB|@l@ZjHhDpBbA^RhD|AvAn@jAn@VRn@j@tCtCXXdLvKrIjIpDrD`AhAbCpDl@`AT^|@~A^n@T\\pGhKjBtCr@~@pBvB|@v@`@\\dAv@lAp@TNxC`BlKzFXNTLdAl@`C~An@b@j@`@`At@`@\\x@r@jDdD`H~G~A|AdFrF|@dAt@~@|BhCjCrCfCdCh@h@rEvElAlARPrDhDpB`B|AdAz@f@zCnApA`@tCp@dC`@zDh@hARfB^|@N|AXhAZjAZfDlArAp@^RvAx@xA`AdAv@jAbAf@b@j@d@~BrCb@t@vA`CfAzBz@vB~AnEd@rAz@~B~DxKp@xAP^Vb@TZh@p@vAvArBrAfAh@~Af@zAXhBTbGZhCNb@BjBL~Hb@h@Dz@Lz@NjAVjA\\|Ap@~A`Aj@`@l@h@`A`ANNr@|@dAhBJTv@fB\\dA^hBRdB@H^zDDd@XrCVlC`@nEJdANdBBf@JtAPbF@j@BvBDtEFdFFxAJbAj@vCfA~El@dCJ\\^z@rBxFt@lCNt@lBfK\\nCPvCTjNR`D|@hIz@`Id@rCxDpQhArF^vCNlCFtJ@z@FpABl@r@zGv@hGHn@@LFtAZ`DPxAXtBD`@dCpRrApIh@~B|@`E`@`BZfAZ|@d@bAjC~EtHpN|AtCdCrEhHvMvApCh@nAj@hBJ`@\\|BNhCNxGHdBFz@Hz@PvBHr@Jf@Hd@RnAl@tC|@zCXz@hArCdAbC|@hBx@fBf@bAN`@Rb@`ApBbBhDXj@lCdG|B`Fx@xAdAjBJP~A~Bj@v@pCjDNRr@bAVZbCxCJLDFRVjAbBp@|@jAdBrAdCdDjHvA~CR`@R\\dAhBxArB|AbBl@j@jB~Af@`@XTtBhBzAlAj@f@|ApAPLnGnFvRdPtLzJ~DfDfI~GXVzAtAxA|Ax@bA^l@hAfBNXR`@JVh@nAv@vBz@|CNp@nBxJvIdc@Np@vDvRbCvLz@hEt@~DPv@vAjHZ|AxAlHh@|BRr@Xt@\\p@bA`B`DrERXz@lA|CdE`E`GvDbFT\\lDvEV^z@hA\\b@xH|Jt@|@TXTVh@p@pHxI`EzEdD`Ez@nAn@pAl@pA`@jAlB|Gt@rD~@fFJl@PfAd@lCb@|Bx@nCp@jBxBdGzA`F\\rAv@bDl@dCb@lBn@fCfAfDdBjFv@bCXz@\\FXbA~BfHnAxDfAvCf@lAL\\xB~Fb@rA^`AhBvD"}, "startLocation": {"latLng": {"latitude": 12.901739899999999, "longitude": 77.4691213}}, "endLocation": {"latLng": {"latitude": 12.573165699999999, "longitude": 77.0140452}}, "steps": [{"distanceMeters": 37, "staticDuration": "9s", "polyline": {"encodedPolyline": "{zvmA_uywMk@p@"}, "startLocation": {"latLng": {"latitude": 12.901739899999999, "longitude": 77.4691213}}, "endLocation": {"latLng": {"latitude": 12.901962, "longitude": 77.46887029999999}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head northwest toward NH 275"}, "localizedValues": {"distance": {"text": "37 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 10727, "staticDuration": "686s", "polyline": {"encodedPolyline": "g|vmAmsywM~@xAb@t@zAlCtBfE`@t@|AlCT\\v@vAjBzCdAfBPZd@t@jCnE^h@h@t@~@dAXVt@l@\\VrDfC`@XnDdCp@d@LH`Ar@lBvAl@h@j@h@h@n@PXp@jAPXl@hAbBxC~ArCj@~@T\\BBvCdEp@z@\\\\b@b@RRb@^vDtC`D`Ch@^fAv@jHnFXRh@`@jDfCnBhA`Af@~@b@lG|CdAh@dAh@bCrAl@\\lBdA`Af@d@Vf@X`@T|@f@zDtBpAv@^VjA`Ab@^z@x@zBjCjBjCPVX`@`AxAX`@RVjBnCNRb@d@\\^z@n@j@^|Ar@~Aj@hF`B`DbAlDjAxDrAr@ZfAh@VNpCpBZXrB`Bz@p@dDfCj@d@fBrA`@\\XTZTPLj@b@xCzBpCxBzFxEn@d@^Vv@z@v@z@~@bAnCjDRXT^dAlALL~AxArDhC`B~@~@f@dAd@fC`AtAd@~@XnBj@NDxErBhB`ApBdAnAj@rBx@bA`@p@RbBh@bCv@hC~@bElA~Af@zErAdBf@PFXJb@RdA`@j@Z^Tt@d@xC`Cj@n@t@l@|@x@hB~AzClCrAlAbCzBrAnA\\ZhAbAXRZV^VNJ|Ax@fChAvBz@vBz@d@Rl@Tx@Zp@Xp@Xx@\\bBr@zB|@v@XnCp@vB^xCX~AJh@Bt@HnC^hARhARrATfARxCj@"}, "startLocation": {"latLng": {"latitude": 12.901962, "longitude": 77.46887029999999}}, "endLocation": {"latLng": {"latitude": 12.825829299999999, "longitude": 77.41169459999999}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left onto NH 275\nToll road\nPass by <PERSON><PERSON> (on the left in 7 km)"}, "localizedValues": {"distance": {"text": "10.7 km"}, "staticDuration": {"text": "11 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 37179, "staticDuration": "1658s", "polyline": {"encodedPolyline": "m`hmAannwM|FdAfEr@nAR|@L^DXDhCZbCZfB`@d@NjAb@|Av@`Al@fAx@XV`AbAXZTZh@t@r@rAP^N^j@dBT`A`@hCFpA@v@B|@DlF@x@BrABnA\\fTFfD@XBtBLhJD~BF~B@l@B|BHvENvFRlDP|ADd@Hf@n@~Dp@tC^pA|@nCr@lBpBdEz@~A`ChD`ApAdCnCdGnFzDhDdI`HTTtCfCr@l@jKdJhDfCrA~@fCtAnClAbC`AfElAjDv@`El@xD^fBF\\BhBDrCBnJ@dC@V?hA@tACrGCjC@`@?t@@~@@dA@zC@z@@pA@`FFnADlBRf@Hb@Hp@NpAb@~@^dB|@zAhAv@l@dJnHxApATPzBbC`AvAf@z@fAbC^v@JVXp@zF|MXn@lB`EP^N^hApCXv@HPxBnGvApDf@jA`AhBR\\n@lAjGrKfFjJXh@l@rAn@`B|@hCPh@`AvDv@zDp@rF^|DdAtOb@zGFfAD`@RtBnAjRHvAL~ADb@Db@Z|Dd@tCxAtFf@vAr@tCl@~AVl@^r@BDfA|Ap@x@`BlBhE`Fj@l@|AhB~AbBdD|CpG`G^ZdHrGhEzDlBfBd@`@LLfClCf@p@zAtB~@vAbClDl@x@NVPVhEjGZ^pArAnBzA\\TrHfFdC~AJFzJxGl@`@bAr@zAjAnBfBpBvBhB~B`AxAnBnD~B`G^hA`@~AtA|GX`Bj@bDrAlIxA~Ht@~EjApGj@`DdBxJBJJn@Hd@`ArFxArI|BpMVrBJbBFbCC~COhCOnAYzAk@zBe@`BSt@aAbDM^Ur@iAvDu@fCWz@yGzTUx@g@`BuB`H{@|CM`@wFdRgDvK}CnK{BvHuB`HwDhMcAvD]tB[dCQrCGzBJbDb@fFr@zDrAvEN^|AbDr@hAjBjClBrBXXf@b@nEdDzEdDFB`IvFvBzAnIdGbKdHnDjCfD|B\\V`CtBnBvBzBnCbBlBfAnA~@fAfNfPvRlUdCtCjC`DlCtCx@n@`BjAdEhClEdCjAr@~A`A`CrApDzBlEjC~@j@hLxGnBlArBlAzAz@jI|ElFbDzRlLxQrKxDtBtI`EfF`C|At@rB|@l@ZjHhDpBbA^RhD|AvAn@jAn@VRn@j@tCtCXXdLvKrIjIpDrD`AhAbCpDl@`AT^|@~A^n@T\\pGhKjBtCr@~@pBvB|@v@`@\\dAv@lAp@TNxC`BlKzFXNTLdAl@`C~An@b@j@`@`At@`@\\x@r@jDdD`H~G~A|AdFrF|@dAt@~@|BhCjCrCfCdCh@h@rEvElAlARPrDhDpB`B|AdAz@f@zCnApA`@tCp@dC`@zDh@hARfB^|@N|AXhAZjAZfDlArAp@^RvAx@xA`AdAv@jAbAf@b@j@d@~BrCb@t@vA`CfAzBz@vB~AnEd@rAz@~B~DxKp@xAP^Vb@TZh@p@vAvArBrAfAh@~Af@zAXhBTbGZhCNb@BjBL~Hb@h@Dz@Lz@NjAVjA\\|Ap@~A`Aj@`@l@h@`A`ANNr@|@dAhBJTv@fB\\dA^hBRdB@H^zD"}, "startLocation": {"latLng": {"latitude": 12.825829299999999, "longitude": 77.41169459999999}}, "endLocation": {"latLng": {"latitude": 12.644518999999999, "longitude": 77.1693947}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "Continue onto Bengaluru - <PERSON><PERSON><PERSON>"}, "localizedValues": {"distance": {"text": "37.2 km"}, "staticDuration": {"text": "28 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 18295, "staticDuration": "840s", "polyline": {"encodedPolyline": "gsdlAuc_vMDd@XrCVlC`@nEJdANdBBf@JtAPbF@j@BvBDtEFdFFxAJbAj@vCfA~El@dCJ\\^z@rBxFt@lCNt@lBfK\\nCPvCTjNR`D|@hIz@`Id@rCxDpQhArF^vCNlCFtJ@z@FpABl@r@zGv@hGHn@@LFtAZ`DPxAXtBD`@dCpRrApIh@~B|@`E`@`BZfAZ|@d@bAjC~EtHpN|AtCdCrEhHvMvApCh@nAj@hBJ`@\\|BNhCNxGHdBFz@Hz@PvBHr@Jf@Hd@RnAl@tC|@zCXz@hArCdAbC|@hBx@fBf@bAN`@Rb@`ApBbBhDXj@lCdG|B`Fx@xAdAjBJP~A~Bj@v@pCjDNRr@bAVZbCxCJLDFRVjAbBp@|@jAdBrAdCdDjHvA~CR`@R\\dAhBxArB|AbBl@j@jB~Af@`@XTtBhBzAlAj@f@|ApAPLnGnFvRdPtLzJ~DfDfI~GXVzAtAxA|Ax@bA^l@hAfBNXR`@JVh@nAv@vBz@|CNp@nBxJvIdc@Np@vDvRbCvLz@hEt@~DPv@vAjHZ|AxAlHh@|BRr@Xt@\\p@bA`B`DrERXz@lA|CdE`E`GvDbFT\\lDvEV^z@hA\\b@xH|Jt@|@TXTVh@p@pHxI`EzEdD`Ez@nAn@pAl@pA`@jAlB|Gt@rD~@fFJl@PfAd@lCb@|Bx@nCp@jBxBdGzA`F\\rAv@bDl@dCb@lBn@fCfAfDdBjFv@bCXz@"}, "startLocation": {"latLng": {"latitude": 12.644518999999999, "longitude": 77.1693947}}, "endLocation": {"latLng": {"latitude": 12.5765991, "longitude": 77.02109039999999}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "Continue onto Bengaluru - Mysuru Expy/NH 275\nPass by the gas station (on the left in 5.8 km)"}, "localizedValues": {"distance": {"text": "18.3 km"}, "staticDuration": {"text": "14 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 17, "staticDuration": "3s", "polyline": {"encodedPolyline": "wjwkAydbuM\\F"}, "startLocation": {"latLng": {"latitude": 12.5765991, "longitude": 77.02109039999999}}, "endLocation": {"latLng": {"latitude": 12.5764513, "longitude": 77.0210477}}, "navigationInstruction": {"maneuver": "RAMP_LEFT", "instructions": "Take the exit toward Mysore Rd/Vidyaranyapura - Nanjangud Rd"}, "localizedValues": {"distance": {"text": "17 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 844, "staticDuration": "65s", "polyline": {"encodedPolyline": "yiwkAqdbuMXbA~BfHnAxDfAvCf@lAL\\xB~Fb@rA^`AhBvD"}, "startLocation": {"latLng": {"latitude": 12.5764513, "longitude": 77.0210477}}, "endLocation": {"latLng": {"latitude": 12.573165699999999, "longitude": 77.0140452}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at ಪೋಲಿಸ್ ಇರ್ತಾರೆ ಮಗ onto Mysore Rd/Vidyaranyapura - Nanjangud Rd\nPass by Sree Balaji Convention Hall (on the left)\nDestination will be on the left"}, "localizedValues": {"distance": {"text": "0.8 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "67.1 km"}, "duration": {"text": "54 mins"}, "staticDuration": {"text": "54 mins"}}}, {"distanceMeters": 56903, "duration": "3541s", "staticDuration": "3541s", "polyline": {"encodedPolyline": "iuvkAyx`uMHNnApCFLX~@Hb@Xv@z@dCN^v@lBlB`F~@bCxClIzAfEv@fCv@pDZjBVnCFhABb@FvD@hADlB@d@B|@?`@Dd@JrAHt@VnAPj@Tz@vBlFnAvCj@rAx@nBvApDx@nBdBlEr@dBp@`Bv@bBR~@~@vBtAjDvBpF^`Af@nAJT@D~@bC`AfCRd@rChHjCfHfBpE^~@DJBFBDj@rA`@`AN^rAbDr@~AjB~D`DnHbAxBpAzCdC~Fh@lAt@tAfEjI|BhEpBzDVd@x@`BfCvEfArBf@~@hA~BfCvEpCfFnChGn@jBx@dCz@~BtArDfBnFRx@J`@~B~Il@dD\\dBj@fDH`BBd@CnCKfJIjGA`BCrC@rAAf@G~AOrB_@tB_AzD_AbDU|@i@jC]ZYjBSnCEh@KzE@dABfAJ|B^nFLfArAvMLfATxBn@|Fd@hE@NHl@PzBPxDAjD?TCj@GtAYlCQnAc@rCu@~EiAfH]lCOvAIvB@hB?j@JrBLzA|@rFH`@nCvOl@tEDf@TvBPzBTbEHtA~Ar[x@tOdAhSv@vOLvB`AlRDt@VjFJ`D@~DEnDI~BE~@[nEc@hEs@jEKj@}BnKk@fDMfAUdBY|CM|BI|AEhDAxEDpCBp@Bn@FtATlCd@tEn@jEh@nCzA~FnAvDbBfEfCdFrBfDV^^h@fB`C~@jAxC`DpBlB|ChCzDpCZPpC`BrBbAlEjBlBt@`Cv@nBj@lDx@rEx@tC^fAJv@HbBNjJn@lN~@hBRxANxBTd@HxF~@d@HhB\\pHpAh@LnEvA^Nj@\\zBpAjAz@|@z@z@x@Z\\`ApADFtAbCfAxBZx@dA`CBHfBlEDJxB|F`@nAf@|ALZL\\Tn@\\`AdBvFXbAhCtJXjAhApEnAdFdBpGdC|JnBxHxAxENf@Rn@z@nCZ`ANd@N`@^lA~@vClAxDv@dCZ`Av@~BDHHVl@bBj@xAz@rB`@`AP^r@~A\\v@b@hAh@lA\\z@~@xBd@fA`A`CrBzERh@N^HNN^^|@r@~Al@vATf@Td@^z@^|@d@hAt@fBtE`Lp@`Bn@~Ad@dBd@bCNpAJrABx@Dv@DbATrGPlBd@bCZjAr@pB|@jBNVvAbDnA~D^dCPzBJhBR~ERlFJzBHhDLtAHhCNpCZlDBb@r@~H~@lLRxBFt@TjCFl@HdAp@dIHp@RfCNhBDd@XpCXzCBPT`C^pBLv@H\\Nh@f@fBRf@b@lAb@lAh@hAbBfElDtIp@`BjAnCZr@rBdFz@dBtB`DdBrBh@h@rBdBr@f@hCbBz@h@bC`BdAz@p@p@lA~Ab@v@nDhIbEfJ`AtBn@lAnAbCb@x@b@v@FLdAlB^r@h@|@hA|BNTVb@rA`CjG`KrAtBjBrCpCrE`@p@R^RXXb@NTPZ^j@T\\hAjBd@t@v@dAx@fAbAjAxAvArAjApElF\\\\\\\\|@pAlAtBpEdG~B`DvC|DxC|Eh@hAZx@Zt@dBjFp@lBr@dBh@fAn@pAdApB~ClGr@nA`@v@NV`@n@r@jApBdCd@d@jCrBVPp@`@`Bx@|An@dDlA~HrCbCz@zAh@vEnBpAj@dCtAvA~@n@h@x@z@`ApAxAvB|BdDlAtAf@b@bBjAhGzCfAj@~CfBtA~@lAdApAdAZVn@h@@@~DfDTTVT~@|@lB|AZVLHNN~@`ANPt@`Ad@n@n@dApChEn@~@|@xA~AbCpB~Cd@p@~@bB^x@v@`CRz@`@`CbAnFBFf@dBp@`BJTh@z@NVNPVZhB`Br@l@lA~@x@r@LHv@n@vAlAnJxHdBrA|@r@PNLJ`DpCv@n@zEzDjCxBzDdD`@ZbCnBdB`BnCzCd@j@nB`C~@hAXXRVLL|AdB~AjB~DpEhDzDRR`@d@bBjBlCxCfArAfAnARTjCrCvAdBRRnAxAdAnAzBhCVZp@n@RR^^pA|A~AxApA|@\\R`Ab@vGlClBr@`Bj@l@V|@`@rD|A`A^^Nr@X~@ZTHtCnAbCvAh@^^Xv@n@rAvA~A|B|@dBv@lBh@~An@lCNp@XvAv@jEzBrLz@|ELn@lBlKj@nDh@lCLn@R`AXdBZbBfBdJh@rCXdBDX^nBj@~Cv@~DLh@n@dCj@nBp@`B`CzEfCvEP^d@`AvAtCtAfC|A~Cp@jAl@jAn@nAt@|Aj@`Aj@v@`@d@`@^HHt@n@VN`@Vv@^TJ^Lp@P~Bl@`@H`KvBTDtCl@VF`B`@pCh@hBTP@x@DnADb@?h@@lFDlIJxA@|@@^@b@?`CBz@?dDAzAGhCQhD[|@IbBKvDQrC?fA?nBBfBLhBNrBZp@L^F\\HlAZ`AV|Bp@hD~@XHXJh@N|Br@|An@b@Tr@f@z@t@tArAlAtAvBhCl@l@hAx@l@\\`Ah@`@Pr@P`@F^Fx@JxCJfADfCFP?`DJr@BN?N?jBF^Bz@BtEN`BHnADb@@b@@T?nCGhBQd@GjEm@rAKr@CpB?nBHV@|BPzRpAp@@dDLhDLhCFnBNr@HlAXTHj@RxB`@f@JpAXbFlAlCn@fAVnBb@vAT~Cr@|@VvAf@~CjA\\N^N`C|@THv@FpJY|ETFB`DRzBJ|BDtB?hA?nJD~BCv@AzBGTAZDzAEpAD`AAhA@b@@~BH^DZ@vAOvDm@nBQxBMpDStAIz@Oz@[DIBEFAF?JH@BAJ?BzEl@lDJbDZZBr@Hp@Bl@FxC^R{@b@i@ZQTEfBERAPCFAp@Yx@o@dCwBAMFG@AB?J@DL?BpETXAREZE`EJlI^LKJATi@|@{DF[ZaAhAuBlFJrDHpDFzBDvCr@n@PFD~@f@h@\\d@d@TQ\\EXDVNHNBDD\\?Bd@DVF|BhAbElBLFlCjAtClA|BrAVVV`@|A`ETdAZdEPHFTCROLA@S?QIsCUAYDmAYG"}, "startLocation": {"latLng": {"latitude": 12.573165699999999, "longitude": 77.0140452}}, "endLocation": {"latLng": {"latitude": 12.3028123, "longitude": 76.65565579999999}}, "steps": [{"distanceMeters": 8331, "staticDuration": "585s", "polyline": {"encodedPolyline": "iuvkAyx`uMHNnApCFLX~@Hb@Xv@z@dCN^v@lBlB`F~@bCxClIzAfEv@fCv@pDZjBVnCFhABb@FvD@hADlB@d@B|@?`@Dd@JrAHt@VnAPj@Tz@vBlFnAvCj@rAx@nBvApDx@nBdBlEr@dBp@`Bv@bBR~@~@vBtAjDvBpF^`Af@nAJT@D~@bC`AfCRd@rChHjCfHfBpE^~@DJBFBDj@rA`@`AN^rAbDr@~AjB~D`DnHbAxBpAzCdC~Fh@lAt@tAfEjI|BhEpBzDVd@x@`BfCvEfArBf@~@hA~BfCvEpCfFnChGn@jBx@dCz@~BtArDfBnFRx@J`@~B~Il@dD\\dBj@fDH`BBd@CnCKfJIjGA`BCrC@rAAf@G~AOrB_@tB_AzD_AbDU|@i@jC"}, "startLocation": {"latLng": {"latitude": 12.573165699999999, "longitude": 77.0140452}}, "endLocation": {"latLng": {"latitude": 12.5472019, "longitude": 76.9442658}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head southwest on Mysore Rd/Vidyaranyapura - Nanjangud Rd toward Ambedkar St\nPass by USHA DIAGNOSTIC CENTRE (on the right in 2.4 km)"}, "localizedValues": {"distance": {"text": "8.3 km"}, "staticDuration": {"text": "10 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 11615, "staticDuration": "510s", "polyline": {"encodedPolyline": "_sqkAudstM]ZYjBSnCEh@KzE@dABfAJ|B^nFLfArAvMLfATxBn@|Fd@hE@NHl@PzBPxDAjD?TCj@GtAYlCQnAc@rCu@~EiAfH]lCOvAIvB@hB?j@JrBLzA|@rFH`@nCvOl@tEDf@TvBPzBTbEHtA~Ar[x@tOdAhSv@vOLvB`AlRDt@VjFJ`D@~DEnDI~BE~@[nEc@hEs@jEKj@}BnKk@fDMfAUdBY|CM|BI|AEhDAxEDpCBp@Bn@FtATlCd@tEn@jEh@nCzA~FnAvDbBfEfCdFrBfDV^^h@fB`C~@jAxC`DpBlB|ChCzDpCZPpC`BrBbAlEjBlBt@`Cv@nBj@lDx@rEx@tC^fAJv@HbBNjJn@lN~@hBRxANxBTd@HxF~@d@HhB\\pHpAh@LnEvA^Nj@\\zBpAjAz@|@z@z@x@Z\\`ApADFtAbCfAxBZx@"}, "startLocation": {"latLng": {"latitude": 12.5472019, "longitude": 76.9442658}}, "endLocation": {"latLng": {"latitude": 12.5157102, "longitude": 76.8571012}}, "navigationInstruction": {"maneuver": "RAMP_RIGHT", "instructions": "Slight right to merge onto Bengaluru - Mysuru Expy/Mandya By-Pass Rd"}, "localizedValues": {"distance": {"text": "11.6 km"}, "staticDuration": {"text": "9 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 30642, "staticDuration": "1517s", "polyline": {"encodedPolyline": "enkkA{cbtMdA`CBHfBlEDJxB|F`@nAf@|ALZL\\Tn@\\`AdBvFXbAhCtJXjAhApEnAdFdBpGdC|JnBxHxAxENf@Rn@z@nCZ`ANd@N`@^lA~@vClAxDv@dCZ`Av@~BDHHVl@bBj@xAz@rB`@`AP^r@~A\\v@b@hAh@lA\\z@~@xBd@fA`A`CrBzERh@N^HNN^^|@r@~Al@vATf@Td@^z@^|@d@hAt@fBtE`Lp@`Bn@~Ad@dBd@bCNpAJrABx@Dv@DbATrGPlBd@bCZjAr@pB|@jBNVvAbDnA~D^dCPzBJhBR~ERlFJzBHhDLtAHhCNpCZlDBb@r@~H~@lLRxBFt@TjCFl@HdAp@dIHp@RfCNhBDd@XpCXzCBPT`C^pBLv@H\\Nh@f@fBRf@b@lAb@lAh@hAbBfElDtIp@`BjAnCZr@rBdFz@dBtB`DdBrBh@h@rBdBr@f@hCbBz@h@bC`BdAz@p@p@lA~Ab@v@nDhIbEfJ`AtBn@lAnAbCb@x@b@v@FLdAlB^r@h@|@hA|BNTVb@rA`CjG`KrAtBjBrCpCrE`@p@R^RXXb@NTPZ^j@T\\hAjBd@t@v@dAx@fAbAjAxAvArAjApElF\\\\\\\\|@pAlAtBpEdG~B`DvC|DxC|Eh@hAZx@Zt@dBjFp@lBr@dBh@fAn@pAdApB~ClGr@nA`@v@NV`@n@r@jApBdCd@d@jCrBVPp@`@`Bx@|An@dDlA~HrCbCz@zAh@vEnBpAj@dCtAvA~@n@h@x@z@`ApAxAvB|BdDlAtAf@b@bBjAhGzCfAj@~CfBtA~@lAdApAdAZVn@h@@@~DfDTTVT~@|@lB|AZVLHNN~@`ANPt@`Ad@n@n@dApChEn@~@|@xA~AbCpB~Cd@p@~@bB^x@v@`CRz@`@`CbAnFBFf@dBp@`BJTh@z@NVNPVZhB`Br@l@lA~@x@r@LHv@n@vAlAnJxHdBrA|@r@PNLJ`DpCv@n@zEzDjCxBzDdD`@ZbCnBdB`BnCzCd@j@nB`C~@hAXXRVLL|AdB~AjB~DpEhDzDRR`@d@bBjBlCxCfArAfAnARTjCrCvAdBRRnAxAdAnAzBhCVZp@n@RR^^pA|A~AxApA|@\\R`Ab@vGlClBr@`Bj@l@V|@`@rD|A`A^^Nr@X~@ZTHtCnAbCvAh@^^Xv@n@rAvA~A|B|@dBv@lBh@~An@lCNp@XvAv@jEzBrLz@|ELn@lBlKj@nDh@lCLn@R`AXdBZbBfBdJh@rCXdBDX^nBj@~Cv@~DLh@n@dCj@nBp@`B`CzEfCvEP^d@`AvAtCtAfC|A~Cp@jAl@jAn@nAt@|Aj@`Aj@v@`@d@`@^HHt@n@VN`@Vv@^TJ^Lp@P~Bl@`@H`KvBTDtCl@VF`B`@pCh@hBTP@x@DnADb@?h@@lFDlIJxA@|@@^@b@?`CBz@?dDAzAGhCQhD[|@IbBKvDQrC?fA?nBBfBLhBNrBZp@L^F\\HlAZ`AV|Bp@hD~@XHXJh@N|Br@|An@b@Tr@f@z@t@tArAlAtAvBhCl@l@hAx@l@\\`Ah@`@Pr@P`@F^Fx@JxCJfADfCFP?`DJr@BN?N?jBF^Bz@BtEN`BHnADb@@b@@T?nCGhBQd@GjEm@rAKr@CpB?nBHV@|BPzRpAp@@dDLhDLhCFnBNr@HlAX"}, "startLocation": {"latLng": {"latitude": 12.5157102, "longitude": 76.8571012}}, "endLocation": {"latLng": {"latitude": 12.3504762, "longitude": 76.6610377}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "Continue onto Bengaluru - Mysuru Expy/NH 275\nToll road\nPass by Sri Subramanya Swamy Temple (on the right in 27.9 km)"}, "localizedValues": {"distance": {"text": "30.6 km"}, "staticDuration": {"text": "25 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 315, "staticDuration": "69s", "polyline": {"encodedPolyline": "oekjAoz{rMTHj@RxB`@f@JpAXbFlA"}, "startLocation": {"latLng": {"latitude": 12.3504762, "longitude": 76.6610377}}, "endLocation": {"latLng": {"latitude": 12.3477886, "longitude": 76.66014009999999}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "Continue onto Bangalore - Mysore Rd"}, "localizedValues": {"distance": {"text": "0.3 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 2567, "staticDuration": "250s", "polyline": {"encodedPolyline": "utjjA{t{rMlCn@fAVnBb@vAT~Cr@|@VvAf@~CjA\\N^N`C|@THv@FpJY|ETFB`DRzBJ|BDtB?hA?nJD~BCv@AzBGTAZDzAEpAD`AAhA@b@@~BH^DZ@vAOvDm@nBQxBMpDStAIz@Oz@["}, "startLocation": {"latLng": {"latitude": 12.3477886, "longitude": 76.66014009999999}}, "endLocation": {"latLng": {"latitude": 12.3252256, "longitude": 76.6585383}}, "navigationInstruction": {"maneuver": "STRAIGHT", "instructions": "Continue straight\nPass by Sri Dhandina Maramma Temple (on the right in 1.1 km)"}, "localizedValues": {"distance": {"text": "2.6 km"}, "staticDuration": {"text": "4 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 540, "staticDuration": "110s", "polyline": {"encodedPolyline": "ugfjA{j{rMDIBEFAF?JH@BAJ?BzEl@lDJbDZZBr@Hp@Bl@FxC^"}, "startLocation": {"latLng": {"latitude": 12.3252256, "longitude": 76.6585383}}, "endLocation": {"latLng": {"latitude": 12.3206023, "longitude": 76.6577614}}, "navigationInstruction": {"maneuver": "ROUNDABOUT_LEFT", "instructions": "At the roundabout, take the 3rd exit onto Ashoka Rd"}, "localizedValues": {"distance": {"text": "0.5 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 65, "staticDuration": "14s", "polyline": {"encodedPolyline": "wjejA_f{rMR{@b@i@"}, "startLocation": {"latLng": {"latitude": 12.3206023, "longitude": 76.6577614}}, "endLocation": {"latLng": {"latitude": 12.3203248, "longitude": 76.6582703}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left after the cathedral (on the left)"}, "localizedValues": {"distance": {"text": "65 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 286, "staticDuration": "56s", "polyline": {"encodedPolyline": "_iejAei{rMZQTEfBERAPCFAp@Yx@o@dCwB"}, "startLocation": {"latLng": {"latitude": 12.3203248, "longitude": 76.6582703}}, "endLocation": {"latLng": {"latitude": 12.318114099999999, "longitude": 76.6594254}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "Continue onto Good Shepherd Convent Rd"}, "localizedValues": {"distance": {"text": "0.3 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 490, "staticDuration": "76s", "polyline": {"encodedPolyline": "e{djAmp{rMAMFG@AB?J@DL?BpETXAREZE`EJlI^"}, "startLocation": {"latLng": {"latitude": 12.318114099999999, "longitude": 76.6594254}}, "endLocation": {"latLng": {"latitude": 12.313899099999999, "longitude": 76.6591901}}, "navigationInstruction": {"maneuver": "ROUNDABOUT_LEFT", "instructions": "At Five Lights Cir, take the 3rd exit onto Bangalore Nilgiri Rd\nPass by Hotel Ajantha Palace (on the right)"}, "localizedValues": {"distance": {"text": "0.5 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 282, "staticDuration": "60s", "polyline": {"encodedPolyline": "{`djA}n{rMLKJATi@|@{DF[ZaAhAuB"}, "startLocation": {"latLng": {"latitude": 12.313899099999999, "longitude": 76.6591901}}, "endLocation": {"latLng": {"latitude": 12.3127976, "longitude": 76.6614685}}, "navigationInstruction": {"maneuver": "ROUNDABOUT_LEFT", "instructions": "At the roundabout, take the 1st exit"}, "localizedValues": {"distance": {"text": "0.3 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 400, "staticDuration": "53s", "polyline": {"encodedPolyline": "_zcjAe}{rMlFJrDHpDFzBD"}, "startLocation": {"latLng": {"latitude": 12.3127976, "longitude": 76.6614685}}, "endLocation": {"latLng": {"latitude": 12.3092046, "longitude": 76.6612907}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right onto Government House Rd"}, "localizedValues": {"distance": {"text": "0.4 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 223, "staticDuration": "34s", "polyline": {"encodedPolyline": "occjAa|{rMvCr@n@PFD~@f@h@\\d@d@"}, "startLocation": {"latLng": {"latitude": 12.3092046, "longitude": 76.6612907}}, "endLocation": {"latLng": {"latitude": 12.3074359, "longitude": 76.6603744}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "Continue onto Bangalore Nilgiri Rd/Telephone Exchange Road"}, "localizedValues": {"distance": {"text": "0.2 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 920, "staticDuration": "112s", "polyline": {"encodedPolyline": "oxbjAiv{rMTQ\\EXDVNHNBDD\\?Bd@DVF|BhAbElBLFlCjAtClA|BrAVVV`@|A`ETdAZdE"}, "startLocation": {"latLng": {"latitude": 12.3074359, "longitude": 76.6603744}}, "endLocation": {"latLng": {"latitude": 12.3017884, "longitude": 76.65528309999999}}, "navigationInstruction": {"maneuver": "ROUNDABOUT_LEFT", "instructions": "At Hardinge Cir, take the 2nd exit onto Bangalore Nilagiri Road/Chamaraja Double Road/Dasara Exhibition Rd"}, "localizedValues": {"distance": {"text": "0.9 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 155, "staticDuration": "31s", "polyline": {"encodedPolyline": "euajAovzrMPHFTCROLA@S?QIsCU"}, "startLocation": {"latLng": {"latitude": 12.3017884, "longitude": 76.65528309999999}}, "endLocation": {"latLng": {"latitude": 12.302700999999999, "longitude": 76.6550992}}, "navigationInstruction": {"maneuver": "ROUNDABOUT_LEFT", "instructions": "At Dr <PERSON><PERSON><PERSON><PERSON>/Gun House Cir, take the 3rd exit"}, "localizedValues": {"distance": {"text": "0.2 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 57, "staticDuration": "25s", "polyline": {"encodedPolyline": "{zajAkuzrMAYDmA"}, "startLocation": {"latLng": {"latitude": 12.302700999999999, "longitude": 76.6550992}}, "endLocation": {"latLng": {"latitude": 12.302681999999999, "longitude": 76.6556193}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right"}, "localizedValues": {"distance": {"text": "57 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 15, "staticDuration": "39s", "polyline": {"encodedPolyline": "wzajAsxzrMYG"}, "startLocation": {"latLng": {"latitude": 12.302681999999999, "longitude": 76.6556193}}, "endLocation": {"latLng": {"latitude": 12.3028123, "longitude": 76.65565579999999}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left\nDestination will be on the left"}, "localizedValues": {"distance": {"text": "15 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "56.9 km"}, "duration": {"text": "59 mins"}, "staticDuration": {"text": "59 mins"}}}], "distanceMeters": 142954, "duration": "9722s", "staticDuration": "9722s", "polyline": {"encodedPolyline": "_henAgptxMt@{EHm@DUNBETYxAw@hF[xBYjBG\\a@dCe@|Ci@dE_@tCE|D@PCfAGpEHRL\\h@`Ad@v@TXnBpCd@t@xApBVZrBfCT\\`Al<PERSON>h@p@`@\\VLJJtCfCb@XJDp@HTAPAj@I`AKbBUbKqAlB[hBUfAO`@`@PZWPa@^MLe@j@U\\Sl@GRYdBMfACPGl@K`B?v@PrANhA`@bADjAF`AFrA@NTlBn@pAdBzA|BnB`CjB|DbD|ArANLTZLVLbANfBFf@Lj@VvBJxBBhAElAMrBMrBS~DAtB@f@B~@MbBu@lCYdBWnAk@dBk@fBM~@?t@Lx@JrAH|ABj@RfF?VB|@Bz@Z|G?VDb@Fj@Jb@Tz@~@pDX~@FT^hAVr@h@pBh@tCDTH`@^`BDRDRZxAFVRt@n@vBj@lCXpB@TFbA?HBdC@fA@XNrA^pCRxADX`@lCFZH`@Xr@JVJPZf@BHRRp@fALR^n@LTNVn@n@HFj@\\RLZPt@b@v@t@Vb@Rb@Tt@`@`BLv@Lf@f@zBt@nCF^|@vEJl@`@`EXdDHz@D\\?FD`@P|AT~BPx@FPVp@LX^|@t@tAZj@LTRZh@x@RVtB~BTV@Dt@hARd@HTL^BLBNXxAL`@BHLTPRTT^\\FHRZXd@FHRHDFFDPTBDNXz@~Af@|@BDZd@DJNPZh@`AfBb@hABJJf@Lh@FVLV`@f@pAjAv@`ABDrBxB\\`@p@~@Zd@JVr@pAt@zA^r@^`AFHZ`AP~@Hd@N|@Nv@DR`AlEJ`@L^~AxEdAxCf@lAVf@n@fAZh@JPFHDHjAvART~@z@XVpAfA`@\\d@^FB`At@JJRNRLZNZTXNZRbAp@xBdAdAp@LFNHfAr@ZTTPVTVTZVHHd@`@RPlAhAf@j@NRPZJRVd@Zj@DJNVJNFJn@z@jAdAhA|@`@d@DJP\\FN@D~@nDp@hCDTDd@@v@?RCj@CxA?V?X?R?@?XBn@JnABVDh@B^TfDRrBV`BT|@DJR`AD\\DPDJt@zC^zANl@b@hAvAxBfAtABBHJX^`@l@j@r@zC`D|@lA~CvFz@lAv@h@n@XrAj@JFp@b@j@h@HL\\l@FNL\\Lh@Pv@Nh@TdATf@PVHJf@f@x@p@n@h@FDl@h@JJx@~@t@bAXb@T^Td@PZRVT`@DFBD\\h@JN^h@h@x@VZvB`C|A~ABDDBdEdEXZb@h@Zn@Pd@p@tBd@pAJ\\f@pAd@hADHFL^v@JRHRLVLV^p@T^BDf@d@p@h@`@T`Ah@x@l@XVv@fAh@v@NPRVf@f@v@j@l@^tEjBX`@n@b@tA~@NNdAlAdA|A|@bBtAdCl@~@|@fAZXbBvAJJ`B~Az@|@JHJLj@r@x@v@HF~@ZlBb@hB^d@Jh@LJF^T^d@Rd@Lt@@t@AbAA`B@fBFh@Jd@DLh@jBXdBJ^FXLl@n@bCn@|BPj@N`@b@jA\\bAr@tADFHPRZb@t@tBlDLRHN`@jAF`@Hl@@Jb@zCBNDNXbAb@bAJNp@~@jAxAFH\\l@PZd@~@`AxAh@n@`@ZRPt@b@l@XdAj@~C|AZPdAv@l@n@LJTXDFJNj@q@k@p@~@xAb@t@zAlCtBfE`@t@|AlCT\\v@vAjBzCdAfBPZd@t@jCnE^h@h@t@~@dAXVt@l@\\VrDfC`@XnDdCp@d@LH`Ar@lBvAl@h@j@h@h@n@PXp@jAPXl@hAbBxC~ArCj@~@T\\BBvCdEp@z@\\\\b@b@RRb@^vDtC`D`Ch@^fAv@jHnFXRh@`@jDfCnBhA`Af@~@b@lG|CdAh@dAh@bCrAl@\\lBdA`Af@d@Vf@X`@T|@f@zDtBpAv@^VjA`Ab@^z@x@zBjCjBjCPVX`@`AxAX`@RVjBnCNRb@d@\\^z@n@j@^|Ar@~Aj@hF`B`DbAlDjAxDrAr@ZfAh@VNpCpBZXrB`Bz@p@dDfCj@d@fBrA`@\\XTZTPLj@b@xCzBpCxBzFxEn@d@^Vv@z@v@z@~@bAnCjDRXT^dAlALL~AxArDhC`B~@~@f@dAd@fC`AtAd@~@XnBj@NDxErBhB`ApBdAnAj@rBx@bA`@p@RbBh@bCv@hC~@bElA~Af@zErAdBf@PFXJb@RdA`@j@Z^Tt@d@xC`Cj@n@t@l@|@x@hB~AzClCrAlAbCzBrAnA\\ZhAbAXRZV^VNJ|Ax@fChAvBz@vBz@d@Rl@Tx@Zp@Xp@Xx@\\bBr@zB|@v@XnCp@vB^xCX~AJh@Bt@HnC^hARhARrATfARxCj@|FdAfEr@nAR|@L^DXDhCZbCZfB`@d@NjAb@|Av@`Al@fAx@XV`AbAXZTZh@t@r@rAP^N^j@dBT`A`@hCFpA@v@B|@DlF@x@BrABnA\\fTFfD@XBtBLhJD~BF~B@l@B|BHvENvFRlDP|ADd@Hf@n@~Dp@tC^pA|@nCr@lBpBdEz@~A`ChD`ApAdCnCdGnFzDhDdI`HTTtCfCr@l@jKdJhDfCrA~@fCtAnClAbC`AfElAjDv@`El@xD^fBF\\BhBDrCBnJ@dC@V?hA@tACrGCjC@`@?t@@~@@dA@zC@z@@pA@`FFnADlBRf@Hb@Hp@NpAb@~@^dB|@zAhAv@l@dJnHxApATPzBbC`AvAf@z@fAbC^v@JVXp@zF|MXn@lB`EP^N^hApCXv@HPxBnGvApDf@jA`AhBR\\n@lAjGrKfFjJXh@l@rAn@`B|@hCPh@`AvDv@zDp@rF^|DdAtOb@zGFfAD`@RtBnAjRHvAL~ADb@Db@Z|Dd@tCxAtFf@vAr@tCl@~AVl@^r@BDfA|Ap@x@`BlBhE`Fj@l@|AhB~AbBdD|CpG`G^ZdHrGhEzDlBfBd@`@LLfClCf@p@zAtB~@vAbClDl@x@NVPVhEjGZ^pArAnBzA\\TrHfFdC~AJFzJxGl@`@bAr@zAjAnBfBpBvBhB~B`AxAnBnD~B`G^hA`@~AtA|GX`Bj@bDrAlIxA~Ht@~EjApGj@`DdBxJBJJn@Hd@`ArFxArI|BpMVrBJbBFbCC~COhCOnAYzAk@zBe@`BSt@aAbDM^Ur@iAvDu@fCWz@yGzTUx@g@`BuB`H{@|CM`@wFdRgDvK}CnK{BvHuB`HwDhMcAvD]tB[dCQrCGzBJbDb@fFr@zDrAvEN^|AbDr@hAjBjClBrBXXf@b@nEdDzEdDFB`IvFvBzAnIdGbKdHnDjCfD|B\\V`CtBnBvBzBnCbBlBfAnA~@fAfNfPvRlUdCtCjC`DlCtCx@n@`BjAdEhClEdCjAr@~A`A`CrApDzBlEjC~@j@hLxGnBlArBlAzAz@jI|ElFbDzRlLxQrKxDtBtI`EfF`C|At@rB|@l@ZjHhDpBbA^RhD|AvAn@jAn@VRn@j@tCtCXXdLvKrIjIpDrD`AhAbCpDl@`AT^|@~A^n@T\\pGhKjBtCr@~@pBvB|@v@`@\\dAv@lAp@TNxC`BlKzFXNTLdAl@`C~An@b@j@`@`At@`@\\x@r@jDdD`H~G~A|AdFrF|@dAt@~@|BhCjCrCfCdCh@h@rEvElAlARPrDhDpB`B|AdAz@f@zCnApA`@tCp@dC`@zDh@hARfB^|@N|AXhAZjAZfDlArAp@^RvAx@xA`AdAv@jAbAf@b@j@d@~BrCb@t@vA`CfAzBz@vB~AnEd@rAz@~B~DxKp@xAP^Vb@TZh@p@vAvArBrAfAh@~Af@zAXhBTbGZhCNb@BjBL~Hb@h@Dz@Lz@NjAVjA\\|Ap@~A`Aj@`@l@h@`A`ANNr@|@dAhBJTv@fB\\dA^hBRdB@H^zDDd@XrCVlC`@nEJdANdBBf@JtAPbF@j@BvBDtEFdFFxAJbAj@vCfA~El@dCJ\\^z@rBxFt@lCNt@lBfK\\nCPvCTjNR`D|@hIz@`Id@rCxDpQhArF^vCNlCFtJ@z@FpABl@r@zGv@hGHn@@LFtAZ`DPxAXtBD`@dCpRrApIh@~B|@`E`@`BZfAZ|@d@bAjC~EtHpN|AtCdCrEhHvMvApCh@nAj@hBJ`@\\|BNhCNxGHdBFz@Hz@PvBHr@Jf@Hd@RnAl@tC|@zCXz@hArCdAbC|@hBx@fBf@bAN`@Rb@`ApBbBhDXj@lCdG|B`Fx@xAdAjBJP~A~Bj@v@pCjDNRr@bAVZbCxCJLDFRVjAbBp@|@jAdBrAdCdDjHvA~CR`@R\\dAhBxArB|AbBl@j@jB~Af@`@XTtBhBzAlAj@f@|ApAPLnGnFvRdPtLzJ~DfDfI~GXVzAtAxA|Ax@bA^l@hAfBNXR`@JVh@nAv@vBz@|CNp@nBxJvIdc@Np@vDvRbCvLz@hEt@~DPv@vAjHZ|AxAlHh@|BRr@Xt@\\p@bA`B`DrERXz@lA|CdE`E`GvDbFT\\lDvEV^z@hA\\b@xH|Jt@|@TXTVh@p@pHxI`EzEdD`Ez@nAn@pAl@pA`@jAlB|Gt@rD~@fFJl@PfAd@lCb@|Bx@nCp@jBxBdGzA`F\\rAv@bDl@dCb@lBn@fCfAfDdBjFv@bCXz@\\FXbA~BfHnAxDfAvCf@lAL\\xB~Fb@rA^`AhBvDHNnApCFLX~@Hb@Xv@z@dCN^v@lBlB`F~@bCxClIzAfEv@fCv@pDZjBVnCFhABb@FvD@hADlB@d@B|@?`@Dd@JrAHt@VnAPj@Tz@vBlFnAvCj@rAx@nBvApDx@nBdBlEr@dBp@`Bv@bBR~@~@vBtAjDvBpF^`Af@nAJT@D~@bC`AfCRd@rChHjCfHfBpE^~@DJBFBDj@rA`@`AN^rAbDr@~AjB~D`DnHbAxBpAzCdC~Fh@lAt@tAfEjI|BhEpBzDVd@x@`BfCvEfArBf@~@hA~BfCvEpCfFnChGn@jBx@dCz@~BtArDfBnFRx@J`@~B~Il@dD\\dBj@fDH`BBd@CnCKfJIjGA`BCrC@rAAf@G~AOrB_@tB_AzD_AbDU|@i@jC]ZYjBSnCEh@KzE@dABfAJ|B^nFLfArAvMLfATxBn@|Fd@hE@NHl@PzBPxDAjD?TCj@GtAYlCQnAc@rCu@~EiAfH]lCOvAIvB@hB?j@JrBLzA|@rFH`@nCvOl@tEDf@TvBPzBTbEHtA~Ar[x@tOdAhSv@vOLvB`AlRDt@VjFJ`D@~DEnDI~BE~@[nEc@hEs@jEKj@}BnKk@fDMfAUdBY|CM|BI|AEhDAxEDpCBp@Bn@FtATlCd@tEn@jEh@nCzA~FnAvDbBfEfCdFrBfDV^^h@fB`C~@jAxC`DpBlB|ChCzDpCZPpC`BrBbAlEjBlBt@`Cv@nBj@lDx@rEx@tC^fAJv@HbBNjJn@lN~@hBRxANxBTd@HxF~@d@HhB\\pHpAh@LnEvA^Nj@\\zBpAjAz@|@z@z@x@Z\\`ApADFtAbCfAxBZx@dA`CBHfBlEDJxB|F`@nAf@|ALZL\\Tn@\\`AdBvFXbAhCtJXjAhApEnAdFdBpGdC|JnBxHxAxENf@Rn@z@nCZ`ANd@N`@^lA~@vClAxDv@dCZ`Av@~BDHHVl@bBj@xAz@rB`@`AP^r@~A\\v@b@hAh@lA\\z@~@xBd@fA`A`CrBzERh@N^HNN^^|@r@~Al@vATf@Td@^z@^|@d@hAt@fBtE`Lp@`Bn@~Ad@dBd@bCNpAJrABx@Dv@DbATrGPlBd@bCZjAr@pB|@jBNVvAbDnA~D^dCPzBJhBR~ERlFJzBHhDLtAHhCNpCZlDBb@r@~H~@lLRxBFt@TjCFl@HdAp@dIHp@RfCNhBDd@XpCXzCBPT`C^pBLv@H\\Nh@f@fBRf@b@lAb@lAh@hAbBfElDtIp@`BjAnCZr@rBdFz@dBtB`DdBrBh@h@rBdBr@f@hCbBz@h@bC`BdAz@p@p@lA~Ab@v@nDhIbEfJ`AtBn@lAnAbCb@x@b@v@FLdAlB^r@h@|@hA|BNTVb@rA`CjG`KrAtBjBrCpCrE`@p@R^RXXb@NTPZ^j@T\\hAjBd@t@v@dAx@fAbAjAxAvArAjApElF\\\\\\\\|@pAlAtBpEdG~B`DvC|DxC|Eh@hAZx@Zt@dBjFp@lBr@dBh@fAn@pAdApB~ClGr@nA`@v@NV`@n@r@jApBdCd@d@jCrBVPp@`@`Bx@|An@dDlA~HrCbCz@zAh@vEnBpAj@dCtAvA~@n@h@x@z@`ApAxAvB|BdDlAtAf@b@bBjAhGzCfAj@~CfBtA~@lAdApAdAZVn@h@@@~DfDTTVT~@|@lB|AZVLHNN~@`ANPt@`Ad@n@n@dApChEn@~@|@xA~AbCpB~Cd@p@~@bB^x@v@`CRz@`@`CbAnFBFf@dBp@`BJTh@z@NVNPVZhB`Br@l@lA~@x@r@LHv@n@vAlAnJxHdBrA|@r@PNLJ`DpCv@n@zEzDjCxBzDdD`@ZbCnBdB`BnCzCd@j@nB`C~@hAXXRVLL|AdB~AjB~DpEhDzDRR`@d@bBjBlCxCfArAfAnARTjCrCvAdBRRnAxAdAnAzBhCVZp@n@RR^^pA|A~AxApA|@\\R`Ab@vGlClBr@`Bj@l@V|@`@rD|A`A^^Nr@X~@ZTHtCnAbCvAh@^^Xv@n@rAvA~A|B|@dBv@lBh@~An@lCNp@XvAv@jEzBrLz@|ELn@lBlKj@nDh@lCLn@R`AXdBZbBfBdJh@rCXdBDX^nBj@~Cv@~DLh@n@dCj@nBp@`B`CzEfCvEP^d@`AvAtCtAfC|A~Cp@jAl@jAn@nAt@|Aj@`Aj@v@`@d@`@^HHt@n@VN`@Vv@^TJ^Lp@P~Bl@`@H`KvBTDtCl@VF`B`@pCh@hBTP@x@DnADb@?h@@lFDlIJxA@|@@^@b@?`CBz@?dDAzAGhCQhD[|@IbBKvDQrC?fA?nBBfBLhBNrBZp@L^F\\HlAZ`AV|Bp@hD~@XHXJh@N|Br@|An@b@Tr@f@z@t@tArAlAtAvBhCl@l@hAx@l@\\`Ah@`@Pr@P`@F^Fx@JxCJfADfCFP?`DJr@BN?N?jBF^Bz@BtEN`BHnADb@@b@@T?nCGhBQd@GjEm@rAKr@CpB?nBHV@|BPzRpAp@@dDLhDLhCFnBNr@HlAXTHj@RxB`@f@JpAXbFlAlCn@fAVnBb@vAT~Cr@|@VvAf@~CjA\\N^N`C|@THv@FpJY|ETFB`DRzBJ|BDtB?hA?nJD~BCv@AzBGTAZDzAEpAD`AAhA@b@@~BH^DZ@vAOvDm@nBQxBMpDStAIz@Oz@[DIBEFAF?JH@BAJ?BzEl@lDJbDZZBr@Hp@Bl@FxC^R{@b@i@ZQTEfBERAPCFAp@Yx@o@dCwBAMFG@AB?J@DL?BpETXAREZE`EJlI^LKJATi@|@{DF[ZaAhAuBlFJrDHpDFzBDvCr@n@PFD~@f@h@\\d@d@TQ\\EXDVNHNBDD\\?Bd@DVF|BhAbElBLFlCjAtClA|BrAVVV`@|A`ETdAZdEPHFTCROLA@S?QIsCUAYDmAYG"}, "description": "Mysore Rd", "warnings": ["This route has tolls.", "This route includes a highway."], "viewport": {"low": {"latitude": 12.301656, "longitude": 76.6549378}, "high": {"latitude": 12.976652099999999, "longitude": 77.6080424}}, "travelAdvisory": {}, "localizedValues": {"distance": {"text": "143 km"}, "duration": {"text": "2 hours 42 mins"}, "staticDuration": {"text": "2 hours 42 mins"}}, "routeLabels": ["DEFAULT_ROUTE"], "polylineDetails": {}}], "geocodingResults": {}}