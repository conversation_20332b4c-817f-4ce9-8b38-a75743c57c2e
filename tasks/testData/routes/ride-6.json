{"routes": [{"legs": [{"distanceMeters": 40942, "duration": "3792s", "staticDuration": "3792s", "polyline": {"encodedPolyline": "sx|mAe`oyMKPb@Zn@d@l@`@LHp@h@d@Z|@j@h@`@h@\\fAx@B@LHf@\\PLn@b@VPRN`@Xl@`@PLDBp@d@XRh@^RNf@\\^V\\V~@l@PNJHPJjAn@FB\\NXLd@Tz@`@n@XRJFBLFn@\\ZNx@\\TJPDt@RNBn@DX@V@V?bDDT?rCBL?p@@pA?j@?h@Ar@AfAAtAEd@G|AQz@SDAnB}@zBeAdCmAbFaCnAm@zE}B|Aq@|BiAHE^[PQf@o@fAuA\\a@dB{Bj@u@`@i@t@_At@aAfDoEl@y@n@{@j@y@zB}C|@iAf@c@n@SxDu@lB]nAUr@OPCnCi@|AYl@MjB_@|A[fAQjDg@`BSd@ED?x@G`Ge@`@ETA`@CNANC|Ec@z@IbAKbCUn@IfD_@fAOzEq@rEo@d@KHAbDe@|@IrFq@LAlBSTAv@I\\GHAREpBa@j@MHCVIx@WvAe@r@UhAc@`@MDANEHEv@[\\Mr@U|Ai@TIz@UHCtA_@x@SbBc@H]TeATaAT{@DQJa@F[Jg@BIT_ADOPu@FUR_Ap@cDRcATiAd@gBLa@Rk@^y@JULYVk@nAqCdA_CVi@xAiDVk@v@gBb@aALYjAeCtBcFh@oAPa@N]Te@\\o@DE\\WZMf@KRGFALClBW`BYn@KFApAOdAOb@ILAxBa@zCe@bAO\\Ev@M|AW^EdAGf@Cb@ArAA^AV?dAA`@?t@?dA?X?^?F?~@I@?j@MPGPGPKbAu@t@m@~@w@DElAeAzBiBhA}@XY\\WTSvBeBXWNM^[`Ay@VU|AmAb@_@^YNMzCmC\\WLQVs@Hg@@KVyBVaCBSXiCTqBDa@Fg@@KJqAH_ALgCBc@?ILcCJuAF_A@WFwA?k@?q@Ae@IyA?c@AmA?_@A_@CoAEaB?QAYCcAAa@GkA?YCIBCDAB?f@@bI~@l@HVD`@Fz@Hl@Hp@Jv@JVBF?vEf@j@FnDVtBFt@?nBB`@?`EDj@?`BBhDD~@@`BB^?nGHvCAJA`@E|Eo@`Ca@fG}@|Ba@bLqB`Cc@|A[xAY\\GfB_@|KoBtBYh@INA^E~AKJAjE?v@@fBKnFa@hGe@h@Gx@G~D]j@EtBUvBKzBKlEUfAIj@GrCErA?`BNTB~@JlAPvARdAN~ATj@HvBTj@HfBF`@@zFPzAD|GRB?|@B~DJ`EN~AFJ@vFTjBJfDVnAJxGn@fANtHfAvIlA|Df@dAJ|BR\\BnCTZBb@BhL|@XBnBZRHfAPPDd@NRHl@\\\\V~@hA@@t@jAlAhAj@^zAf@jB~@v@VfBJl@Jz@NvAJfAVzBt@dA`@B?VHRHTP`@Zn@n@JJ`C`C~EnEJHbBzALJhAv@FBdG~BhBr@jE`BNF~ElBfBp@zCjAfBp@ND~An@tChAnFpBp@T`Ab@VJj@XTHt@\\p@ZfAd@vB`A|@b@|@^XDl@IPIJOd@yDD_@j@iFFm@L_AJw@TcBR{@FUJWNGJ]p@mB@AXcAj@eBDQx@qBd@cANWNUt@aBxC_FPWt@yAx@{AHObCqEpD{HzAqDtAoCf@{@Ra@HQxAaClBiCLOb@g@RUlAkAhA{@XUx@i@h@Yr@]`Bo@fBaApGqDhAu@\\[^Uv@m@f@_@dAeATW|ByC?[b@m@`@i@TYX_@NSbHwIbB{BnH}IfEoElAkA^?hCkCNOzAeBnAgA|AeAtBmAfAo@jBgAtA_AZWv@q@|AqA`@]nDwCpCaCRQnBsAfEqDfAmAl@u@z@qA|BaEh@aAp@_Bz@iCnB{H~EmRV_A\\qAVw@j@uAhBgDh@iAn@gAp@y@nAoAt@q@VUxAkAxJcHnA{@zAgA\\UROp@g@hAw@n@e@\\Wb@_@TSb@a@RSpA_B~BcDfA{AVa@Zi@\\o@f@mAXo@ZeAf@uBX_BJg@fAyFJe@nAwGH]ZqAb@eBjCyI~@cEJe@VkAR{@R{@Ry@z@uCx@uCZ_A\\aA^{@P_@v@wAPYPYn@aAx@gAHKJO`FsGhBiCj@_AZm@v@kBZ{@Vu@ZkARaAH[Hk@PkARqBFcADeADaAHwARiBt@mDxAoHf@_DDa@R{Bv@}PF_AP}ATqAfAaGFYXsBNgAJeAHkA`@gEVuCD_@Dc@D_@PsBLyAFs@D_@L{AHo@^_DD_@Jg@XmAx@qCx@qBn@oArAwBzA{BbBgCdBeCvFqIT]f@y@|@uAf@w@b@s@j@aALWh@mAJUXs@Lc@x@{CJg@RiAxAkIFYLq@xBuLNw@|CiQNw@n@eDFYH]VkAd@_B\\}@Ti@bAoB|BcE`@q@`@q@r@aAb@e@l@k@bB_Bn@k@pA_Bl@y@l@sA`@}ADWFc@Hu@DeABy@E_ACUKcASeAu@uDSiBAsBJmBz@kFv@eDb@}Aj@eBt@sBnAuC~@eBrBeDdAyA`ByB~AeBXWZW|F_GrAuAbEcEjB{B|AqBjA}AvAeB~CeET[z@qAZq@rBiDh@o@f@k@|B{BbAmAv@kAf@cAtAsCZi@v@kAhCmDhB{Bn@}@zBaC~CaCdF}C~@m@pA{@bBqAn@o@z@{@vBqCbB}BzB}Cj@u@dF_HVc@t@mA^u@Z}@b@_Bt@eDhAoEJa@|AiGS[AYJm@"}, "startLocation": {"latLng": {"latitude": 12.9321025, "longitude": 77.7422734}}, "endLocation": {"latLng": {"latitude": 12.6877412, "longitude": 77.92695259999999}}, "steps": [{"distanceMeters": 12, "staticDuration": "4s", "polyline": {"encodedPolyline": "sx|mAe`oyMKP"}, "startLocation": {"latLng": {"latitude": 12.9321025, "longitude": 77.7422734}}, "endLocation": {"latLng": {"latitude": 12.932161299999999, "longitude": 77.7421804}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head northwest toward Varthur - Sarjapur Rd"}, "localizedValues": {"distance": {"text": "12 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 6113, "staticDuration": "691s", "polyline": {"encodedPolyline": "_y|mAs_oyMb@Zn@d@l@`@LHp@h@d@Z|@j@h@`@h@\\fAx@B@LHf@\\PLn@b@VPRN`@Xl@`@PLDBp@d@XRh@^RNf@\\^V\\V~@l@PNJHPJjAn@FB\\NXLd@Tz@`@n@XRJFBLFn@\\ZNx@\\TJPDt@RNBn@DX@V@V?bDDT?rCBL?p@@pA?j@?h@Ar@AfAAtAEd@G|AQz@SDAnB}@zBeAdCmAbFaCnAm@zE}B|Aq@|BiAHE^[PQf@o@fAuA\\a@dB{Bj@u@`@i@t@_At@aAfDoEl@y@n@{@j@y@zB}C|@iAf@c@n@SxDu@lB]nAUr@OPCnCi@|AYl@MjB_@|A[fAQjDg@`BSd@ED?x@G`Ge@`@ETA`@CNANC|Ec@z@IbAKbCUn@IfD_@fAOzEq@rEo@d@KHAbDe@|@IrFq@LAlBSTAv@I\\GHAREpBa@j@MHCVIx@WvAe@r@UhAc@`@MDANEHEv@[\\Mr@U|Ai@TIz@UHCtA_@x@SbBc@"}, "startLocation": {"latLng": {"latitude": 12.932161299999999, "longitude": 77.7421804}}, "endLocation": {"latLng": {"latitude": 12.8833302, "longitude": 77.7524057}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left onto Varthur - Sarjapur Rd\nPass by Chrysalis High School, Varthur (on the left)"}, "localizedValues": {"distance": {"text": "6.1 km"}, "staticDuration": {"text": "12 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 4739, "staticDuration": "530s", "polyline": {"encodedPolyline": "ygsmAq_qyMH]TeATaAT{@DQ<PERSON><PERSON>@F[Jg@BIT_ADOPu@FUR_Ap@cDRcATiAd@gBLa@Rk@^y@JULYVk@nAqCdA_CVi@xAiDVk@v@gBb@aALYjAeCtBcFh@oAPa@N]Te@\\o@DE\\WZMf@KRGFALClBW`BYn@KFApAOdAOb@ILAxBa@zCe@bAO\\Ev@M|AW^EdAGf@Cb@ArAA^AV?dAA`@?t@?dA?X?^?F?~@I@?j@MPGPGPKbAu@t@m@~@w@DElAeAzBiBhA}@XY\\WTSvBeBXWNM^[`Ay@VU|AmAb@_@^YNMzCmC\\WLQVs@Hg@@KVyBVaCBSXiCTqBDa@Fg@@KJqAH_ALgCBc@?ILcCJuAF_A@WFwA?k@?q@Ae@IyA?c@AmA?_@A_@CoAEaB?QAYCcAAa@GkA?Y"}, "startLocation": {"latLng": {"latitude": 12.8833302, "longitude": 77.7524057}}, "endLocation": {"latLng": {"latitude": 12.8609785, "longitude": 77.783585}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left after <PERSON>ke wala (on the left)\nPass by the gas station (on the left in 3.1 km)"}, "localizedValues": {"distance": {"text": "4.7 km"}, "staticDuration": {"text": "9 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 6765, "staticDuration": "587s", "polyline": {"encodedPolyline": "c|nmAkbwyMCIBCDAB?f@@bI~@l@HVD`@Fz@Hl@Hp@Jv@JVBF?vEf@j@FnDVtBFt@?nBB`@?`EDj@?`BBhDD~@@`BB^?nGHvCAJA`@E|Eo@`Ca@fG}@|Ba@bLqB`Cc@|A[xAY\\GfB_@|KoBtBYh@INA^E~AKJAjE?v@@fBKnFa@hGe@h@Gx@G~D]j@EtBUvBKzBKlEUfAIj@GrCErA?`BNTB~@JlAPvARdAN~ATj@HvBTj@HfBF`@@zFPzAD|GRB?|@B~DJ`EN~AFJ@vFTjBJfDVnAJxGn@fANtHfAvIlA|Df@dAJ|BR\\BnCTZBb@BhL|@XBnBZRH"}, "startLocation": {"latLng": {"latitude": 12.8609785, "longitude": 77.783585}}, "endLocation": {"latLng": {"latitude": 12.8010752, "longitude": 77.782827}}, "navigationInstruction": {"maneuver": "ROUNDABOUT_LEFT", "instructions": "At the roundabout, take the 2nd exit onto NH 948A\nPass by IIFL Gold Loan (on the right)"}, "localizedValues": {"distance": {"text": "6.8 km"}, "staticDuration": {"text": "10 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 2780, "staticDuration": "308s", "polyline": {"encodedPolyline": "wecmAu}vyMfAPPDd@NRHl@\\\\V~@hA@@t@jAlAhAj@^zAf@jB~@v@VfBJl@Jz@NvAJfAVzBt@dA`@B?VHRHTP`@Zn@n@JJ`C`C~EnEJHbBzALJhAv@FBdG~BhBr@jE`BNF~ElBfBp@zCjAfBp@ND~An@tChAnFpBp@T`Ab@VJj@XTHt@\\p@ZfAd@vB`A|@b@|@^"}, "startLocation": {"latLng": {"latitude": 12.8010752, "longitude": 77.782827}}, "endLocation": {"latLng": {"latitude": 12.779499999999999, "longitude": 77.77061979999999}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "Continue onto NH 948A"}, "localizedValues": {"distance": {"text": "2.8 km"}, "staticDuration": {"text": "5 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 533, "staticDuration": "95s", "polyline": {"encodedPolyline": "{~~lAkqtyMXDl@IPIJOd@yDD_@j@iFFm@L_AJw@TcBR{@FUJWNG"}, "startLocation": {"latLng": {"latitude": 12.779499999999999, "longitude": 77.77061979999999}}, "endLocation": {"latLng": {"latitude": 12.777988299999999, "longitude": 77.7749302}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left"}, "localizedValues": {"distance": {"text": "0.5 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 2421, "staticDuration": "289s", "polyline": {"encodedPolyline": "mu~lAiluyMJ]p@mB@AXcAj@eBDQx@qBd@cANWNUt@aBxC_FPWt@yAx@{AHObCqEpD{HzAqDtAoCf@{@Ra@HQxAaClBiCLOb@g@RUlAkAhA{@XUx@i@h@Yr@]`Bo@fBaApGqDhAu@\\[^Uv@m@f@_@dAeATW|ByC"}, "startLocation": {"latLng": {"latitude": 12.777988299999999, "longitude": 77.7749302}}, "endLocation": {"latLng": {"latitude": 12.7644922, "longitude": 77.7918558}}, "navigationInstruction": {"maneuver": "MERGE", "instructions": "Merge onto NH 4/NH 44/NH 48\nToll road"}, "localizedValues": {"distance": {"text": "2.4 km"}, "staticDuration": {"text": "5 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 16, "staticDuration": "3s", "polyline": {"encodedPolyline": "aa|lAcvxyM?["}, "startLocation": {"latLng": {"latitude": 12.7644922, "longitude": 77.7918558}}, "endLocation": {"latLng": {"latitude": 12.764494299999999, "longitude": 77.7920019}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left"}, "localizedValues": {"distance": {"text": "16 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 452, "staticDuration": "75s", "polyline": {"encodedPolyline": "aa|lA_wxyMb@m@`@i@TYX_@NSbHwIbB{B"}, "startLocation": {"latLng": {"latitude": 12.764494299999999, "longitude": 77.7920019}}, "endLocation": {"latLng": {"latitude": 12.7618563, "longitude": 77.7951729}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at MRF TYRES WHEEL ALGN BALANCE\nPass by TamilNadu RTO Check Post (on the left)"}, "localizedValues": {"distance": {"text": "0.5 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 472, "staticDuration": "55s", "polyline": {"encodedPolyline": "sp{lAyjyyMnH}IfEoElAkA"}, "startLocation": {"latLng": {"latitude": 12.7618563, "longitude": 77.7951729}}, "endLocation": {"latLng": {"latitude": 12.7589486, "longitude": 77.7983423}}, "navigationInstruction": {"maneuver": "STRAIGHT", "instructions": "Continue straight"}, "localizedValues": {"distance": {"text": "0.5 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 18, "staticDuration": "2s", "polyline": {"encodedPolyline": "m~zlAs~yyM^?"}, "startLocation": {"latLng": {"latitude": 12.7589486, "longitude": 77.7983423}}, "endLocation": {"latLng": {"latitude": 12.7587856, "longitude": 77.798345}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right toward NH 4/NH 44/NH 48"}, "localizedValues": {"distance": {"text": "18 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 16562, "staticDuration": "1106s", "polyline": {"encodedPolyline": "m}zlAs~yyMhCkCNOzAeBnAgA|AeAtBmAfAo@jBgAtA_AZWv@q@|AqA`@]nDwCpCaCRQnBsAfEqDfAmAl@u@z@qA|BaEh@aAp@_Bz@iCnB{H~EmRV_A\\qAVw@j@uAhBgDh@iAn@gAp@y@nAoAt@q@VUxAkAxJcHnA{@zAgA\\UROp@g@hAw@n@e@\\Wb@_@TSb@a@RSpA_B~BcDfA{AVa@Zi@\\o@f@mAXo@ZeAf@uBX_BJg@fAyFJe@nAwGH]ZqAb@eBjCyI~@cEJe@VkAR{@R{@Ry@z@uCx@uCZ_A\\aA^{@P_@v@wAPYPYn@aAx@gAHKJO`FsGhBiCj@_AZm@v@kBZ{@Vu@ZkARaAH[Hk@PkARqBFcADeADaAHwARiBt@mDxAoHf@_DDa@R{Bv@}PF_AP}ATqAfAaGFYXsBNgAJeAHkA`@gEVuCD_@Dc@D_@PsBLyAFs@D_@L{AHo@^_DD_@Jg@XmAx@qCx@qBn@oArAwBzA{BbBgCdBeCvFqIT]f@y@|@uAf@w@b@s@j@aALWh@mAJUXs@Lc@x@{CJg@RiAxAkIFYLq@xBuLNw@|CiQNw@n@eDFYH]VkAd@_B\\}@Ti@bAoB|BcE`@q@`@q@r@aAb@e@l@k@bB_Bn@k@pA_Bl@y@l@sA`@}ADWFc@Hu@DeABy@E_ACUKcASeAu@uDSiBAsBJmBz@kFv@eDb@}Aj@eBt@sBnAuC~@eBrBeDdAyA`ByB~AeBXWZW|F_GrAuAbEcEjB{B|AqBjA}AvAeB~CeET[z@qAZq@rBiDh@o@f@k@|B{BbAmAv@kAf@cAtAsCZi@v@kAhCmDhB{Bn@}@zBaC~CaCdF}C~@m@pA{@bBqAn@o@z@{@vBqCbB}BzB}Cj@u@dF_HVc@t@mA^u@Z}@b@_Bt@eDhAoEJa@|AiG"}, "startLocation": {"latLng": {"latitude": 12.7587856, "longitude": 77.798345}}, "endLocation": {"latLng": {"latitude": 12.687685499999999, "longitude": 77.9264549}}, "navigationInstruction": {"maneuver": "MERGE", "instructions": "Merge onto NH 4/NH 44/NH 48\nPass by the lake (on the right in 2.7 km)"}, "localizedValues": {"distance": {"text": "16.6 km"}, "staticDuration": {"text": "18 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 59, "staticDuration": "47s", "polyline": {"encodedPolyline": "aamlAi_szMS[AYJm@"}, "startLocation": {"latLng": {"latitude": 12.687685499999999, "longitude": 77.9264549}}, "endLocation": {"latLng": {"latitude": 12.6877412, "longitude": 77.92695259999999}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left\nPass by Jio-bp pulse Charging Station (on the left)\nDestination will be on the left"}, "localizedValues": {"distance": {"text": "59 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "40.9 km"}, "duration": {"text": "1 hour 3 mins"}, "staticDuration": {"text": "1 hour 3 mins"}}}, {"distanceMeters": 317134, "duration": "18806s", "staticDuration": "18806s", "polyline": {"encodedPolyline": "kaml<PERSON><PERSON>zMH_@TO`@OLAj@}BbBgFt@sC~AgGdA{DZFoAbFkBbHOr@GVy@vDi@lBWdAkAtEUhAeCvJoAdDo@fBqAhCq@dAkCjDqBnCk@v@sCzD{@hAk@v@{@dAiBrBYVk@f@{AfAyBxAw@f@uEpCo@h@cBvAuA~AyD|EW`@iB`CyAfC{@dBs@xA]f@W\\w@z@o@p@k@f@_BfBm@|@eDtFaEnFmDnEu@`AY^i@v@mApAg@h@aB|AqCpC}A`BqBjBcDfDoA~Am@x@iBlC?@kAnBg@|@Yl@oAlCk@|ASj@Y`Ai@nBeA~ESlAMt@SlBAxA@\\Bl@ZnBt@nDN`AFh@FlA?p@?j@MrAGb@Mr@WdAGNa@dAo@fAy@bAmApAu@p@u@r@_Av@e@f@s@fAa@t@mC|E_AbBs@dBc@xAa@tBGTWvAYlAMn@a@xBk@jDk@~Ce@hCOx@iBbK[|AGZk@fDm@dDWvA}@nDSn@Wn@MZs@xAs@lAMTw@nAkBvCU\\{ItM_@h@_AnAkBrCeAnBS\\Yp@a@z@Wt@Qf@q@fCMp@WvAKnAQzACVIv@]bDCZO~AATEZGv@iAtLKfAMhA[lBE\\}@tE_@rBKv@En@Eb@OnCi@fMGz@KbAC`@Mr@]nBuAlHw@lDW~BSrEWxCKbAGXQ|@a@jBiAjDu@`BS\\gAlBaEnF}BzCKNkA~Am@z@OVOTOV]n@Qb@Q`@Q`@Qd@IVUx@oA`EW|@Ux@u@zC]vAOz@y@hDIZgCtI[fAMn@Kd@I\\WjAy@lEKd@gA|FIb@CJa@tBWbAw@`CEJoAhC{@rA_@d@qAlBeAtA{AnBm@j@u@j@kBxAeBjASL[RcD`Ca@Vi@^sHnFyAhAWTs@p@g@f@kAnAe@r@m@nAcA`By@bBQ\\g@pAe@`BU~@]rASx@w@vCqAhF_BnGiAbEoAdDy@zA}@`BiB|CORs@v@qBtBwGfFu@p@eDrCiBzAc@^sC~Bw@n@oElCgCvAm@^aAp@eBfB_A~@OLYX}@|@Gf@UTw@v@g@d@a@^iBnBg@l@a@f@eCrC_FdG}AnBe@l@k@v@sE|Fi@N}@fAkCjDuAnAeChBiBnA_@RuGrDmBbAi@Pw@`@aAh@}@l@yAlAs@t@UVgBxBw@dAu@lAw@tAi@bAaDhH{@fBkCtFm@tA[|@k@vA_A`BUd@c@v@]n@cB`D]j@o@vAIPs@~Ak@vASd@kArB_@x@m@vAM`@EJEPIX]dBKr@_@xCCV[tCgA~IKp@QhAmAtGWxAQ`ACv@YrAs@bCcApCgD|JkA|CgCjHgAbDg@bBm@~Co@|Fm@tGsA~LIh@S|@kCbIoBrFaA~Cc@rBWlBIhBOfMObEAp@SjGOxBIv@QzASrBa@dEMhAu@|FG`@YpAy@nC}@pCAHi@~AsB`GqAzDo@nAe@~@sGnLIPgAvBo@lBm@fC{AvI{BvMQz@eAbGy@vDqA~H_@~ASp@q@dBq@hAk@v@i@l@QPgAv@kJnFu@b@kAz@uBlBcBrBaAbBcBtDg@`Ai@lAWn@eB|Dw@`BSd@q@fBUt@SxAEnA?pA?r@@TAPB~C?v@EdDMjCo@jIEd@Eb@Gj@Il@i@`EQz@G\\Md@k@`B{@bB{@rAi@n@e@d@u@n@[TQLSPwFfEaAx@cBhA_UrPwPdMsCtBqDnCoBxAgAv@wAdAkA|@eAv@k@`@WRKHi@^e@\\k@b@g@\\k@`@e@Zo@h@IFUNyAlA{@t@iDbDKJoBlBWRQPiB`B[^iGxFoAjAYVgA|@]TaBhA{A~@a@XkCrAgDzAsCbAaBd@eBb@eBX}Bh@oAV[Fw@NcGnA{Bd@kB\\kD|@OD}Bn@}DfAeAZu@TmCv@oBj@iAd@mAn@aBbAg@^k@ZgCnBIFKFsCvBoGlEwGzEq@f@oCpBmCjB}@p@UNGFgBnAc@Z_Aj@gC`B]VUZe@f@}AdAkBnAeFnD_D~BcAt@wJjHeE~C_BjAQNa@ZiC`BuBdBNTMFULwDhCKTUZaAhA_@t@MdACv@@b@BxCIdC_@xDe@tDYjBQnAk@tDI^m@vB[`A]z@gAhDe@tA}HzTaAnCaDbI{@vBm@xAi@jAu@lBk@xAaD|Hk@|Aq@lB_BlFoAdFsAzHa@`DMjAYxCMlBQhEGz@AtD@jAHvDLxBDr@V~Bn@rE\\jBf@zBTx@~AbFd@nAp@~AjBzDfB~CzIjPvClFf@|@lBvCnBnC~ApBvEjG|@nAtBxClAjB~AvCrBxEhC~HNn@j@rCl@|DFb@RlBHfAVbDNnBJ~@\\zBX~A^~ALn@XdBPrAHjALrCB`AD|B@bADfDHzG@v@@j@@v@PjL@x@@f@@j@DvCH~AT`B\\nAl@`B\\r@\\n@\\f@fCpC^`@lAtAvA|Bj@pANb@Nd@FP^dBHh@DZDb@DxA?t@IbDGdCCv@IpBQvHGvBA|@KzDIjDQbHAnDH`CFx@PrBZjBnAnFfBtH|@rEf@`DF\\p@lFDr@XlD^rFNfFDvD?tC?n@ArBKlE[|FYrDKlAy@tG{@pFaA`FADGRCL{@nDiArEs@zC{@xEi@vD[dDc@dFc@~Fi@|Fa@tD}@rF{@zEmA|Go@nDi@rCo@hDs@bEiAbGuAtGKl@oCtMm@rCoCnMkAtFq@dDKd@Op@iBdHe@~Bw@fDm@zB[jAaBjFMb@cChJk@hBi@`Bk@jBmDrKaA~CeAdDiAzD}@|CcCvIiFnQ]dAkA~Ci@jAMVoAdCgApBmApBaBpC{CdFgLpRiJpOcDpF}@xAKNaAtAW\\UVmBbBeAp@yBhAi@Ro@TODmCx@eB\\yEzAeA^mGxBQJa@PiAp@mAbAs@n@{@dACHqA~AcAjB_DfEc@l@yBvCaBzBcI~JwAfBaAjAwCvCiChC]\\m@l@sCpCKJKHQT{@v@cDlDcC|CgEjFIJuDvEc@j@mErFmIfKSVu@`AKJmB`C{D`FkC`D_AbAkAfAyApAo@d@c@ViEfCmD|AuA`@wBn@gBf@eAXw@TeAVaBd@oCp@eG~AsCv@iBh@]HyCx@_IlBwB^eBRW@oCF{A?cDOwAM_@EkCa@q@KmJmAc@GyEu@c@GkEq@[EaKwAaDWeEIoJK_EAqFAyIFyCDy@@eHEuMSkEEY?kPBi@?yC?cLQaACuCI_MYyBEcCOiAIo@GoOaBkBQ]EiBM]CeJi@e@C]AWCySkAuBK_HIa@?oDBwDAkB@oEC_QD{E@gC@_R@uMBqB@yO@mFH}ADmLb@uBHwUz@uA?yACwD[m@IqAScE_AiD{@q@OkEu@{Ek@wBWoAO_CY}Dg@}Gy@_@G}AQ}Ca@}Fq@_Da@aDYeES}AEYAmBIsFK}EQkACwCI}CG[A}BGoJ[gFa@cEg@yBYa@GaNeB{Ee@_DUcBMcGW_EAuA?aCCcFHeKh@YB{Hp@mBLcGHiOFgFDaFCoAA{FQcCMaAGkHa@qKk@cBA[AcCCcA@[@{@BePnA_@@}Ep@yDb@sABu@CeCIoBIaBIsHo@uIq@mDSmD]iJgA}AO_@EmAKoCSsDUoC?e@Fm@Vc@ZYb@]bAK`@Id@ETMv@Qd@MPWP}@~ECNM`AMVUnA_@bCGV]xAGRKXOPq@vDc@~BY|AuB`MMv@Q`AwAjIg@fB[z@qAjCeC|EgA|BkM~V_@r@mCzFu@|AUb@yApC{ItQsHrPsA`De@dAUh@a@|@{@hBGLCD}BxEeBxD_NbZe@bAwAvCmDxHqD|H}AtDQf@u@vBIRk@|A_A~BM\\]bAEPERsApF}ApG_A|DQv@y@fDmAfFwAtFeAlE]|AQl@g@|AOf@s@jBsAlDMZwDpKy@~Bk@|Ae@lAiCzG_@dAcA~CoDzJmA~Ce@vAiL~]u@xBy@lCyCzIiBvFgBvFwAfEsD|KkAbEUz@wAvFy@lDSv@Y`Ak@zBsBxHWbAWdAo@dCM`@]jAc@jAsAvCe@fAkAjBg@v@IL{@jAuAxAsGrFcAz@EDkGfFsAhAaEfDgB~A_ChBgAx@uAv@gCjAqA^sN~E}DlA_J~CoA`@aA\\s@\\SL_Bz@i@`@wAt@qA~@iBzAgBvAm@n@sB|BWVk@l@_C~B_A|@gBtAaAv@UPi@f@q@j@iBbBaA`A{GtGmCbCwKdKEDcQhPgD~C{@z@kAfAiItHaGrFqApAo@l@qDbD{CnCUVoClCqArAOL}@~@o@j@gFvDoCpBu@l@iDhCqAfAiCvB_A~@SV}@hAw@hAqAlBo@bA}CtEq@~@e@\\w@fAoB`DcB~ByBbDQXu@bAyEdHmCtDmAzBeBnCg@p@k@z@QRs@|@UZsBnC_@f@qAhBeAtAYZAB_B~AgBdBo@l@QPo@l@i@j@uBdCmAfB_J`Ls@|@qA`Ce@xAcBdEeA|B_@v@kC~E}AdCeErGOVaCtDmD|F}CpEyFfJe@v@iDpFo@`AgBrC_ArAoDrFOVsHjLiDhFaC~DcF|H}FdJiDnFS\\}@xAk@`AMP{AbCkBvCy@rA_@l@_AfB_AnBm@nAmB|D_CzEm@pA{AvCk@jA}DdJ_BvDc@jAaAjDg@|AGN[`AQf@Wt@c@rAq@`BgCtE}@~AyA|BUp@u@tAi@`AaBzCgCxEwB~DS^[l@oBlDo@hA}BxDqA`B]`@oApAeBfBwCtC{AzA{@~@iGhGoDjDmAlAkMrM{@|@eEhEaAz@yAtA}FdGeKpKs@t@_EbEoAnAyA|AKLg@h@_BbBkFfFcF`FsBrByAxAkAjAaC`CkBhB{AzA_B|Aq@p@q@p@k@j@gBdBmFlFaE~Ds@r@mAjAURQRi@f@sFpFcBfB_@^iBdBi@d@cD`DeBdBqApA{AxAiAjAcAbA[Ze@b@uBrB_BdBiBtBqCvCyAbBsAzAMLGFkBhBy@~@aAzAgCfEKReAfB[b@qBxBu@r@iAhAsFfFcA`AqBrBkJjJe@d@ED_CxBc@ZgBbA{@Z}@Vk@Ne@JsYbG_ARiB`@mAb@cCf@oIxASDa@H[HKBg@Nw@XMHq@b@_@ZkArAkLvRiB|CgCrDgGjHkE~EuBjCqIzJe@r@w@tAu@`Ba@|@S`@Sb@_DlHgAjCqApCu@nAy@`A}@|@qDvCm@n@[ZiB|B_@j@eDlFuFzIyFzHiEfFuExF]`@y@dA_DxDcCxCqAzAiBhBoClCsGdGeFfE{ApA{DjD[VqD~Ca@\\eAbAsAnAaBpAe@f@SVs@z@aE|EcAtAo@vAw@|Bs@dCW~@Qd@GPGPm@`Ak@p@cAbA{A`A_G|CcDrB_ChBUXyBlBg@d@s@t@wB~Bg@f@_B~A{CbDkE|EWX{BdC}FjGoDvDu@x@sJlJqBrBuDjESTIJ_@`@_@b@qCfCqAhA]X{@l@y@h@uHvE_Ah@uAz@{D`C_Ah@yBvAk@b@k@d@cAlAsAvBc@~@{AtC{DxHS\\qCrFeBdDe@|@oAdCi@r@aAnBi@jAe@bA{@`CCHSn@[z@Od@[fAcAdCk@jAc@z@W`@iCjEcDdFi@z@[h@kAnBwAlCaCbES\\U\\o@fAi@|@eElGuAtBqFfJQZkBfDmCxE}@~Ae@~@sCbFc@z@qElIYf@U`@mDrFoBdCw@t@sAbAYRe@ZyCbBOJi@ZsEnCqAv@]RWTg@r@Od@Ib@GnA?\\?fBCdBIlAWvAUx@_@|@Uh@o@`A]b@q@n@y@n@uKpHu@l@e@\\CBsBxAcCdB}B~Ac@\\cAv@MHoEzD}GlGuApA{EhEwG|FkEpDeBtAaAl@yB|@y@VcATqAXeIpByBj@mCp@gCl@uBh@cHdBk@NkAXWFYFc@L{A^cHbByCt@[FKHqBn@oCp@cCd@{@TiGzAgB`@yA^e@La@HyBf@sA\\gE`Ai@N{@RuIzBqA\\oEfAsD~@eAVgCn@o@PyFrAk@PqLvCuC|@c@J]HuA`@iB^eCn@iDt@]JgKfCmAZGBa@JkBd@uCv@wD~@yC|@uCbAgCfAwDrB_BbAwA`AsBbBkBbBcClCiApAw@nAcBdCa@t@_@l@EFYr@yCzGOZYl@Wp@eCdG[p@mAtCy@dCo@hAsEvKcFlL_CvFCHu@`BmBpEe@dA_AjBm@fAwAjBg@h@iA`Aw@l@y@f@}Az@{@\\eA\\}@h@OFyBn@aBf@mDjAgGlBaFvA{Af@{@XwAb@c@Lw@BiAX_]`KgK~CeF|AiEpAeCr@qEtA{Bt@eGrBgC~@oBj@kAb@kN|EiA`@sKxDoCdAq@T_Bj@{@ZqDlAMFyCdA}Aj@QDw@XwAp@o@\\OHy@f@_DdCqB`BkGfF{JdIOJ]ZsKzImGdFs@j@URsIbHuBdBqJzHcAx@iGdF[VC@_FbEyGnFsC`C{@t@aGpEOLeDhCcDfC{L|JcFdE}HrG_F`E{FvE_Ap@aCpAaDzAYLu@^uBnA_CjA{XhM}C|AIDiRxIwLrFuG~CeAd@mAj@gAl@c@Z[T{AdAuEzDqOrNwJ~I{@x@_CxBeEvDmAfAcAn@_EnDuEfEyBjBmErCu@d@yIvF}E|Ca@Xo@f@{@v@yDtEgBdBaCdB{BfAiK~DuJlDeBp@kCpA_C|AiA`AiFlFmMzMcDhDiAtAmCtDw@nAqAvBa@v@mAlC{AvDWv@iEzLaAnCgA~Cu@nBy@hBu@rAcA|Au@dAgAjAaKbJwAhAiBnAsAr@sAl@iBp@mA`@sF|AmXzHsA^kLzCiBj@cG`BgAZ}C~@eGhBmF~As@TaAXkBj@a@LgAZwW`He@L_M~CkJdCwKxCSFyCz@wEtA_AViAXSD_@LoB\\yANw@BqBG_DU}@Mk@G]E_AK}AKcD@uAP_B\\oC~@wKbEkOtFeC~@uAn@aDfA_EfBiDbBaCnAkEfCyFvCgBr@mBl@}DjA_Ez@iBXuBVqDZ_CVaDj@aDl@{Bp@UFcDhA_IzCoDzAyFlCeE|Aa@TgPpH_@P_JbEcAd@q@ZoCpAgCjAUJaH~CyAp@cD|AaGnCc@RyAn@}LvF]Nc@RoClA}Ax@gAh@wIzDYNYL{BbA}EzBs@\\ULgOvH}Ax@{CvAcErByDjBeD~A{BhA{BhA{Az@mFdDuErCuErCeI`FyEpCqSlM]TiDrBeRpLaF|CeNpIiFbDkEhC{ZfR{BxAGL_HxDeBfAwBtAe@ZWLwBpAoHlEqG~DwM`IeMhIcMpIgNdJiI`GwBxAuDvB_D~ASJ[NaEbBUJkCdAyEjBo@VeIbDOFu@XeBp@mAd@{DzAuB|@aA\\}B~@cMrEgGzBwK`EwEdBsFpB{E~Aa@LyGvBODOD}FlByJbDc@L{Br@_@LyAf@uBv@iDzAyHxDi@XkL~F_CjAqAp@y@b@yCzAwC|Ac@TgDjBu@b@mPhKaBbA_@ToG~DqJbGqDpBuAj@QHyCbAkDz@[HwB\\iKnAaBRmD`@cAJi@FcMvAkALyAP[BSBcCViIt@sBPkEZy@DcEVoI\\oBF_ABeI\\aKZ_ENY@uERwKp@sHd@gKl@{@DO@uF\\oCLoCDgCBmGDkFBuFDkEBsMJi@B_BJcBPq@HyCd@uATmAXqAb@e@Ry@`@uA`AmAlACBGFu@x@cBjBMPu@z@gHhIKNkArAcAjAwA~AyApBoAvAuOnQcBrB{DjFiAjAONmAjAqCtCcFzFgFbGiFdGsGtHSTkC~CwA~AWX}@x@uBzAaBt@iFbBmBl@m@PwT|Gk@P[JwLrDqAh@cU`H[JcP`FmBx@eHlDaAh@kBdA[RcAv@_BdBw@nAU`@qEvIS^cApB{DrHk@`Ac@v@gKbS{BjEe@|@uHrNu@vA}AbD_@dAcAjDq@pCeCpKW|@u@fDa@hBw@bDg@rBkEpQyIx^}CnMiAxEK`@St@_@jAm@|Ag@bAk@`AsB~CmEnFiDdEgBtB_C~CWZY`@OXk@|@kAlBkAdCyA|D{IjUgCvGeCpGCFEHIVaD|HaHtPgBpEMVwEbLaHvQyArDaEhKIRoAvCw@bBaK~Sg@fAqDdIyBbFQf@}ClIm@`BITeAhCu@fBiAjCkBpDgBfD}EdIQXoDbFq@z@{@z@m@b@eAv@eAp@_B|@{@b@s@`@e@Z{@j@_CnBiCfC_AbAwAxAwA~A{@x@c@\\_Ax@iC|BsDvC{@n@qDrCsEnDc@^uBdB{@n@s@h@eA`A_ExEcCrC_HdIyClDiFhG{J|KaAfA{DvE_IhJuDvEqAzAq@z@iBpBwCpDcAnAmClDUZsB|BmL~NyC|Dw@fAaJhM{@vAeDvGwAvCkEdJ_ApBeA|BwA~CcAfBuHtOsAtCqAhCWh@_A`BiAlBaQxYiB~Ck@jAiAnC]bAq@hCi@~B}BpMm@rDe@`COr@gBtIWlAyB|KGZsDvQw@bEg@hCk@bCMh@]hB_@tAETkAtD{BrG}AbFsBhGwEtM}AfEuA~C{@vAcApAs@v@c@b@qAjAyEbD{LnIuB|AsBpB}B|CuJfN_DdEoFpGgEfFqBlBwApA_Ax@aBrAkCzBoAfAmB~AqAhA}CjCkA~@_GjF}HvHyAzAkGdGq@p@wGbHiYzZsLnMkPxQgDjDy@`AGx@cAtAuAvB[j@IPWp@OX]v@sArBc@h@ONg@f@cCxDuBdDqAjBa@b@cBpBi@?k@l@eNlLqB|A}AvAaFfEuCdCyEpEaF`F}EzEqGjGiAfAeAhAyNnNqApAqGhGmElEwBrBwAvAeCzBuCzBoClB_SvNwAjAc@d@qAxAwDbFaBzBoDbFeDrEY^eCnDuB`C_A~@uCfCeDjCaBvAeBzAyAvAKL_ChCgBpBIHoEhEa@`@i@f@i@f@iBfBiBhBIHsAbBs@bAOTEFYf@i@hA{@xBwChKi@rBsClKGViBxHIXETuAxG{AhH_@lBaFzUi@|BgAtFy@bCaCdFKTc@~@_@x@gA`CiAvBg@r@a@f@{@z@}EhEwDdDw@h@YXWVq@l@wDxCqDdDkCxByDbDmBnB]Zk@h@o@p@wEtEuAnAg@^}At@{@Vq@Le@Hs@F{ABqAG}@MeBe@[K_A_@qD}AIEgEgBOGmDwAwGwB}@Qk@E{A?c@BeARmA^qHlCuCjA_CbAu@Xa@NaCbAmCbAcDpAwCfAyN~FsDbB]Pm@^]Xe@^k@j@uAlBe@~@k@xAe@fBW`BKtB?jBFbAl@pK@`BC`@QfAMd@IRIR_@j@SV[^[XoAv@sLnFe@RQH{IvDiF`CYJuIxDcBp@{BdA}Ax@EBiDlBoDtBmAr@sBlAGB_HdEcSjLGBiErCqJxFs\\dSsO`J_KbGgI|EsGzDkAn@_LxGoBbAkAd@qD~@yB\\cTpBaGj@oCNcBP_Iv@}AVQDODoIrBgA`@yEz@mEh@yR|BkFr@aBZcD`AoNhGqBz@_n@jX_Y|LeC`AqBh@{AXuBRuRxA_Il@uDl@cAViBl@g@RgB~@qKnGYPiCzAyCvBy@z@sHvHwUpWmDxCyAhAuD|C_K~HeLhJeBlA}@x@s@j@oBfB}ApAsElCaDtA}LvFug@bUoB`AuFdCyDhBqBz@}At@cIrDgDvAyHlDe@TyAp@}CvAuDjBqEpByCvAuBhAoEvCcAf@gBjA}AlAcBbAuCrBiIlFmNnJwEfDcGzDgAt@wA`AmCdB}LdIo@^yCxB}@t@yBtBmCvBiMnMmEvEuBlBQPk@j@yDpDwAlAeBpAQLmDpB}CtAq@ViC`Am@PkH|B}FxBoKnDgE~AkBl@cFlBaBp@gJbDcExAa@LeA`@{Ap@s@`@oAx@m@b@cCpBgMrMmEpEuBnBu@t@gLvL{@bAUXoArAoAlA}AnBs@bAiAjBiBlD]r@oAdCgBpD{ClGuAvBsBlCeLhMkDbEoI|JaBpBaFjGeIvJ{GfIyCvDkEdF}AxAuA`AaF|CaAf@gEfCo@`@iJ~FmAl@cCpAoC|AqHrDSLo@ZaG|CiAh@aBt@eBr@{DnAgBh@{Af@gF~AkA`@iIfCqCfAwAt@eCdBmCrB}L|JiJnHmC~BkEhDiCrB_ClB[V}ApAoDpCSPq@j@gFlEsAjAKHQN{@r@a@Z{BdBmChBMHo@\\{@f@iCdAs@Xe@NmBf@k@N{@NwAVaAJeBRsFp@}AN{Gp@eAJ{SrBSBcLjAmCXM@M@uBXgALmE|@uBd@wD`AoG`Bg@Lm@P_@HeBd@eBd@oBh@yCz@}@RgAVSD}@PgEp@aE^aCLoDJe@@yDFuAAgA?O?]Aa@AeLa@wE@m@?a@@mCByEVgBNcJz@m@HqJbA_J`AkFf@I@qD`@_BZaB^oBl@cC`AgAd@}A~@qEvCgAr@oCdBeEnC}@j@oCdBqFnD}CjBuA|@kMfImBlAs@d@{GlEi@\\uA|@s@d@cFfDsA~@o@f@YZa@d@OTk@z@qAvBoDtHi@z@uAtBaAxAwBfDeAdBw@jAW\\_BzB{ArB}FlIwDpFqBrCUX_NzR}EfHcBbCo@|@wBjDINm@hAKR[l@c@|@Wf@mC~FmFnKyGxMeBnDeAhBU^eA|AaAnAcBhB{BjB_Ar@mClByC|Bo@f@{GdFoHvFcAz@kDfCmAdAuB~A]VkBvAmG`FkHjFiF`EKH{ElDiB|AsBfCORsAjBg@|@oApC_AzCYvAIb@UxAK|@IfAG~@Cx@AlBAZBzD@d@?n@?|@?b@Ax@?`GAjHAj@B|I@rQ?fD?p@@J?xBAfC@pG?l@@nR?@?~C?bB?jCBpQ?xHFvB?VPxHFlATpEV`Ed@lGf@hFHj@^~Cd@nDZ|BnArH`@|BH`@XvATnA|EpXJd@Nz@bA`G|AtIdA~Ft@bE\\pCNjBDpCAnC?l@An@ShUElHGtD?J?JC`AIzBKhBANW`Dk@hEa@dCuAdGi@nBgDbKa@hAkBnF[~@M\\aBbFw@xB}AlE]`AmMn\\ITQb@oB`FkBzEQb@sFrNiM|[yEvL}@`C}CzHqJzV}A`EsBzF}BdGSd@Yh@{DbK]|@k@xAKVsAlDkA~CmApDqA`DQb@oCpHwBxFQh@qDrJ_@`BeG~OmI`TYv@_ItSq@lBoAhDOb@kEdLqGbQy@tB{JnU{HbRsHlQcDzHQ^yDfJqGhOyDhJgBhEyBjFeCrF_HrNsEpJgA|BcF|JsGnMuCrFe@z@gEjHcFlI_BtCmIdNeDpFwFlJeDpFe@x@mGjK{G~KgX~c@cFlIqArBwOxVmDdFyHtKyF`IwSvYaC|CkBtBkMfOo@t@}FpFeAt@{DjCuLrHg@XqD`CyFdDi@XOHmEpBi@RmAd@uCpAcAd@}An@oJxD]NcIhDyClAu@\\iAd@gDrAo@VaBt@sF`C{Af@]HoCn@qCr@c@J_@HyA`@qCdAMFiCvAuB|AcA`A}@bAiAzAILILwAlCe@dA{DvIa@`AmAnCg@dA{@dBS^k@`AMNa@n@Yd@kDxF_@j@c@r@_GtJcAnBIPk@`B_@tAUpAQxACTKpAG~A@pB@h@HnAjAbJNhAh@zDPzAfBnN\\nCJl@vA|Gz@rDhE`RLj@Np@|B~JH\\nApFd@tCNzBDdBA`CG`BIdB_AlREbA?NC^a@lQMzFEhBIhFCd@MtCM|BUpCSnBQdBiAdLsBxRgA~KC`@Eb@OfBK`AWdCw@~H}A`PCVGj@{@rICZM~AMbAqApMUhCGn@Ip@QhBe@jEU`Ce@zE]vCSrBGf@SlBCVy@rHe@jDe@pDuAfK_AlHk@nDwArHaAbFq@hEaD~P_@zB_AfFcAnFe@dCo@lDwB|LcAfGObBGdAa@rJIbBOdAUrASdAcFvPoFrQMb@aF`P}@tCu@jCqAlEkEzMkAlDcCjIYtAO|@QdBC\\IbBHnRF|EBxF?~@BfCCpDOxBQ~Ac@nBiAnD_D`JyFbPw@zBg@`BoCrH}ArEy@xBcAzD]`CKrAc@zJSzFMrCa@rD_@|B{@pD_BdGQn@mCxKiArEq@jCu@dEG`@Ij@}A~JuCnRSnAWzAQ~@iCvOCNk@~DgAhHm@tD]fB_@bC{AzJuBzLgA~Gm@tD{@bFg@|CU|Ao@|Hw@rK]vFQ`BYdBo@zCGZoBfIq@vCcFxTOp@u@bDoDrOoFlUkCnKkApEgArEWdAo@pBw@hBmAbCaEvHiD~GsDdGkDxESVaF|GwH|JcElF_AdAw@|@e@f@m@p@iAjAkKnK}KfLkEbEeGlGJNoGtG_ChCcDhEsBtC_EpFKPyArB{AxBg@x@]j@[j@gApBe@v@nA|@Ve@TpB"}, "startLocation": {"latLng": {"latitude": 12.6877412, "longitude": 77.92695259999999}}, "endLocation": {"latLng": {"latitude": 14.427665499999998, "longitude": 75.9689837}}, "steps": [{"distanceMeters": 61, "staticDuration": "22s", "polyline": {"encodedPolyline": "kamlAmbszMH_@TO`@OLA"}, "startLocation": {"latLng": {"latitude": 12.6877412, "longitude": 77.92695259999999}}, "endLocation": {"latLng": {"latitude": 12.6873437, "longitude": 77.927278}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head east toward NH 4/NH 44/NH 48"}, "localizedValues": {"distance": {"text": "61 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 558, "staticDuration": "91s", "polyline": {"encodedPolyline": "{~llA<PERSON>zMj@}BbBgFt@sC~AgGdA{D"}, "startLocation": {"latLng": {"latitude": 12.6873437, "longitude": 77.927278}}, "endLocation": {"latLng": {"latitude": 12.6855151, "longitude": 77.9320709}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left onto NH 4/NH 44/NH 48"}, "localizedValues": {"distance": {"text": "0.6 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 17201, "staticDuration": "1254s", "polyline": {"encodedPolyline": "osllAmbtzMZFoAbFkBbHOr@GVy@vDi@lBWdAkAtEUhAeCvJoAdDo@fBqAhCq@dAkCjDqBnCk@v@sCzD{@hAk@v@{@dAiBrBYVk@f@{AfAyBxAw@f@uEpCo@h@cBvAuA~AyD|EW`@iB`CyAfC{@dBs@xA]f@W\\w@z@o@p@k@f@_BfBm@|@eDtFaEnFmDnEu@`AY^i@v@mApAg@h@aB|AqCpC}A`BqBjBcDfDoA~Am@x@iBlC?@kAnBg@|@Yl@oAlCk@|ASj@Y`Ai@nBeA~ESlAMt@SlBAxA@\\Bl@ZnBt@nDN`AFh@FlA?p@?j@MrAGb@Mr@WdAGNa@dAo@fAy@bAmApAu@p@u@r@_Av@e@f@s@fAa@t@mC|E_AbBs@dBc@xAa@tBGTWvAYlAMn@a@xBk@jDk@~Ce@hCOx@iBbK[|AGZk@fDm@dDWvA}@nDSn@Wn@MZs@xAs@lAMTw@nAkBvCU\\{ItM_@h@_AnAkBrCeAnBS\\Yp@a@z@Wt@Qf@q@fCMp@WvAKnAQzACVIv@]bDCZO~AATEZGv@iAtLKfAMhA[lBE\\}@tE_@rBKv@En@Eb@OnCi@fMGz@KbAC`@Mr@]nBuAlHw@lDW~BSrEWxCKbAGXQ|@a@jBiAjDu@`BS\\gAlBaEnF}BzCKNkA~Am@z@OVOTOV]n@Qb@Q`@Q`@Qd@IVUx@oA`EW|@Ux@u@zC]vAOz@y@hDIZgCtI[fAMn@Kd@I\\WjAy@lEKd@gA|FIb@CJa@tBWbAw@`CEJoAhC{@rA_@d@qAlBeAtA{AnBm@j@u@j@kBxAeBjASL[RcD`Ca@Vi@^sHnFyAhAWTs@p@g@f@kAnAe@r@m@nAcA`By@bBQ\\g@pAe@`BU~@]rASx@w@vCqAhF_BnGiAbEoAdDy@zA}@`BiB|CORs@v@qBtBwGfFu@p@eDrCiBzAc@^sC~Bw@n@oElCgCvAm@^aAp@eBfB_A~@OLYX}@|@"}, "startLocation": {"latLng": {"latitude": 12.6855151, "longitude": 77.9320709}}, "endLocation": {"latLng": {"latitude": 12.7584629, "longitude": 77.7985391}}, "navigationInstruction": {"maneuver": "UTURN_RIGHT", "instructions": "Make a U-turn\nPass by the lake (on the left in 14.2 km)"}, "localizedValues": {"distance": {"text": "17.2 km"}, "staticDuration": {"text": "21 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 917, "staticDuration": "126s", "polyline": {"encodedPolyline": "k{zlA{_zyMGf@UTw@v@g@d@a@^iBnBg@l@a@f@eCrC_FdG}AnBe@l@k@v@sE|F"}, "startLocation": {"latLng": {"latitude": 12.7584629, "longitude": 77.7985391}}, "endLocation": {"latLng": {"latitude": 12.763886399999999, "longitude": 77.7922164}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left"}, "localizedValues": {"distance": {"text": "0.9 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 25, "staticDuration": "3s", "polyline": {"encodedPolyline": "i}{lAkxxyMi@N"}, "startLocation": {"latLng": {"latitude": 12.763886399999999, "longitude": 77.7922164}}, "endLocation": {"latLng": {"latitude": 12.764101499999999, "longitude": 77.7921405}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_RIGHT", "instructions": "Slight right toward NH 4/NH 44/NH 48"}, "localizedValues": {"distance": {"text": "25 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 3519, "staticDuration": "351s", "polyline": {"encodedPolyline": "s~{lA{wxyM}@fAkCjDuAnAeChBiBnA_@RuGrDmBbAi@Pw@`@aAh@}@l@yAlAs@t@UVgBxBw@dAu@lAw@tAi@bAaDhH{@fBkCtFm@tA[|@k@vA_A`BUd@c@v@]n@cB`D]j@o@vAIPs@~Ak@vASd@kArB_@x@m@vAM`@EJEPIX]dBKr@_@xCCV[tCgA~IKp@QhAmAtGWxAQ`ACv@YrAs@bCcApC"}, "startLocation": {"latLng": {"latitude": 12.764101499999999, "longitude": 77.7921405}}, "endLocation": {"latLng": {"latitude": 12.7803675, "longitude": 77.7655525}}, "navigationInstruction": {"maneuver": "MERGE", "instructions": "Merge onto NH 4/NH 44/NH 48\nToll road"}, "localizedValues": {"distance": {"text": "3.5 km"}, "staticDuration": {"text": "6 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 14333, "staticDuration": "1213s", "polyline": {"encodedPolyline": "id_mAuqsyMgD|JkA|CgCjHgAbDg@bBm@~Co@|Fm@tGsA~LIh@S|@kCbIoBrFaA~Cc@rBWlBIhBOfMObEAp@SjGOxBIv@QzASrBa@dEMhAu@|FG`@YpAy@nC}@pCAHi@~AsB`GqAzDo@nAe@~@sGnLIPgAvBo@lBm@fC{AvI{BvMQz@eAbGy@vDqA~H_@~ASp@q@dBq@hAk@v@i@l@QPgAv@kJnFu@b@kAz@uBlBcBrBaAbBcBtDg@`Ai@lAWn@eB|Dw@`BSd@q@fBUt@SxAEnA?pA?r@@TAPB~C?v@EdDMjCo@jIEd@Eb@Gj@Il@i@`EQz@G\\Md@k@`B{@bB{@rAi@n@e@d@u@n@[TQLSPwFfEaAx@cBhA_UrPwPdMsCtBqDnCoBxAgAv@wAdAkA|@eAv@k@`@WRKHi@^e@\\k@b@g@\\k@`@e@Zo@h@IFUNyAlA{@t@iDbDKJoBlBWRQPiB`B[^iGxFoAjAYVgA|@]TaBhA{A~@a@XkCrAgDzAsCbAaBd@eBb@eBX}Bh@oAV[Fw@NcGnA{Bd@kB\\kD|@OD}Bn@}DfAeAZu@TmCv@oBj@iAd@mAn@aBbAg@^k@ZgCnBIFKFsCvBoGlEwGzEq@f@oCpBmCjB}@p@UNGFgBnAc@Z_Aj@gC`B]VUZe@f@}AdAkBnAeFnD_D~BcAt@wJjH"}, "startLocation": {"latLng": {"latitude": 12.7803675, "longitude": 77.7655525}}, "endLocation": {"latLng": {"latitude": 12.852980599999999, "longitude": 77.6667293}}, "navigationInstruction": {"maneuver": "STRAIGHT", "instructions": "Continue straight to stay on NH 4/NH 44/NH 48\nPass by Pillar Number 8 (on the right in 13.5 km)"}, "localizedValues": {"distance": {"text": "14.3 km"}, "staticDuration": {"text": "20 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 423, "staticDuration": "47s", "polyline": {"encodedPolyline": "cjmmAah`yMeE~C_BjAQNa@ZiC`BuBdB"}, "startLocation": {"latLng": {"latitude": 12.852980599999999, "longitude": 77.6667293}}, "endLocation": {"latLng": {"latitude": 12.8559851, "longitude": 77.6643327}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "Continue onto NH 4/NH 44/NH 48"}, "localizedValues": {"distance": {"text": "0.4 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 14, "staticDuration": "12s", "polyline": {"encodedPolyline": "}|mmAay_yMNT"}, "startLocation": {"latLng": {"latitude": 12.8559851, "longitude": 77.6643327}}, "endLocation": {"latLng": {"latitude": 12.8559105, "longitude": 77.6642249}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left after ROSHNI PAN AND TEA STALL (on the left)"}, "localizedValues": {"distance": {"text": "14 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 150, "staticDuration": "31s", "polyline": {"encodedPolyline": "m|mmAkx_yMMFULwDhC"}, "startLocation": {"latLng": {"latitude": 12.8559105, "longitude": 77.6642249}}, "endLocation": {"latLng": {"latitude": 12.8570146, "longitude": 77.6634248}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right onto Mumbai Hwy/Service Rd\nPass by Pillar Number 212 (on the right)"}, "localizedValues": {"distance": {"text": "0.2 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 294, "staticDuration": "35s", "polyline": {"encodedPolyline": "icnmAks_yMKTUZaAhA_@t@MdACv@@b@BxC"}, "startLocation": {"latLng": {"latitude": 12.8570146, "longitude": 77.6634248}}, "endLocation": {"latLng": {"latitude": 12.857726999999999, "longitude": 77.6609539}}, "navigationInstruction": {"maneuver": "RAMP_LEFT", "instructions": "Take the ramp to NH 48"}, "localizedValues": {"distance": {"text": "0.3 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 40560, "staticDuration": "2441s", "polyline": {"encodedPolyline": "ygnmA}c_yMIdC_@xDe@tDYjBQnAk@tDI^m@vB[`A]z@gAhDe@tA}HzTaAnCaDbI{@vBm@xAi@jAu@lBk@xAaD|Hk@|Aq@lB_BlFoAdFsAzHa@`DMjAYxCMlBQhEGz@AtD@jAHvDLxBDr@V~Bn@rE\\jBf@zBTx@~AbFd@nAp@~AjBzDfB~CzIjPvClFf@|@lBvCnBnC~ApBvEjG|@nAtBxClAjB~AvCrBxEhC~HNn@j@rCl@|DFb@RlBHfAVbDNnBJ~@\\zBX~A^~ALn@XdBPrAHjALrCB`AD|B@bADfDHzG@v@@j@@v@PjL@x@@f@@j@DvCH~AT`B\\nAl@`B\\r@\\n@\\f@fCpC^`@lAtAvA|Bj@pANb@Nd@FP^dBHh@DZDb@DxA?t@IbDGdCCv@IpBQvHGvBA|@KzDIjDQbHAnDH`CFx@PrBZjBnAnFfBtH|@rEf@`DF\\p@lFDr@XlD^rFNfFDvD?tC?n@ArBKlE[|FYrDKlAy@tG{@pFaA`FADGRCL{@nDiArEs@zC{@xEi@vD[dDc@dFc@~Fi@|Fa@tD}@rF{@zEmA|Go@nDi@rCo@hDs@bEiAbGuAtGKl@oCtMm@rCoCnMkAtFq@dDKd@Op@iBdHe@~Bw@fDm@zB[jAaBjFMb@cChJk@hBi@`Bk@jBmDrKaA~CeAdDiAzD}@|CcCvIiFnQ]dAkA~Ci@jAMVoAdCgApBmApBaBpC{CdFgLpRiJpOcDpF}@xAKNaAtAW\\UVmBbBeAp@yBhAi@Ro@TODmCx@eB\\yEzAeA^mGxBQJa@PiAp@mAbAs@n@{@dACHqA~AcAjB_DfEc@l@yBvCaBzBcI~JwAfBaAjAwCvCiChC]\\m@l@sCpCKJKHQT{@v@cDlDcC|CgEjFIJuDvEc@j@mErFmIfKSVu@`AKJmB`C{D`FkC`D_AbAkAfAyApAo@d@c@ViEfCmD|AuA`@wBn@gBf@eAXw@TeAVaBd@oCp@eG~AsCv@iBh@]HyCx@_IlBwB^eBRW@oCF{A?cDOwAM_@EkCa@q@KmJmAc@GyEu@c@GkEq@[EaKwAaDWeEIoJK_EAqFAyIFyCDy@@eHEuMSkEEY?kPBi@?yC?cLQaACuCI_MYyBEcCOiAIo@GoOaBkBQ]EiBM]CeJi@e@C]AWCySkAuBK_HIa@?oDBwDAkB@oEC_QD{E@gC@_R@uMBqB@yO@mFH}ADmLb@uBHwUz@uA?yACwD[m@IqAScE_AiD{@q@OkEu@{Ek@wBWoAO_CY}Dg@}Gy@_@G}AQ}Ca@}Fq@_Da@aDYeES}AEYAmBIsFK}EQkACwCI}CG[A}BGoJ[gFa@cEg@yBYa@GaNeB{Ee@_DUcBMcGW_EAuA?aCCcFHeKh@YB{Hp@mBLcGHiOFgFDaFCoAA{FQcCMaAGkHa@qKk@cBA[AcCCcA@[@{@BePnA_@@}Ep@yDb@sABu@CeCIoBIaBIsHo@uIq@mDSmD]iJgA}AO_@EmAKoCSsDU"}, "startLocation": {"latLng": {"latitude": 12.857726999999999, "longitude": 77.6609539}}, "endLocation": {"latLng": {"latitude": 13.054227099999999, "longitude": 77.4767267}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "Continue onto NH 48\nToll road\nDrive along the lake (on the left for 280m)"}, "localizedValues": {"distance": {"text": "40.6 km"}, "staticDuration": {"text": "41 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 358, "staticDuration": "42s", "polyline": {"encodedPolyline": "}stnAqd{wMoC?e@Fm@Vc@ZYb@]bAK`@Id@ETMv@Qd@MPWP"}, "startLocation": {"latLng": {"latitude": 13.054227099999999, "longitude": 77.4767267}}, "endLocation": {"latLng": {"latitude": 13.056324499999999, "longitude": 77.4747871}}, "localizedValues": {"distance": {"text": "0.4 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 171, "staticDuration": "28s", "polyline": {"encodedPolyline": "_aunAmxzwM}@~ECNM`A"}, "startLocation": {"latLng": {"latitude": 13.056324499999999, "longitude": 77.4747871}}, "endLocation": {"latLng": {"latitude": 13.056721099999999, "longitude": 77.4732633}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "Continue onto NH 48"}, "localizedValues": {"distance": {"text": "0.2 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 45092, "staticDuration": "3040s", "polyline": {"encodedPolyline": "ocunA{nzwMMVUnA_@bCGV]xAGRKXOPq@vDc@~BY|AuB`MMv@Q`AwAjIg@fB[z@qAjCeC|EgA|BkM~V_@r@mCzFu@|AUb@yApC{ItQsHrPsA`De@dAUh@a@|@{@hBGLCD}BxEeBxD_NbZe@bAwAvCmDxHqD|H}AtDQf@u@vBIRk@|A_A~BM\\]bAEPERsApF}ApG_A|DQv@y@fDmAfFwAtFeAlE]|AQl@g@|AOf@s@jBsAlDMZwDpKy@~Bk@|Ae@lAiCzG_@dAcA~CoDzJmA~Ce@vAiL~]u@xBy@lCyCzIiBvFgBvFwAfEsD|KkAbEUz@wAvFy@lDSv@Y`Ak@zBsBxHWbAWd<PERSON>o@dCM`@]jAc@jAsAvCe@fAkAjBg@v@IL{@jAuAxAsGrFcAz@EDkGfFsAhAaEfDgB~A_ChBgAx@uAv@gCjAqA^sN~E}DlA_J~CoA`@aA\\s@\\SL_Bz@i@`@wAt@qA~@iBzAgBvAm@n@sB|BWVk@l@_C~B_A|@gBtAaAv@UPi@f@q@j@iBbBaA`A{GtGmCbCwKdKEDcQhPgD~C{@z@kAfAiItHaGrFqApAo@l@qDbD{CnCUVoClCqArAOL}@~@o@j@gFvDoCpBu@l@iDhCqAfAiCvB_A~@SV}@hAw@hAqAlBo@bA}CtEq@~@e@\\w@fAoB`DcB~ByBbDQXu@bAyEdHmCtDmAzBeBnCg@p@k@z@QRs@|@UZsBnC_@f@qAhBeAtAYZAB_B~AgBdBo@l@QPo@l@i@j@uBdCmAfB_J`Ls@|@qA`Ce@xAcBdEeA|B_@v@kC~E}AdCeErGOVaCtDmD|F}CpEyFfJe@v@iDpFo@`AgBrC_ArAoDrFOVsHjLiDhFaC~DcF|H}FdJiDnFS\\}@xAk@`AMP{AbCkBvCy@rA_@l@_AfB_AnBm@nAmB|D_CzEm@pA{AvCk@jA}DdJ_BvDc@jAaAjDg@|AGN[`AQf@Wt@c@rAq@`BgCtE}@~AyA|BUp@u@tAi@`AaBzCgCxEwB~DS^[l@oBlDo@hA}BxDqA`B]`@oApAeBfBwCtC{AzA{@~@iGhGoDjDmAlAkMrM{@|@eEhEaAz@yAtA}FdGeKpKs@t@_EbEoAnAyA|AKLg@h@_BbBkFfFcF`FsBrByAxAkAjAaC`CkBhB{AzA_B|Aq@p@q@p@k@j@gBdBmFlFaE~Ds@r@mAjAURQRi@f@sFpFcBfB_@^iBdBi@d@cD`DeBdBqApA{AxAiAjAcAbA[Ze@b@uBrB_BdBiBtBqCvCyAbBsAzAMLGFkBhBy@~@aAzAgCfEKReAfB[b@qBxBu@r@iAhAsFfFcA`AqBrBkJjJe@d@ED_CxBc@ZgBbA{@Z}@Vk@Ne@JsYbG_ARiB`@mAb@cCf@oIxASDa@H[HKBg@Nw@XMHq@b@_@ZkArAkLvRiB|CgCrDgGjHkE~EuBjCqIzJe@r@w@tAu@`Ba@|@S`@Sb@_DlHgAjCqApCu@nAy@`A}@|@qDvCm@n@[ZiB|B_@j@eDlFuFzIyFzHiEfFuExF]`@y@dA_DxDcCxCqAzAiBhBoClCsGdGeFfE{ApA{DjD[VqD~Ca@\\eAbAsAnAaBpAe@f@SVs@z@aE|EcAtAo@vAw@|Bs@dCW~@Qd@GPGPm@`Ak@p@cAbA{A`A_G|CcDrB_ChBUXyBlBg@d@s@t@wB~Bg@f@_B~A{CbDkE|EWX{BdC}FjGoDvDu@x@sJlJqBrBuDjESTIJ_@`@_@b@qCfCqAhA]X{@l@y@h@uHvE_Ah@uAz@{D`C_Ah@yBvAk@b@k@d@cAlAsAvBc@~@{AtC{DxHS\\qCrFeBdDe@|@oAdCi@r@aAnBi@jAe@bA{@`CCHSn@[z@Od@[fAcAdCk@jAc@z@W`@iCjEcDdFi@z@[h@kAnBwAlCaCbES\\U\\o@fAi@|@eElGuAtBqFfJQZkBfDmCxE"}, "startLocation": {"latLng": {"latitude": 13.056721099999999, "longitude": 77.4732633}}, "endLocation": {"latLng": {"latitude": 13.3060946, "longitude": 77.1557608}}, "navigationInstruction": {"maneuver": "RAMP_RIGHT", "instructions": "Slight right to merge onto NH 4/NH 48\nToll road"}, "localizedValues": {"distance": {"text": "45.1 km"}, "staticDuration": {"text": "51 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 11098, "staticDuration": "753s", "polyline": {"encodedPolyline": "azepAon|uM}@~Ae@~@sCbFc@z@qElIYf@U`@mDrFoBdCw@t@sAbAYRe@ZyCbBOJi@ZsEnCqAv@]RWTg@r@Od@Ib@GnA?\\?fBCdBIlAWvAUx@_@|@Uh@o@`A]b@q@n@y@n@uKpHu@l@e@\\CBsBxAcCdB}B~Ac@\\cAv@MHoEzD}GlGuApA{EhEwG|FkEpDeBtAaAl@yB|@y@VcATqAXeIpByBj@mCp@gCl@uBh@cHdBk@NkAXWFYFc@L{A^cHbByCt@[FKHqBn@oCp@cCd@{@TiGzAgB`@yA^e@La@HyBf@sA\\gE`Ai@N{@RuIzBqA\\oEfAsD~@eAVgCn@o@PyFrAk@PqLvCuC|@c@J]HuA`@iB^eCn@iDt@]JgKfCmAZGBa@JkBd@uCv@wD~@yC|@uCbAgCfAwDrB_BbAwA`AsBbBkBbBcClCiApAw@nAcBdCa@t@_@l@EFYr@yCzGOZYl@Wp@eCdG[p@mAtCy@dCo@hAsEvKcFlL_CvFCHu@`BmBpEe@dA_AjBm@fAwAjBg@h@iA`Aw@l@y@f@}Az@{@\\eA\\"}, "startLocation": {"latLng": {"latitude": 13.3060946, "longitude": 77.1557608}}, "endLocation": {"latLng": {"latitude": 13.3817136, "longitude": 77.096813}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Keep right to stay on NH 4/NH 48\nPass by Kyatasandra Bus Stand (on the left in 1.5 km)"}, "localizedValues": {"distance": {"text": "11.1 km"}, "staticDuration": {"text": "13 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 746, "staticDuration": "101s", "polyline": {"encodedPolyline": "urtpAa~puM}@h@OFyBn@aBf@mDjAgGlBaFvA{Af@{@XwAb@c@L"}, "startLocation": {"latLng": {"latitude": 13.3817136, "longitude": 77.096813}}, "endLocation": {"latLng": {"latitude": 13.387899599999999, "longitude": 77.0941658}}, "navigationInstruction": {"maneuver": "RAMP_LEFT", "instructions": "Exit onto Service Rd\nPass by ನ್ಯೂ ಮೀಸೆ ಮಂಜಣ್ಣ ಮಿಲ್ಟ್ರೀ ಹೋಟೆಲ್ (ನಾಟಿಸ್ಟೈಲ್) new meese manjanna miltry hotel (Nati stail) (on the left in 400m)"}, "localizedValues": {"distance": {"text": "0.7 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 72652, "staticDuration": "3758s", "polyline": {"encodedPolyline": "kyupAqmpuMw@BiAX_]`KgK~CeF|AiEpAeCr@qEtA{Bt@eGrBgC~@oBj@kAb@kN|EiA`@sKxDoCdAq@T_Bj@{@ZqDlAMFyCdA}Aj@QDw@XwAp@o@\\OHy@f@_DdCqB`BkGfF{JdIOJ]ZsKzImGdFs@j@URsIbHuBdBqJzHcAx@iGdF[VC@_FbEyGnFsC`C{@t@aGpEOLeDhCcDfC{L|JcFdE}HrG_F`E{FvE_Ap@aCpAaDzAYLu@^uBnA_CjA{XhM}C|AIDiRxIwLrFuG~CeAd@mAj@gAl@c@Z[T{AdAuEzDqOrNwJ~I{@x@_CxBeEvDmAfAcAn@_EnDuEfEyBjBmErCu@d@yIvF}E|Ca@Xo@f@{@v@yDtEgBdBaCdB{BfAiK~DuJlDeBp@kCpA_C|AiA`AiFlFmMzMcDhDiAtAmCtDw@nAqAvBa@v@mAlC{AvDWv@iEzLaAnCgA~Cu@nBy@hBu@rAcA|Au@dAgAjAaKbJwAhAiBnAsAr@sAl@iBp@mA`@sF|AmXzHsA^kLzCiBj@cG`BgAZ}C~@eGhBmF~As@TaAXkBj@a@LgAZwW`He@L_M~CkJdCwKxCSFyCz@wEtA_AViAXSD_@LoB\\yANw@BqBG_DU}@Mk@G]E_AK}AKcD@uAP_B\\oC~@wKbEkOtFeC~@uAn@aDfA_EfBiDbBaCnAkEfCyFvCgBr@mBl@}DjA_Ez@iBXuBVqDZ_CVaDj@aDl@{Bp@UFcDhA_IzCoDzAyFlCeE|Aa@TgPpH_@P_JbEcAd@q@ZoCpAgCjAUJaH~CyAp@cD|AaGnCc@RyAn@}LvF]Nc@RoClA}Ax@gAh@wIzDYNYL{BbA}EzBs@\\ULgOvH}Ax@{CvAcErByDjBeD~A{BhA{BhA{Az@mFdDuErCuErCeI`FyEpCqSlM]TiDrBeRpLaF|CeNpIiFbDkEhC{ZfR{BxAGL_HxDeBfAwBtAe@ZWLwBpAoHlEqG~DwM`IeMhIcMpIgNdJiI`GwBxAuDvB_D~ASJ[NaEbBUJkCdAyEjBo@VeIbDOFu@XeBp@mAd@{DzAuB|@aA\\}B~@cMrEgGzBwK`EwEdBsFpB{E~Aa@LyGvBODOD}FlByJbDc@L{Br@_@LyAf@uBv@iDzAyHxDi@XkL~F_CjAqAp@y@b@yCzAwC|Ac@TgDjBu@b@mPhKaBbA_@ToG~DqJbGqDpBuAj@QHyCbAkDz@[HwB\\iKnAaBRmD`@cAJi@FcMvAkALyAP[BSBcCViIt@sBPkEZy@DcEVoI\\oBF_ABeI\\aKZ_ENY@uERwKp@sHd@gKl@{@DO@uF\\oCLoCDgCBmGDkFBuFDkEBsMJi@B_BJcBPq@HyCd@uATmAXqAb@e@Ry@`@uA`AmAlACBGFu@x@cBjBMPu@z@gHhIKNkArAcAjAwA~AyApBoAvAuOnQcBrB{DjFiAjAONmAjAqCtCcFzFgFbGiFdGsGtHSTkC~CwA~AWX}@x@uBzAaBt@iFbBmBl@m@PwT|Gk@P[JwLrDqAh@cU`H[JcP`FmBx@eHlDaAh@kBdA[RcAv@_BdBw@nAU`@qEvIS^cApB{DrHk@`Ac@v@gKbS{BjEe@|@uHrNu@vA}AbD_@dAcAjDq@pCeCpKW|@u@fDa@hBw@bDg@rBkEpQyIx^}CnMiAxEK`@St@_@jAm@|Ag@bAk@`AsB~CmEnFiDdEgBtB_C~CWZY`@OXk@|@kAlBkAdCyA|D{IjUgCvGeCpGCFEHIVaD|HaHtPgBpEMVwEbLaHvQyArDaEhKIRoAvCw@bBaK~Sg@fAqDdIyBbFQf@}ClIm@`BITeAhCu@fBiAjCkBpDgBfD}EdIQXoDbFq@z@{@z@m@b@eAv@eAp@_B|@{@b@s@`@e@Z{@j@_CnBiCfC_AbAwAxAwA~A{@x@c@\\_Ax@iC|BsDvC{@n@qDrCsEnDc@^uBdB{@n@s@h@eA`A_ExEcCrC_HdIyClDiFhG{J|KaAfA{DvE_IhJuDvEqAzAq@z@iBpBwCpDcAnAmClDUZsB|BmL~NyC|Dw@fAaJhM{@vAeDvGwAvCkEdJ_ApBeA|BwA~CcAfBuHtOsAtCqAhCWh@_A`BiAlBaQxYiB~Ck@jAiAnC]bAq@hCi@~B}BpMm@rDe@`COr@gBtIWlAyB|KGZsDvQw@bEg@hCk@bCMh@]hB_@tAETkAtD{BrG}AbFsBhGwEtM}AfEuA~C{@vAcApAs@v@c@b@qAjAyEbD{LnIuB|AsBpB}B|CuJfN_DdEoFpGgEfFqBlBwApA_Ax@aBrAkCzBoAfAmB~AqAhA}CjCkA~@_GjF}HvHyAzAkGdGq@p@wGbHiYzZsLnMkPxQgDjDy@`A"}, "startLocation": {"latLng": {"latitude": 13.387899599999999, "longitude": 77.0941658}}, "endLocation": {"latLng": {"latitude": 13.8775641, "longitude": 76.6954295}}, "navigationInstruction": {"maneuver": "RAMP_RIGHT", "instructions": "Take the ramp on the right onto NH 4/NH 48\nToll road\nPass by VIJAYADURGA ENTERPRISES (on the right in 9.1 km)"}, "localizedValues": {"distance": {"text": "72.7 km"}, "staticDuration": {"text": "1 hour 3 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 32, "staticDuration": "5s", "polyline": {"encodedPolyline": "wmusAmqbsMGx@"}, "startLocation": {"latLng": {"latitude": 13.8775641, "longitude": 76.6954295}}, "endLocation": {"latLng": {"latitude": 13.8775972, "longitude": 76.695138}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left"}, "localizedValues": {"distance": {"text": "32 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 832, "staticDuration": "90s", "polyline": {"encodedPolyline": "_nusAsobsMcAtAuAvB[j@IPWp@OX]v@sArBc@h@ONg@f@cCxDuBdDqAjBa@b@cBpB"}, "startLocation": {"latLng": {"latitude": 13.8775972, "longitude": 76.695138}}, "endLocation": {"latLng": {"latitude": 13.882116, "longitude": 76.689025}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_RIGHT", "instructions": "Slight right\nPass by <PERSON><PERSON> HINDUSTHAN MOBILES & AUTO PARTS (on the left)"}, "localizedValues": {"distance": {"text": "0.8 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 24, "staticDuration": "4s", "polyline": {"encodedPolyline": "gjvsAkiasMi@?"}, "startLocation": {"latLng": {"latitude": 13.882116, "longitude": 76.689025}}, "endLocation": {"latLng": {"latitude": 13.8823304, "longitude": 76.6890186}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right toward NH 4/NH 48"}, "localizedValues": {"distance": {"text": "24 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 106811, "staticDuration": "5246s", "polyline": {"encodedPolyline": "qkvsAkiasMk@l@eNlLqB|A}AvAaFfEuCdCyEpEaF`F}EzEqGjGiAfAeAhAyNnNqApAqGhGmElEwBrBwAvAeCzBuCzBoClB_SvNwAjAc@d@qAxAwDbFaBzBoDbFeDrEY^eCnDuB`C_A~@uCfCeDjCaBvAeBzAyAvAKL_ChCgBpBIHoEhEa@`@i@f@i@f@iBfBiBhBIHsAbBs@bAOTEFYf@i@hA{@xBwChKi@rBsClKGViBxHIXETuAxG{AhH_@lBaFzUi@|BgAtFy@bCaCdFKTc@~@_@x@gA`CiAvBg@r@a@f@{@z@}EhEwDdDw@h@YXWVq@l@wDxCqDdDkCxByDbDmBnB]Zk@h@o@p@wEtEuAnAg@^}At@{@Vq@Le@Hs@F{ABqAG}@MeBe@[K_A_@qD}AIEgEgBOGmDwAwGwB}@Qk@E{A?c@BeARmA^qHlCuCjA_CbAu@Xa@NaCbAmCbAcDpAwCfAyN~FsDbB]Pm@^]Xe@^k@j@uAlBe@~@k@xAe@fBW`BKtB?jBFbAl@pK@`BC`@QfAMd@IRIR_@j@SV[^[XoAv@sLnFe@RQH{IvDiF`CYJuIxDcBp@{BdA}Ax@EBiDlBoDtBmAr@sBlAGB_HdEcSjLGBiErCqJxFs\\dSsO`J_KbGgI|EsGzDkAn@_LxGoBbAkAd@qD~@yB\\cTpBaGj@oCNcBP_Iv@}AVQDODoIrBgA`@yEz@mEh@yR|BkFr@aBZcD`AoNhGqBz@_n@jX_Y|LeC`AqBh@{AXuBRuRxA_Il@uDl@cAViBl@g@RgB~@qKnGYPiCzAyCvBy@z@sHvHwUpWmDxCyAhAuD|C_K~HeLhJeBlA}@x@s@j@oBfB}ApAsElCaDtA}LvFug@bUoB`AuFdCyDhBqBz@}At@cIrDgDvAyHlDe@TyAp@}CvAuDjBqEpByCvAuBhAoEvCcAf@gBjA}AlAcBbAuCrBiIlFmNnJwEfDcGzDgAt@wA`AmCdB}LdIo@^yCxB}@t@yBtBmCvBiMnMmEvEuBlBQPk@j@yDpDwAlAeBpAQLmDpB}CtAq@ViC`Am@PkH|B}FxBoKnDgE~AkBl@cFlBaBp@gJbDcExAa@LeA`@{Ap@s@`@oAx@m@b@cCpBgMrMmEpEuBnBu@t@gLvL{@bAUXoArAoAlA}AnBs@bAiAjBiBlD]r@oAdCgBpD{ClGuAvBsBlCeLhMkDbEoI|JaBpBaFjGeIvJ{GfIyCvDkEdF}AxAuA`AaF|CaAf@gEfCo@`@iJ~FmAl@cCpAoC|AqHrDSLo@ZaG|CiAh@aBt@eBr@{DnAgBh@{Af@gF~AkA`@iIfCqCfAwAt@eCdBmCrB}L|JiJnHmC~BkEhDiCrB_ClB[V}ApAoDpCSPq@j@gFlEsAjAKHQN{@r@a@Z{BdBmChBMHo@\\{@f@iCdAs@Xe@NmBf@k@N{@NwAVaAJeBRsFp@}AN{Gp@eAJ{SrBSBcLjAmCXM@M@uBXgALmE|@uBd@wD`AoG`Bg@Lm@P_@HeBd@eBd@oBh@yCz@}@RgAVSD}@PgEp@aE^aCLoDJe@@yDFuAAgA?O?]Aa@AeLa@wE@m@?a@@mCByEVgBNcJz@m@HqJbA_J`AkFf@I@qD`@_BZaB^oBl@cC`AgAd@}A~@qEvCgAr@oCdBeEnC}@j@oCdBqFnD}CjBuA|@kMfImBlAs@d@{GlEi@\\uA|@s@d@cFfDsA~@o@f@YZa@d@OTk@z@qAvBoDtHi@z@uAtBaAxAwBfDeAdBw@jAW\\_BzB{ArB}FlIwDpFqBrCUX_NzR}EfHcBbCo@|@wBjDINm@hAKR[l@c@|@Wf@mC~FmFnKyGxMeBnDeAhBU^eA|AaAnAcBhB{BjB_Ar@mClByC|Bo@f@{GdFoHvFcAz@kDfCmAdAuB~A]VkBvAmG`FkHjFiF`EKH{ElDiB|AsBfCORsAjBg@|@oApC_AzCYvAIb@UxAK|@IfAG~@Cx@AlBAZBzD@d@?n@?|@?b@Ax@?`GAjHAj@B|I@rQ?fD?p@@J?xBAfC@pG?l@@nR?@?~C?bB?jCBpQ?xHFvB?VPxHFlATpEV`Ed@lGf@hFHj@^~Cd@nDZ|BnArH`@|BH`@XvATnA|EpXJd@Nz@bA`G|AtIdA~Ft@bE\\pCNjBDpCAnC?l@An@ShUElHGtD?J?JC`AIzBKhBANW`Dk@hEa@dCuAdGi@nBgDbKa@hAkBnF[~@M\\aBbFw@xB}AlE]`AmMn\\ITQb@oB`FkBzEQb@sFrNiM|[yEvL}@`C}CzHqJzV}A`EsBzF}BdGSd@Yh@{DbK]|@k@xAKVsAlDkA~CmApDqA`DQb@oCpHwBxFQh@qDrJ_@`BeG~OmI`TYv@_ItSq@lBoAhDOb@kEdLqGbQy@tB{JnU{HbRsHlQcDzHQ^yDfJqGhOyDhJgBhEyBjFeCrF_HrNsEpJgA|BcF|JsGnMuCrFe@z@gEjHcFlI_BtCmIdNeDpFwFlJeDpFe@x@mGjK{G~KgX~c@cFlIqArBwOxVmDdFyHtKyF`IwSvYaC|CkBtBkMfOo@t@}FpFeAt@{DjCuLrHg@XqD`CyFdDi@XOHmEpBi@RmAd@uCpAcAd@}An@oJxD]NcIhDyClAu@\\iAd@gDrAo@VaBt@sF`C{Af@]HoCn@qCr@c@J_@HyA`@qCdAMFiCvAuB|AcA`A}@bAiAzAILILwAlCe@dA{DvIa@`AmAnCg@dA{@dBS^k@`AMNa@n@Yd@kDxF_@j@c@r@_GtJcAnBIPk@`B_@tAUpAQxACTKpAG~A@pB@h@HnAjAbJNhAh@zDPzAfBnN\\nCJl@vA|Gz@rDhE`RLj@Np@|B~JH\\nApFd@tCNzBDdBA`CG`BIdB_AlREbA?NC^a@lQMzFEhBIhFCd@MtCM|BUpCSnBQdBiAdLsBxRgA~KC`@Eb@OfBK`AWdCw@~H}A`PCVGj@{@rICZM~AMbAqApMUhCGn@Ip@QhBe@jEU`Ce@zE]vCSrBGf@SlBCVy@rHe@jDe@pDuAfK_AlHk@nDwArHaAbFq@hEaD~P_@zB_AfFcAnFe@dCo@lDwB|LcAfGObBGdAa@rJIbBOdAUrASdAcFvPoFrQMb@aF`P}@tCu@jCqAlEkEzMkAlDcCjIYtAO|@QdBC\\IbBHnRF|EBxF?~@BfCCpDOxBQ~Ac@nBiAnD_D`JyFbPw@zBg@`BoCrH}ArEy@xBcAzD]`CKrAc@zJSzFMrCa@rD_@|B{@pD_BdGQn@mCxKiArEq@jCu@dEG`@Ij@}A~JuCnRSnAWzAQ~@iCvOCNk@~DgAhHm@tD]fB_@bC{AzJuBzLgA~Gm@tD{@bFg@|CU|Ao@|Hw@rK]vFQ`BYdBo@zCGZoBfIq@vCcFxTOp@u@bDoDrOoFlUkCnKkApEgArEWdAo@pBw@hBmAbCaEvHiD~GsDdGkDxESVaF|GwH|JcElF_AdAw@|@e@f@m@p@iAjAkKnK}KfLkEbEeGlG"}, "startLocation": {"latLng": {"latitude": 13.8823304, "longitude": 76.6890186}}, "endLocation": {"latLng": {"latitude": 14.4219903, "longitude": 75.9776607}}, "navigationInstruction": {"maneuver": "MERGE", "instructions": "Merge onto NH 4/NH 48\nContinue to follow NH 48\nToll road\nPass by Spoorti (on the right in 26.8 km)"}, "localizedValues": {"distance": {"text": "107 km"}, "staticDuration": {"text": "1 hour 27 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 11, "staticDuration": "1s", "polyline": {"encodedPolyline": "mx_wAkkvnMJN"}, "startLocation": {"latLng": {"latitude": 14.4219903, "longitude": 75.9776607}}, "endLocation": {"latLng": {"latitude": 14.4219298, "longitude": 75.97758089999999}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left"}, "localizedValues": {"distance": {"text": "11 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 1110, "staticDuration": "69s", "polyline": {"encodedPolyline": "ax_wA{jvnMoGtG_ChCcDhEsBtC_EpFKPyArB{AxBg@x@]j@[j@gApBe@v@"}, "startLocation": {"latLng": {"latitude": 14.4219298, "longitude": 75.97758089999999}}, "endLocation": {"latLng": {"latitude": 14.428303499999998, "longitude": 75.9696719}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at Vijay\nPass by <PERSON><PERSON><PERSON> <PERSON><PERSON> (on the left)"}, "localizedValues": {"distance": {"text": "1.1 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 56, "staticDuration": "17s", "polyline": {"encodedPolyline": "{_awAmytnMnA|@"}, "startLocation": {"latLng": {"latitude": 14.428303499999998, "longitude": 75.9696719}}, "endLocation": {"latLng": {"latitude": 14.4278971, "longitude": 75.96935909999999}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left\nRestricted usage road"}, "localizedValues": {"distance": {"text": "56 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 24, "staticDuration": "9s", "polyline": {"encodedPolyline": "k}`wAowtnMVe@"}, "startLocation": {"latLng": {"latitude": 14.4278971, "longitude": 75.96935909999999}}, "endLocation": {"latLng": {"latitude": 14.4277779, "longitude": 75.9695472}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left at Apoorva Restaurant\nRestricted usage road"}, "localizedValues": {"distance": {"text": "24 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 62, "staticDuration": "17s", "polyline": {"encodedPolyline": "s|`wAuxtnMTpB"}, "startLocation": {"latLng": {"latitude": 14.4277779, "longitude": 75.9695472}}, "endLocation": {"latLng": {"latitude": 14.427665499999998, "longitude": 75.9689837}}, "navigationInstruction": {"maneuver": "TURN_SHARP_RIGHT", "instructions": "Sharp right\nRestricted usage road\nDestination will be on the right"}, "localizedValues": {"distance": {"text": "62 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "317 km"}, "duration": {"text": "5 hours 13 mins"}, "staticDuration": {"text": "5 hours 13 mins"}}}, {"distanceMeters": 149051, "duration": "8232s", "staticDuration": "8232s", "polyline": {"encodedPolyline": "}{`wAcutnMUqBWd@oA}@GJaA|AUSeQzYS\\cDnFyJtPk@~@_C~DoAfB_F~Gg@t@_AxA{@dB[z@e@hBWtBG`BFjBH~@TzAVfAp@nBnBlFzBvHhBvGPr@r@lC\\rAHXr@pCjCxJ~@vFLp@RjAb@`C`@~ANp@Pt@dApEtD~NnFlShAfEb@fBz@bEx@~En@tEHnADp@?dCCpAWlCY`Be@rBqBnGSh@ABYx@Qj@kCzHc@pAo@nB_ApCeA|COf@Of@Yz@GNi@vAyApEM\\IV}B`HoH~T_BxEqDvK{ChJiFfPs@rBcBfF[x@Wt@qA|DyAjEY|@oDrK[~@oDzKkFzO{IbX[z@cCpHkIbWuF`QaBfFoApDHp@g@~AAFs@`B_@rAo@zA_@`AUp@Wr@W~@w@rB}AfEeB~EkEhM]`Ae@TWv@mAnDsBzFuAdEgAzCeE`M{G`SuBlGsAxD}DhLEP{H`UuLj]gDpJOd@_LjZaE|KaDnIgC`HENGPyCzIsBnGUr@eQth@}CxJ{Nnc@y@`CcHdTgFvOyHtUIVgAhDSl@u@~By@bD_@fBYpAi@pDwBtQo@lFGn@CRqAdLMbAK`AeAtIaA~GGd@iCrPkBxLOdAI`@[rB}@zCg@hAo@jAyArB_BzAi@d@cAn@aDbBmDhB_CjAeCnAgIfEy@`@eEtBeFjCw@d@yCzAc@TkEvBwBdAqBhAaBx@iFlCkAj@yAv@eH|CcAb@aF~AyD~@u@NoB`@}Er@UBa@DsBTkEXqDHeEFaEFO?W@qA@sBB_KN_UZqDFuEFuBDiAFmAFuBReD`@kB^iCn@K@a@LgAZ{Ah@kBn@wDdB{BnAy@d@sDrBoCxAsHhEMHqBdAGDoAp@eDnByAhAu@t@SR_BnB[d@[h@sAjC_B~CaAnBCFuElJ}CrGeBrDCF_B~CeBhDoV`g@k@jAy@hBkDhHqAdCkAdCuFbLMXcPb\\gBpDmBxDm@lAgFjKMV}GnNO\\{A|CyBpEgAdCQZaAfBiA|Bk@hAkB~DyAxCsC~FuBbEeB`DoAjCeAdBsC`GMVsAlC{@zAsE|JYh@GLGNoC|Fq@pBEPe@fBc@hCKpAEnBCb@ArATxFNhENpEJrCDjB?lAIdCG`A]dCg@|B_@fAm@dBy@`ByB|DyEdJuHrN_EfH}FfLaBbDiGpLc@x@iCbFiEdIuChFsElIy@rAeChFcAnB{LxUoIlOo@lAoFjKO\\eArCs@~BU|@Kh@K`@[zAeCbMk@nDuA`HqCpNwDtRyD~RKb@]hB_CnL}@tEeB~KkAdHE\\oA`IcDvSqAjIWxAUrAeEdX{BnNc@zBg@zBuA`F{AjEiBhEsBzDw@rAwBdDyFfI}FjIs@~@kQ~Vw@fAsNjSw@lAyAbCmBpDcDfHUd@u@dBqApCcBrDIRe@dAkBxD}@`BW`@aApAuA~AyBvBcAv@}BtA{ClB{EzC}A`A_@TiIlFmFlDoAt@mJdG_IdFoBtAoGbEgChBe@`@gAdAeBnBgBhC{A|CWp@a@bAiF`NiAlCq@hBiAtCgHbR_D`IKTs@lB[z@Qb@s@lBc@dAgBrEs@tAiAnBSZ[f@}@hAoApAcA|@_An@yGvEwB|ASLQLqM|IyFxDmBlAuDlCma@~X_S`N]TeGdEk@\\iDtBkGxDg@ZmEnCoFhDkAt@WPq@^i@X[PgAj@kCtAuCdBsDbCaMnHiJpFyBtAw@^qD|BiC|AsGlEYTkAz@e@ViB~AkBjBsF|EkM~KqBpBeAxA]j@CDg@`AOXk@zAc@nAy@lCO^[t@_A`Ba@h@_BhB]ZwCzBk@b@q@d@}B~AgCtB{BjCk@v@SZsB`DGLGL_@j@kBzCq@fAkB`D[j@c@n@q@hAm@nAyAdCoAlBuAtBmBdCcK|LiCbDoBdCqCbDmB|BaBzB{@bAuBdCyAhBsBzBmE`EgDzCmAlAs@l@_@^}@z@gA~@oEdEWVs@n@i@b@gBfBcA|@g@f@uDfDiBbBkAlAyArA_AfAuA~Am@t@qAfBgDrEmA`BqC~DmA~AyLxPaBbCUb@iAhBeCfFo@xASf@oB|Fe@`Bw@bD{@tDu@`D}@lESx@uBlJm@fB{@zAcAtA{@v@_GpE_BrAg@d@g@n@QX[n@o@dB[bBWjCQbD[vE_@fGSnC]bFUvDi@hJMtBEvACrBF~BSnJKlCIzFE`BQvCId@Q`AGVGXWv@_@p@e@n@]`@SNk@h@SPo@^sAv@wBpA{@j@kB`Bg@j@y@bA{@pAm@x@[PcClD{EnH_ElG_M|RwExGqCrEgGjJ}@hAeA~@{@d@i@VaBf@yATiFp@y@Rc@L_@LcAh@w@h@cBzAuFnFyMbMkAhAkPrO}BxB}CvCaXhWkAjAaBxAwBrBqChCyV~UYXk@l@m@t@aArAaAhBiGjLaB|CQ\\g@|@iAdBi@l@q@n@k@d@kAx@uDjB}Av@mAj@sA`Aw@n@s@t@y@hAo@hAk@rAqBjH]nAoBxGo@fCy@zCe@bBsArDcBbEyAnCkAjB_CxCa@d@U`@uAzAQPi@d@c@`@cPjM}BpB}AxAgAjAyBfCuCpDq@v@k@v@w@tA[r@]~@WbAQ`AObBClABvBBt@|@vMb@rGHpAHvABvCE|BQ`D_@`Dq@fEq@`EIh@Ih@oAxHy@`Fk@tDE\\]lBIj@G`@gBpLUzAc@jCwDnUoBjMg@hCi@fBWl@M^g@hAe@~@gA~AUXeDhDiDtDONoBvBeItIgLnLk@n@eAdAWVkEtEs@r@gBzAGFkChBuC|AqB|@kBr@{Bp@uGvAeIhBql@xLwHxAmGrAmE~@aE`A_Bf@mBl@gDjAsXlLUHgLvEoAd@oBr@mD`AaE~@cQxDmCj@kCj@{K`CoD|@iC~@WJe@Tw@^SJq@^eBjA_At@w@r@WVcAbAgFfGsA`B{GzHcF`GsEjFoAzAcDtDs@z@UXw@`AUZcKnLiAhAWXeEbFgEzEaAfAs@x@aHlIuAfBaAjBq@~Aw@tC]rBS|BEfBAbAL`CNjBLrAh@|FH|B@`@B|@?pAMnDSfCIj@a@bCo@jCi@fBaBtDUb@[j@{A~BmAzAoBrB[ZcJxGePxKyPjLyA`AeAt@wAbAuAtA}@~@W\\W\\wBdDcE~Gy@lAiApAURMLq@n@s@f@sBhAkAd@wC~@YHqAZw@RoHrBwGlBiGlBu@RgHxBeJhCi@Nw@VoDbAcI|Bu@Tm@PyBt@aA`@yBrAUNmAdAg@f@_HfH{FjG_SnSwDzDuQxQm@l@oFzFoFvFc@f@yE`FsN|NeQrQyLbMmDrDcFjFcMdMcDjDwLxLSPsK~KaFdFoC|C}AtAuCxC{CjDeBjBaEvEsCxCaB|A}@p@c@XcBzAkCrCSPoFjEaFrD_@VwClB}GzD[PuE|BqCjA_DlAoFfBgHpB{GbBqBj@mCr@_JtBwFrAcIvBa@LQF_A\\c@P{BbA{ClBkAz@qAlAwA|AcC|CgB~BuInLiDrE}@lAa@f@_@d@UXmA|AyEbHaCpD{BfDMRyDrFmDfFgD~EqCdEa@l@iB`CqB`C{CpDyAtAo@d@_At@eAp@eAl@sBdA}@b@kFdCeCbAaErAkJ|BE?oG`BqBh@_N~CiG|AgBn@iAj@a@RiGdDqDbB{N|HqIrEcG|CuC~A_@RuC|A}DpBwAv@mBhAKFiAv@m@d@o@h@aDzC}@z@KLWVyAzA{ArAe@d@w@v@Yr@K\\uLpLg@f@oFbFeJdJWXgBfBO[eCdCe@d@aC|BmBbBo@f@oChBcEfCeAp@sA|@oA~@}AtAg@f@kCtCgBpBOPSVi@l@UVKJMNi@n@_CjCIHMNi@j@}DvEcH~HaAjAcAjAa@h@qAfBQXW\\sDjFqC~Di@v@_@h@cArAgApA}@|@sDtCyBbBuD~CeIxG_HrFeCpBmGdFCDUR]\\yAjB{AfC{AjC_A`BkDlFw@dAgBzBgEjFwE|FeBxBIJuA|BKRKTEHQ^Yt@eBlEg@tAkA`Cm@~@qCfDaCrCe@f@mCxCeA|AiAnB_D|Gg@bA{@tAkD|Ei@p@iBdB{@t@[V{@p@{@j@yCbBo@XqAf@aGhBoA\\c@Ls@NyD`@gFh@yDd@w@Ls@JsC`@{Df@eCb@oC^sA\\aCv@_Ab@gCxAcAr@iCbC{@fAyBdDkA|Bo@tAeAlCc@`AqBxCy@`A_A~@sAdA{A`AiCjAkA`@yBh@qCh@cIbB}J|B_MdCcHxAgGpAuE~@cQtDoIdB_Cr@sEdB}FjC{@`@aBz@_B`AyEtDmB~AgAdAaCxBqCjCiE~DsC|B_@\\gAp@}Az@uClAyGlC{DbBe@Pe@PQH{LhFMDiFjCeCfAaErB{BhAgEfBcFhBqGjBoUrGiB^kALo@BqA@yIOsKSqMQmCGqBEy@Cq@A}EIs@CeDEcBEu@AuHMWA_JMw@CiBEmJOqJSo@AeAC_JQsJOaA@_G`@w@BwG^_DRwDReAFg@BoHb@gDNmTjBaCZmj@`KgZpF{Dd@uKtA}AR{QzB}JjAkBRwFx@sB\\SBuAPkFp@sKpA{IjAcNjBmBT_Gr@yIdAqC^}Dd@mNfB{a@|EcCVyBNg@D}AHcFPgKTuMZsAJoE|@oBf@mHpA_CRmCAkEF{N^s@BuADeDZiBb@kFnB{DrAyBf@yAn@cD`A_ItByEjAiBf@{Bv@mAh@a@LiAh@_DrAkCt@{AZsAP}AHiABaA?}@CgAGmC]q@MmN{ByEk@oEa@sHo@iD[wFs@kFw@MA}GaAeCY{@KUCaEWuBQ[CuAM_De@cEi@{@MuLeBsCa@oBYgMgBuDi@iDe@KAaGw@sD]gAGqBMeAEiFM[?y@?yBDuDHuAFyDNuAHE@e@FK@eFT{GZu@@Q?a@?M?gBEuHm@qMkAgAMyAQiBUyCc@qCc@yE}@eGeAa@KeJaBsHuAqASaCa@gEy@w@OsCi@cAS{JiCsD_AcASu@KaCQi@CsLw@{DUg@CsAAWD}BB_IRaHNc@@wADqCDkGPwJXyELe@@mAB{KTaABkA?mAGyB]wAc@UGuDmAkDkA_DaAcAWcBW_BOw@EaKQyGKwHOaAAmBEqCEuACi@@oAEgBCiCEoIMeBB{BLqC^kCd@wE|@yCl@s@LoFdAoDl@s@Lk@FmAJgBDiBEiAGeBWc@I_@ImDs@kC_@QAeCMk@AcEAeF?S?ob@AwFBmAAk@As@?u@AyCA_A?g@@cB?_A@w@@w@DeAH}@JsAPmObCeC`@m@J}Dn@yGdASB_BXwATcCb@qA\\UJ}AtAMLGHi@^_@\\{@dAk@nAg@fAg@dA}@jCM\\Wn@Qp@Wn@"}, "startLocation": {"latLng": {"latitude": 14.427665499999998, "longitude": 75.9689837}}, "endLocation": {"latLng": {"latitude": 15.318615999999999, "longitude": 75.139907}}, "steps": [{"distanceMeters": 62, "staticDuration": "12s", "polyline": {"encodedPolyline": "}{`wAcutnMUqB"}, "startLocation": {"latLng": {"latitude": 14.427665499999998, "longitude": 75.9689837}}, "endLocation": {"latLng": {"latitude": 14.4277779, "longitude": 75.9695472}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head east\nRestricted usage road"}, "localizedValues": {"distance": {"text": "62 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 24, "staticDuration": "10s", "polyline": {"encodedPolyline": "s|`wAuxtnMWd@"}, "startLocation": {"latLng": {"latitude": 14.4277779, "longitude": 75.9695472}}, "endLocation": {"latLng": {"latitude": 14.4278971, "longitude": 75.96935909999999}}, "navigationInstruction": {"maneuver": "TURN_SHARP_LEFT", "instructions": "Sharp left toward NH 48\nRestricted usage road"}, "localizedValues": {"distance": {"text": "24 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 56, "staticDuration": "16s", "polyline": {"encodedPolyline": "k}`wAowtnMoA}@"}, "startLocation": {"latLng": {"latitude": 14.4278971, "longitude": 75.96935909999999}}, "endLocation": {"latLng": {"latitude": 14.428303499999998, "longitude": 75.9696719}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at Apoorva Restaurant toward NH 48\nRestricted usage road"}, "localizedValues": {"distance": {"text": "56 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 70, "staticDuration": "31s", "polyline": {"encodedPolyline": "{_awAmytnMGJaA|A"}, "startLocation": {"latLng": {"latitude": 14.428303499999998, "longitude": 75.9696719}}, "endLocation": {"latLng": {"latitude": 14.428665100000002, "longitude": 75.9691449}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left toward NH 48"}, "localizedValues": {"distance": {"text": "70 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 16, "staticDuration": "1s", "polyline": {"encodedPolyline": "ebawAcvtnMUS"}, "startLocation": {"latLng": {"latitude": 14.428665100000002, "longitude": 75.9691449}}, "endLocation": {"latLng": {"latitude": 14.428777900000002, "longitude": 75.9692383}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right toward NH 48"}, "localizedValues": {"distance": {"text": "16 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 10869, "staticDuration": "616s", "polyline": {"encodedPolyline": "{bawAwvtnMeQzYS\\cDnFyJtPk@~@_C~DoAfB_F~Gg@t@_AxA{@dB[z@e@hBWtBG`BFjBH~@TzAVfAp@nBnBlFzBvHhBvGPr@r@lC\\rAHXr@pCjCxJ~@vFLp@RjAb@`C`@~ANp@Pt@dApEtD~NnFlShAfEb@fBz@bEx@~En@tEHnADp@?dCCpAWlCY`Be@rBqBnGSh@ABYx@Qj@kCzHc@pAo@nB_ApCeA|COf@Of@Yz@GNi@vAyApEM\\IV}B`HoH~T_BxEqDvK{ChJiFfPs@rBcBfF[x@Wt@qA|DyAjEY|@oDrK[~@oDzKkFzO{IbX[z@cCpHkIbWuF`QaBfFoApD"}, "startLocation": {"latLng": {"latitude": 14.428777900000002, "longitude": 75.9692383}}, "endLocation": {"latLng": {"latitude": 14.4506058, "longitude": 75.87753550000001}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left onto NH 48\nPass by the gas station (on the right in 6.7 km)"}, "localizedValues": {"distance": {"text": "10.9 km"}, "staticDuration": {"text": "10 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 292, "staticDuration": "50s", "polyline": {"encodedPolyline": "ikewAsybnMHp@g@~AAFs@`B_@rAo@zA_@`A"}, "startLocation": {"latLng": {"latitude": 14.4506058, "longitude": 75.87753550000001}}, "endLocation": {"latLng": {"latitude": 14.4515894, "longitude": 75.87507029999999}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_LEFT", "instructions": "Slight left\nPass by SUSHI CONVENTION HALL (on the left)"}, "localizedValues": {"distance": {"text": "0.3 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 731, "staticDuration": "106s", "polyline": {"encodedPolyline": "mqewAejbnMUp@Wr@W~@w@rB}AfEeB~EkEhM]`A"}, "startLocation": {"latLng": {"latitude": 14.4515894, "longitude": 75.87507029999999}}, "endLocation": {"latLng": {"latitude": 14.454368200000001, "longitude": 75.868918}}, "navigationInstruction": {"maneuver": "STRAIGHT", "instructions": "Continue straight"}, "localizedValues": {"distance": {"text": "0.7 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 24, "staticDuration": "3s", "polyline": {"encodedPolyline": "ybfwAwcanMe@T"}, "startLocation": {"latLng": {"latitude": 14.454368200000001, "longitude": 75.868918}}, "endLocation": {"latLng": {"latitude": 14.4545565, "longitude": 75.86881079999999}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right toward NH 48"}, "localizedValues": {"distance": {"text": "24 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 53332, "staticDuration": "2755s", "polyline": {"encodedPolyline": "_dfwAacanMWv@mAnDsBzFuAdEgAzCeE`M{G`SuBlGsAxD}DhLEP{H`UuLj]gDpJOd@_LjZaE|KaDnIgC`HENGPyCzIsBnGUr@eQth@}CxJ{Nnc@y@`CcHdTgFvOyHtUIVgAhDSl@u@~By@bD_@fBYpAi@pDwBtQo@lFGn@CRqAdLMbAK`AeAtIaA~GGd@iCrPkBxLOdAI`@[rB}@zCg@hAo@jAyArB_BzAi@d@cAn@aDbBmDhB_CjAeCnAgIfEy@`@eEtBeFjCw@d@yCzAc@TkEvBwBdAqBhAaBx@iFlCkAj@yAv@eH|CcAb@aF~AyD~@u@NoB`@}Er@UBa@DsBTkEXqDHeEFaEFO?W@qA@sBB_KN_UZqDFuEFuBDiAFmAFuBReD`@kB^iCn@K@a@LgAZ{Ah@kBn@wDdB{BnAy@d@sDrBoCxAsHhEMHqBdAGDoAp@eDnByAhAu@t@SR_BnB[d@[h@sAjC_B~CaAnBCFuElJ}CrGeBrDCF_B~CeBhDoV`g@k@jAy@hBkDhHqAdCkAdCuFbLMXcPb\\gBpDmBxDm@lAgFjKMV}GnNO\\{A|CyBpEgAdCQZaAfBiA|Bk@hAkB~DyAxCsC~FuBbEeB`DoAjCeAdBsC`GMVsAlC{@zAsE|JYh@GLGNoC|Fq@pBEPe@fBc@hCKpAEnBCb@ArATxFNhENpEJrCDjB?lAIdCG`A]dCg@|B_@fAm@dBy@`ByB|DyEdJuHrN_EfH}FfLaBbDiGpLc@x@iCbFiEdIuChFsElIy@rAeChFcAnB{LxUoIlOo@lAoFjKO\\eArCs@~BU|@Kh@K`@[zAeCbMk@nDuA`HqCpNwDtRyD~RKb@]hB_CnL}@tEeB~KkAdHE\\oA`IcDvSqAjIWxAUrAeEdX{BnNc@zBg@zBuA`F{AjEiBhEsBzDw@rAwBdDyFfI}FjIs@~@kQ~Vw@fAsNjSw@lAyAbCmBpDcDfHUd@u@dBqApCcBrDIRe@dAkBxD}@`BW`@aApAuA~AyBvBcAv@}BtA{ClB{EzC}A`A_@TiIlFmFlDoAt@mJdG_IdFoBtAoGbEgChBe@`@gAdAeBnBgBhC{A|CWp@a@bAiF`NiAlCq@hBiAtCgHbR_D`IKTs@lB[z@Qb@s@lBc@dAgBrEs@tAiAnBSZ[f@}@hAoApAcA|@_An@yGvEwB|ASLQLqM|IyFxDmBlAuDlCma@~X_S`N]TeGdEk@\\iDtBkGxDg@ZmEnCoFhDkAt@WPq@^i@X[PgAj@kCtAuCdBsDbCaMnHiJpFyBtAw@^qD|BiC|AsGlEYTkAz@e@ViB~AkBjBsF|EkM~KqBpBeAxA]j@CDg@`AOXk@zAc@nAy@lCO^[t@_A`Ba@h@_BhB]ZwCzBk@b@q@d@}B~AgCtB{BjCk@v@SZsB`DGLGL_@j@kBzCq@fAkB`D[j@c@n@q@hAm@nAyAdCoAlBuAtBmBdCcK|LiCbDoBdCqCbDmB|BaBzB{@bAuBdCyAhBsBzBmE`EgDzCmAlAs@l@_@^}@z@gA~@oEdEWVs@n@i@b@gBfBcA|@g@f@uDfDiBbBkAlAyArA_AfAuA~Am@t@qAfBgDrEmA`BqC~DmA~AyLxPaBbCUb@iAhBeCfFo@xASf@oB|Fe@`Bw@bD{@tDu@`D}@lESx@uBlJm@fB{@zAcAtA{@v@_GpE_BrAg@d@g@n@QX[n@o@dB[bBWjCQbD[vE_@fGSnC]bFUvDi@hJMtBEvACrBF~BSnJKlCIzFE`BQvCId@Q`AGVGX"}, "startLocation": {"latLng": {"latitude": 14.4545565, "longitude": 75.86881079999999}}, "endLocation": {"latLng": {"latitude": 14.7170849, "longitude": 75.483205}}, "navigationInstruction": {"maneuver": "MERGE", "instructions": "Merge onto NH 48\nToll road\nPass by By Pass Davangere (on the left in 14.5 km)"}, "localizedValues": {"distance": {"text": "53.3 km"}, "staticDuration": {"text": "46 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 506, "staticDuration": "79s", "polyline": {"encodedPolyline": "wlyxA_yukMWv@_@p@e@n@]`@SNk@h@SPo@^sAv@wBpA{@j@kB`Bg@j@"}, "startLocation": {"latLng": {"latitude": 14.7170849, "longitude": 75.483205}}, "endLocation": {"latLng": {"latitude": 14.720415000000001, "longitude": 75.4801026}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "Continue onto SH 136\nPass by <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> (on the left)"}, "localizedValues": {"distance": {"text": "0.5 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 163, "staticDuration": "15s", "polyline": {"encodedPolyline": "sazxAseukMy@bA{@pAm@x@[P"}, "startLocation": {"latLng": {"latitude": 14.720415000000001, "longitude": 75.4801026}}, "endLocation": {"latLng": {"latitude": 14.7213801, "longitude": 75.47897280000001}}, "localizedValues": {"distance": {"text": "0.2 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 40654, "staticDuration": "2050s", "polyline": {"encodedPolyline": "sgzxAq~tkMcClD{EnH_ElG_M|RwExGqCrEgGjJ}@hAeA~@{@d@i@VaBf@yATiFp@y@Rc@L_@LcAh@w@h@cBzAuFnFyMbMkAhAkPrO}BxB}CvCaXhWkAjAaBxAwBrBqChCyV~UYXk@l@m@t@aArAaAhBiGjLaB|CQ\\g@|@iAdBi@l@q@n@k@d@kAx@uDjB}Av@mAj@sA`Aw@n@s@t@y@hAo@hAk@rAqBjH]nAoBxGo@fCy@zCe@bBsArDcBbEyAnCkAjB_CxCa@d@U`@uAzAQPi@d@c@`@cPjM}BpB}AxAgAjAyBfCuCpDq@v@k@v@w@tA[r@]~@WbAQ`AObBClABvBBt@|@vMb@rGHpAHvABvCE|BQ`D_@`Dq@fEq@`EIh@Ih@oAxHy@`Fk@tDE\\]lBIj@G`@gBpLUzAc@jCwDnUoBjMg@hCi@fBWl@M^g@hAe@~@gA~AUXeDhDiDtDONoBvBeItIgLnLk@n@eAdAWVkEtEs@r@gBzAGFkChBuC|AqB|@kBr@{Bp@uGvAeIhBql@xLwHxAmGrAmE~@aE`A_Bf@mBl@gDjAsXlLUHgLvEoAd@oBr@mD`AaE~@cQxDmCj@kCj@{K`CoD|@iC~@WJe@Tw@^SJq@^eBjA_At@w@r@WVcAbAgFfGsA`B{GzHcF`GsEjFoAzAcDtDs@z@UXw@`AUZcKnLiAhAWXeEbFgEzEaAfAs@x@aHlIuAfBaAjBq@~Aw@tC]rBS|BEfBAbAL`CNjBLrAh@|FH|B@`@B|@?pAMnDSfCIj@a@bCo@jCi@fBaBtDUb@[j@{A~BmAzAoBrB[ZcJxGePxKyPjLyA`AeAt@wAbAuAtA}@~@W\\W\\wBdDcE~Gy@lAiApAURMLq@n@s@f@sBhAkAd@wC~@YHqAZw@RoHrBwGlBiGlBu@RgHxBeJhCi@Nw@VoDbAcI|Bu@Tm@PyBt@aA`@yBrAUNmAdAg@f@_HfH{FjG_SnSwDzDuQxQm@l@oFzFoFvFc@f@yE`FsN|NeQrQyLbMmDrDcFjFcMdMcDjDwLxLSPsK~KaFdFoC|C}AtAuCxC{CjDeBjBaEvEsCxCaB|A}@p@c@XcBzAkCrCSPoFjEaFrD_@VwClB}GzD[PuE|BqCjA_DlAoFfBgHpB{GbBqBj@mCr@_JtBwFrAcIvBa@LQF_A\\c@P{BbA{ClBkAz@qAlAwA|AcC|CgB~BuInLiDrE}@lAa@f@_@d@UXmA|AyEbHaCpD{BfDMRyDrFmDfFgD~EqCdEa@l@iB`CqB`C{CpDyAtAo@d@_At@eAp@eAl@sBdA}@b@kFdCeCbAaErAkJ|BE?oG`BqBh@_N~CiG|AgBn@iAj@a@RiGdDqDbB{N|HqIrEcG|CuC~A_@RuC|A}DpBwAv@mBhAKFiAv@m@d@o@h@aDzC}@z@KLWVyAzA{ArAe@d@w@v@"}, "startLocation": {"latLng": {"latitude": 14.7213801, "longitude": 75.47897280000001}}, "endLocation": {"latLng": {"latitude": 14.9750508, "longitude": 75.2328974}}, "navigationInstruction": {"maneuver": "STRAIGHT", "instructions": "Continue straight onto NH 48\nToll road\nPass by Puncture repair shop (on the left in 32.1 km)"}, "localizedValues": {"distance": {"text": "40.7 km"}, "staticDuration": {"text": "34 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 974, "staticDuration": "180s", "polyline": {"encodedPolyline": "aykzAs|djMYr@K\\uLpLg@f@oFbFeJdJWXgBfB"}, "startLocation": {"latLng": {"latitude": 14.9750508, "longitude": 75.2328974}}, "endLocation": {"latLng": {"latitude": 14.981262599999999, "longitude": 75.22654059999999}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_LEFT", "instructions": "Slight left\nPass by VISHAL DHABA (on the left in 350m)"}, "localizedValues": {"distance": {"text": "1.0 km"}, "staticDuration": {"text": "3 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 40832, "staticDuration": "2217s", "polyline": {"encodedPolyline": "{_mzA{tcjMO[eCdCe@d@aC|BmBbBo@f@oChBcEfCeAp@sA|@oA~@}AtAg@f@kCtCgBpBOPSVi@l@UVKJMNi@n@_CjCIHMNi@j@}DvEcH~HaAjAcAjAa@h@qAfBQXW\\sDjFqC~Di@v@_@h@cArAgApA}@|@sDtCyBbBuD~CeIxG_HrFeCpBmGdFCDUR]\\yAjB{AfC{AjC_A`BkDlFw@dAgBzBgEjFwE|FeBxBIJuA|BKRKTEHQ^Yt@eBlEg@tAkA`Cm@~@qCfDaCrCe@f@mCxCeA|AiAnB_D|Gg@bA{@tAkD|Ei@p@iBdB{@t@[V{@p@{@j@yCbBo@XqAf@aGhBoA\\c@Ls@NyD`@gFh@yDd@w@Ls@JsC`@{Df@eCb@oC^sA\\aCv@_Ab@gCxAcAr@iCbC{@fAyBdDkA|Bo@tAeAlCc@`AqBxCy@`A_A~@sAdA{A`AiCjAkA`@yBh@qCh@cIbB}J|B_MdCcHxAgGpAuE~@cQtDoIdB_Cr@sEdB}FjC{@`@aBz@_B`AyEtDmB~AgAdAaCxBqCjCiE~DsC|B_@\\gAp@}Az@uClAyGlC{DbBe@Pe@PQH{LhFMDiFjCeCfAaErB{BhAgEfBcFhBqGjBoUrGiB^kALo@BqA@yIOsKSqMQmCGqBEy@Cq@A}EIs@CeDEcBEu@AuHMWA_JMw@CiBEmJOqJSo@AeAC_JQsJOaA@_G`@w@BwG^_DRwDReAFg@BoHb@gDNmTjBaCZmj@`KgZpF{Dd@uKtA}AR{QzB}JjAkBRwFx@sB\\SBuAPkFp@sKpA{IjAcNjBmBT_Gr@yIdAqC^}Dd@mNfB{a@|EcCVyBNg@D}AHcFPgKTuMZsAJoE|@oBf@mHpA_CRmCAkEF{N^s@BuADeDZiBb@kFnB{DrAyBf@yAn@cD`A_ItByEjAiBf@{Bv@mAh@a@LiAh@_DrAkCt@{AZsAP}AHiABaA?}@CgAGmC]q@MmN{ByEk@oEa@sHo@iD[wFs@kFw@MA}GaAeCY{@KUCaEWuBQ[CuAM_De@cEi@{@MuLeBsCa@oBYgMgBuDi@iDe@KAaGw@sD]gAGqBMeAEiFM[?y@?yBDuDHuAFyDNuAHE@e@FK@eFT{GZu@@Q?a@?M?gBEuHm@qMkAgAMyAQiBUyCc@qCc@yE}@eGeAa@KeJaBsHuAqASaCa@gEy@w@OsCi@cAS{JiCsD_AcASu@KaCQi@CsLw@{DUg@CsAAWD}BB_IRaHNc@@wADqCDkGPwJXyELe@@mAB{KTaABkA?mAGyB]wAc@UGuDmAkDkA_DaAcAWcBW_BOw@EaKQyGKwHOaAAmBEqCEuACi@@oAEgBCiCEoIMeBB{BLqC^kCd@wE|@yCl@s@LoFdAoDl@s@Lk@FmAJgBDiBEiAGeBWc@I_@ImDs@kC_@QAeCMk@AcEAeF?S?ob@AwFBmAAk@As@?u@AyCA_A?g@@cB?_A@w@@w@DeAH}@JsAPmObCeC`@m@J}Dn@yGdASB_BXwATcCb@qA\\UJ}AtA"}, "startLocation": {"latLng": {"latitude": 14.981262599999999, "longitude": 75.22654059999999}}, "endLocation": {"latLng": {"latitude": 15.316510899999999, "longitude": 75.14338049999999}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Keep right to continue on NH 48\nPass by IMMANUEL ONLINE CENTER (on the left in 22.7 km)"}, "localizedValues": {"distance": {"text": "40.8 km"}, "staticDuration": {"text": "37 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 446, "staticDuration": "91s", "polyline": {"encodedPolyline": "eon|AcmsiMMLGHi@^_@\\{@dAk@nAg@fAg@dA}@jCM\\Wn@Qp@Wn@"}, "startLocation": {"latLng": {"latitude": 15.316510899999999, "longitude": 75.14338049999999}}, "endLocation": {"latLng": {"latitude": 15.318615999999999, "longitude": 75.139907}}, "navigationInstruction": {"maneuver": "ROUNDABOUT_LEFT", "instructions": "At the roundabout, take the 1st exit and stay on NH 48\nToll road\nPass by Jakhar Travels (on the left)\nDestination will be on the right"}, "localizedValues": {"distance": {"text": "0.4 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "149 km"}, "duration": {"text": "2 hours 17 mins"}, "staticDuration": {"text": "2 hours 17 mins"}}}, {"distanceMeters": 71431, "duration": "4413s", "staticDuration": "4413s", "polyline": {"encodedPolyline": "k|n|AmwriMAF}B`GmAzC{@vBKDCTaAlC}ApE}@dCiB`FWr@mCzGcAjCENiBvE}@fCi@`BOb@mC|HKVKVwC~H_AfCk@xAgDrJ]n@e@t@]l@k@dAg@v@sBlCu@dA{@vA[p@Sj@YfAQtAS~A]fDIr@o@dHg@bGa@xEOlBOnBOdBOvDGbAQzBSjE_@nFKhCg@|JO`C[|EMhA[fBu@pCaBtEqE|LIVqCtJoCrHGPIP{@tBc@jAu@tBUr@Uv@Cz@W~AUz@o@jBc@jAe@`AsB`EeCvFc@fAYRWJYr@cBlDQ`@g@hAyBzE_BjDqFlLm@vAq@`BSd@aAtCmBdF{BnFkC`Gc@bAyBdFEHyAlDwA~CcB|DUl@MX_DbHq@~Aw@xBq@zAk@rAo@tAwCdGc@v@i@bAiCzEU^uDzGaBzCo@jAyAtCc@x@mBtCyBdDcBpC_CxDeAzAwDbGyA|BoB|CsA`CoB|C}C~Ec@r@ILq@bAaAzAy@~Aw@fBcArBw@~AcC|E_G|KU`@aEvHuIxOuA~B{B|Dc@x@s@pAaFfJW`@kC~EiBfDqBvDeCrEi@dAcAjBcB|C_BvCi@~@GJqA~Bg@|@[f@gAxA_AbACDaCtBuAhAMLqDzCmEtDuBtBIJSPeM|My@z@gCnCaAdAeAhAyB|BaGnGsBxBwAfCe@h@wAvAoEvEaCjCmBpBiA`A_AfAeGzG{F`G[ZkErE_CbCaGhGiAjA_BbBkArAiAhA_Ar@q@\\ULm@h@QNQTo@x@eA`BkA`Ce@hAWj@uBdFeA`CyBtFyBnFi@jAe@dAkBlEaDvHiApCaErJ_EhJ{BbFgBbEiAlC{BrFaD|HmCjGcB~DIR_@x@u@hBc@`A_AlB_@l@}@vAa@j@MPg@t@e@jAs@bA{@bA_BzAs@l@kB|AUNwEpDmA|@}@l@aD|B_DnB{@f@sDpBwEzBmD~Am@\\w@\\qAf@mARqLhFcBr@eEfBeH|CuCnAWJgCfA_H|CkHzCuBt@oBb@gBZaAJgBFmA@o@AsACkBSs@KsCo@{@WgBi@kC{@wAg@_FcBi@SaFcBgDcAcAWoB_@UEyAUwC_@mAKcBGeAAg@@iADcCT{B`@e@Nk@TeB~@_Bn@aBl@cDlAaC|@{G~BaBf@qBf@kBXm@J{CXwAFk@?gA?G?eBU_EMuCKsCKmBKgAMM?}DOsAI_CGuBKk@CuEQqACaADS@O?YFo@?aF]uDYeAIw@GiBOi@EuCOeMs@oEYoCS}DWwIu@}E]eBWaDYqGe@gAIUAc@CeCOoF[kLs@yBIeDC{CAoAAU?I?kF?kDIkCKI?m@A}A@{B?O@_BAcBJw@Fi@FgAPqBj@mAd@_Bv@[BOLGBSb@gBbBeApAUXgAjBc@bAGPmAbEOx@gAzE?@oA~FWbAy@|Cw@|BgErLmAlD_BjEo@xAcAdBiBbCmErFuBhCgBvBwDvE_@d@_AlAsDxEgCzC{@|@aC`CkD|CiGtEcCdBq@d@_@VyBdBcBbBgA~As@hAgAlCMd@}BhI_AdDoAnEO`@KZ}@~CiCfJuA~E_E`N[~AK|@SzBe@hGMxAIz@aBrSUxCCVIr@OrAw@zD}@xDa@vAeAtEK`@gFfSi@pBGVe@zBm@jCy@xC{AfDiEfIc@|@wFvLkArBw@t@MLm@d@[PsAr@_A\\iCv@g@PwE~AcB|@gA~@YZUVe@r@GFw@vAa@lAk@bCcBlIsAvGWhA]zAK`@c@nAk@hAu@lAeFhI{CxEm@lAc@vA[~A]~De@tF]hBc@zAi@lAg@v@cApAYh@aDnDiBpBwInJyFjGi@j@gHnIA@q@p@uC|Cc@d@wB`CcApAq@jAo@hBS|@SzAQfFKzBKtCGvACz@ChB@xAFp@NbAxBnLRrB@nAExAOzAId@I^mBnGOb@y@hCOh@a@pAiAhDeBdEw@|Ae@~@a@t@iAnBgElHaIzMg@lAM\\Oh@SdAMrAAb@CxAAz@ArPCvM?rCLvDNpB\\bCXzAh@tB~@`Cp@vAlBnDTb@|GrMpDbHfBhDvCdIhAhDjAfDL^fAjDNr@RpBBv@@p@?\\Ax@QvBShAk@jBe@hAs@hAmAxAaAz@mBjA{@b@kAt@y@`@oBhAoAbAw@x@cAzAYh@}A~CwAnCi@z@q@z@QPON_@ZgAt@w@b@{Aj@{Ab@oG|Ao@RcBt@aAn@w@p@kDzDyClDwBbCkGbHmBtBqC`DyB`CcI~IY\\}AbBeBlBqAxAoC~Cw@x@iAz@eFrD{BbBaLbIkFvDIDMFgBnAaAv@gChB_@X}EpDoBpAaKbGgE`C{HnEwIbF}BvAMHu@j@_@\\e@h@s@lAi@lAk@bBe@dBI^ERmAhFEPK`@gAfE_@fBi@|E[jCAHSrBUtAa@xAO`@Sf@Uf@_@h@uAfBQJkOlQa@f@A@s@fAi@pA[dAO|@c@|GQlCUhDGbAQdBWrAWz@o@vAg@z@Y^g@j@_Ax@u@h@{@h@kFbDqBnA{AdAeCfBg@XID{CnBcB|@qEnBk@RcA`@gEhBm@Xw@^u@d@gAbA}AhBmApAeAfA{@z@_BbAmAj@qCt@gHhByAj@eAn@}@t@{@z@m@|@y@~AwDdIs@jAq@v@kAdAwBtAc@XEBiIfFeEdCuBrAaC|AwAdAuAfAw@z@iA~@cDhDe@d@u@x@kCtCeAdAcDjCeAr@kBnAsBlAwCtAuB|@_@NcFhBkH`C}@XqC`A_AV[DaBTeHn@}B\\qBj@WJg@VuD`CuJtGkIpFsA|@WRkEpC_@TOJqHbFs@l@eL`IkAx@eG|Ea@^y@z@_CrCcA~@aBbA_CtAaCvAwC`BiAj@{HbDYLuFlCw@VgMdGeGrCkAf@YNaBr@yAj@oBj@qAZoMrCuCt@mAf@yAt@mKhHoAx@uDhCeAn@}HjFeAp@}@d@gA\\a@NcF`BgPbFqAj@uAz@cBrAyAjAcDlC{BjBgBjAkCxAuAl@gAb@sE`BuJlDaJ~C_Bf@yBr@kHdC]HsBr@iFpB_Al@iAdAUV_AnAqCxEi@~@ILEJy@xAoAxBYf@ILINQZaA~A_A|Aa@r@iBzC[h@wFnJeKdQ_AlAqAnAy@j@iAl@yAj@_IdCqVxHwH`CeBh@sGpBkBt@KBg@VmCzAiBjAeEjCgAr@a@VgEfCiBjAeAh@_A\\kAZg@LkFrA_Cv@eDx@qA^oHrBsDdA_B`@k@NaDz@w@t@kFvAuBl@wCvAKFe@XeAl@{@h@"}, "startLocation": {"latLng": {"latitude": 15.318615999999999, "longitude": 75.139907}}, "endLocation": {"latLng": {"latitude": 15.715214500000002, "longitude": 74.7004285}}, "steps": [{"distanceMeters": 5588, "staticDuration": "462s", "polyline": {"encodedPolyline": "k|n|AmwriMAF}B`GmAzC{@vBKDCTaAlC}ApE}@dCiB`FWr@mCzGcAjCENiBvE}@fCi@`BOb@mC|HKVKVwC~H_AfCk@xAgDrJ]n@e@t@]l@k@dAg@v@sBlCu@dA{@vA[p@Sj@YfAQtAS~A]fDIr@o@dHg@bGa@xEOlBOnBOdBOvDGbAQzBSjE_@nFKhCg@|JO`C[|EMhA[fBu@pCaBtEqE|LIVqCtJoCrHGPIP{@tBc@jAu@tBUr@Uv@"}, "startLocation": {"latLng": {"latitude": 15.318615999999999, "longitude": 75.139907}}, "endLocation": {"latLng": {"latitude": 15.3361667, "longitude": 75.0918137}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head northwest on NH 48\nToll road\nPass by Ecom Express (on the right in 2.6 km)"}, "localizedValues": {"distance": {"text": "5.6 km"}, "staticDuration": {"text": "8 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 622, "staticDuration": "44s", "polyline": {"encodedPolyline": "ajr|AyjiiMCz@W~AUz@o@jBc@jAe@`AsB`EeCvFc@fAYRWJ"}, "startLocation": {"latLng": {"latitude": 15.3361667, "longitude": 75.0918137}}, "endLocation": {"latLng": {"latitude": 15.3387102, "longitude": 75.0867461}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_LEFT", "instructions": "Slight left"}, "localizedValues": {"distance": {"text": "0.6 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 23898, "staticDuration": "1720s", "polyline": {"encodedPolyline": "}yr|AekhiMYr@cBlDQ`@g@hAyBzE_BjDqFlLm@vAq@`BSd@aAtCmBdF{BnFkC`Gc@bAyBdFEHyAlDwA~CcB|DUl@MX_DbHq@~Aw@xBq@zAk@rAo@tAwCdGc@v@i@bAiCzEU^uDzGaBzCo@jAyAtCc@x@mBtCyBdDcBpC_CxDeAzAwDbGyA|BoB|CsA`CoB|C}C~Ec@r@ILq@bAaAzAy@~Aw@fBcArBw@~AcC|E_G|KU`@aEvHuIxOuA~B{B|Dc@x@s@pAaFfJW`@kC~EiBfDqBvDeCrEi@dAcAjBcB|C_BvCi@~@GJqA~Bg@|@[f@gAxA_AbACDaCtBuAhAMLqDzCmEtDuBtBIJSPeM|My@z@gCnCaAdAeAhAyB|BaGnGsBxBwAfCe@h@wAvAoEvEaCjCmBpBiA`A_AfAeGzG{F`G[ZkErE_CbCaGhGiAjA_BbBkArAiAhA_Ar@q@\\ULm@h@QNQTo@x@eA`BkA`Ce@hAWj@uBdFeA`CyBtFyBnFi@jAe@dAkBlEaDvHiApCaErJ_EhJ{BbFgBbEiAlC{BrFaD|HmCjGcB~DIR_@x@u@hBc@`A_AlB_@l@}@vAa@j@MPg@t@e@jAs@bA{@bA_BzAs@l@kB|AUNwEpDmA|@}@l@aD|B_DnB{@f@sDpBwEzBmD~Am@\\w@\\qAf@mARqLhFcBr@eEfBeH|CuCnAWJgCfA_H|CkHzCuBt@oBb@gBZaAJgBFmA@o@AsACkBSs@KsCo@{@WgBi@kC{@wAg@_FcBi@SaFcBgDcAcAWoB_@UEyAUwC_@mAKcBGeAAg@@iADcCT{B`@e@Nk@TeB~@_Bn@aBl@cDlAaC|@{G~BaBf@qBf@kBXm@J{CXwAFk@?gA?G?eBU_EMuCKsCKmBKgAMM?}DOsAI_CGuBKk@CuEQqACaADS@O?YFo@?aF]uDYeAIw@GiBOi@EuCOeMs@oEYoCS}DWwIu@}E]eBWaDYqGe@gAIUAc@CeCOoF[kLs@yBIeDC{CAoAAU?I?kF?kDIkCKI?m@A}A@{B?O@_BAcBJw@Fi@FgAPqBj@mAd@_Bv@[B"}, "startLocation": {"latLng": {"latitude": 15.3387102, "longitude": 75.0867461}}, "endLocation": {"latLng": {"latitude": 15.4935302, "longitude": 74.9688669}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_LEFT", "instructions": "Slight left at Mukthi ksetra hubli onto NH 48/NH 67\nContinue to follow NH 48\nToll road\nPass by Karnataka Vikas Grameena Bank (on the right in 11 km)"}, "localizedValues": {"distance": {"text": "23.9 km"}, "staticDuration": {"text": "29 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 40854, "staticDuration": "2145s", "polyline": {"encodedPolyline": "qaq}AmjqhMOLGBSb@gBbBeApAUXgAjBc@bAGPmAbEOx@gAzE?@oA~FWbAy@|Cw@|BgErLmAlD_BjEo@xAcAdBiBbCmErFuBhCgBvBwDvE_@d@_AlAsDxEgCzC{@|@aC`CkD|CiGtEcCdBq@d@_@VyBdBcBbBgA~As@hAgAlCMd@}BhI_AdDoAnEO`@KZ}@~CiCfJuA~E_E`N[~AK|@SzBe@hGMxAIz@aBrSUxCCVIr@OrAw@zD}@xDa@vAeAtEK`@gFfSi@pBGVe@zBm@jCy@xC{AfDiEfIc@|@wFvLkArBw@t@MLm@d@[PsAr@_A\\iCv@g@PwE~AcB|@gA~@YZUVe@r@GFw@vAa@lAk@bCcBlIsAvGWhA]zAK`@c@nAk@hAu@lAeFhI{CxEm@lAc@vA[~A]~De@tF]hBc@zAi@lAg@v@cApAYh@aDnDiBpBwInJyFjGi@j@gHnIA@q@p@uC|Cc@d@wB`CcApAq@jAo@hBS|@SzAQfFKzBKtCGvACz@ChB@xAFp@NbAxBnLRrB@nAExAOzAId@I^mBnGOb@y@hCOh@a@pAiAhDeBdEw@|Ae@~@a@t@iAnBgElHaIzMg@lAM\\Oh@SdAMrAAb@CxAAz@ArPCvM?rCLvDNpB\\bCXzAh@tB~@`Cp@vAlBnDTb@|GrMpDbHfBhDvCdIhAhDjAfDL^fAjDNr@RpBBv@@p@?\\Ax@QvBShAk@jBe@hAs@hAmAxAaAz@mBjA{@b@kAt@y@`@oBhAoAbAw@x@cAzAYh@}A~CwAnCi@z@q@z@QPON_@ZgAt@w@b@{Aj@{Ab@oG|Ao@RcBt@aAn@w@p@kDzDyClDwBbCkGbHmBtBqC`DyB`CcI~IY\\}AbBeBlBqAxAoC~Cw@x@iAz@eFrD{BbBaLbIkFvDIDMFgBnAaAv@gChB_@X}EpDoBpAaKbGgE`C{HnEwIbF}BvAMHu@j@_@\\e@h@s@lAi@lAk@bBe@dBI^ERmAhFEPK`@gAfE_@fBi@|E[jCAHSrBUtAa@xAO`@Sf@Uf@_@h@uAfBQJkOlQa@f@A@s@fAi@pA[dAO|@c@|GQlCUhDGbAQdBWrAWz@o@vAg@z@Y^g@j@_Ax@u@h@{@h@kFbDqBnA{AdAeCfBg@XID{CnBcB|@qEnBk@RcA`@gEhBm@Xw@^u@d@gAbA}AhBmApAeAfA{@z@_BbAmAj@qCt@gHhByAj@eAn@}@t@{@z@m@|@y@~AwDdIs@jAq@v@kAdAwBtAc@XEBiIfFeEdCuBrAaC|AwAdAuAfAw@z@iA~@cDhDe@d@u@x@kCtCeAdAcDjCeAr@kBnAsBlAwCtAuB|@_@NcFhBkH`C}@XqC`A_AV[DaBTeHn@}B\\qBj@WJg@VuD`CuJtGkIpFsA|@WRkEpC_@TOJqHbFs@l@eL`IkAx@eG|Ea@^y@z@_CrCcA~@aBbA_CtAaCvAwC`BiAj@{HbDYLuFlCw@VgMdGeGrCkAf@YNaBr@yAj@oBj@qAZoMrCuCt@mAf@yAt@mKhHoAx@uDhCeAn@}HjFeAp@}@d@gA\\a@NcF`BgPbFqAj@uAz@cBrAyAjAcDlC{BjBgBjAkCxAuAl@gAb@sE`BuJlDaJ~C_Bf@yBr@kHdC]HsBr@iFpB_Al@iAdAUV_AnAqCxEi@~@ILEJy@xAoAxBYf@ILINQZaA~A_A|Aa@r@iBzC[h@wFnJeKdQ_AlAqAnAy@j@iAl@yAj@_IdCqVxHwH`CeBh@sGpBkBt@KBg@VmCzAiBjAeEjCgAr@a@VgEfCiBjAeAh@_A\\kAZg@LkFrA_Cv@eDx@qA^oHrBsDdA_B`@k@NaDz@"}, "startLocation": {"latLng": {"latitude": 15.4935302, "longitude": 74.9688669}}, "endLocation": {"latLng": {"latitude": 15.711500299999999, "longitude": 74.7024187}}, "navigationInstruction": {"maneuver": "ROUNDABOUT_LEFT", "instructions": "At the roundabout, take the 1st exit and stay on NH 48\nPass by Circle bus stop (on the right in 3.8 km)"}, "localizedValues": {"distance": {"text": "40.9 km"}, "staticDuration": {"text": "36 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 469, "staticDuration": "42s", "polyline": {"encodedPolyline": "{s{~Aci}fMw@t@kFvAuBl@wCvAKFe@XeAl@{@h@"}, "startLocation": {"latLng": {"latitude": 15.711500299999999, "longitude": 74.7024187}}, "endLocation": {"latLng": {"latitude": 15.715214500000002, "longitude": 74.7004285}}, "navigationInstruction": {"maneuver": "RAMP_LEFT", "instructions": "Exit onto NH Service Road\nPass by SANTOSH (on the left in 350m)\nDestination will be on the left"}, "localizedValues": {"distance": {"text": "0.5 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "71.4 km"}, "duration": {"text": "1 hour 14 mins"}, "staticDuration": {"text": "1 hour 14 mins"}}}, {"distanceMeters": 133657, "duration": "13832s", "staticDuration": "13832s", "polyline": {"encodedPolyline": "ak|~Au||fM{@f@iKhGk@XgAn@e@VgDlB_@PHRf@p@N\\PZBBLFv@\\HNJh@h@v@T\\`A|Az@`ABZAZClABzABp@@t@?TRx@BJx@fBDRF`@Gd@Sr@CHI`@k@n@MRSl@AFAN@RF`@R~@Np@`@`B@p@?lCDn@|BpKN~@?TA^c@~Cc@vD[xCu@fH?p@?PF|AX`Cl@zEz@jGLLR@fAYNEREX@nAd@LDZTLX`AvGPfAr@zE^lCPhAb@tCn@rE`A~G`@tBBZLxAr@jF~@~Fr@jEPnAjBrNXjBp@zEv@bF^lCf@dDv@`G^bDnA~H`@vCh@tD^dCfB|LHb@`@hBJ^HVZz@n@hBb@nA|@pCj@bBfBlEhBnETl@|@dCb@dATj@|@|BPd@Tn@l@rANh@xBnGp@fB\\|@zAxDj@fAPVvApBj@t@|@lAp@x@nClDrAjBbA`Bz@lAh@z@bAfBr@|A`@n@^fADNBn@EdDA~AUxA?TCdAFrBNbAXzADR\\rAz@~DNn@hAzEXbAn@`CNf@hAzE\\zA~@`E`@|Ab@`B`A|DVnAn@hDPp@Xz@Pd@l@tAZZvAn@|@ZbAb@l@TB@b@Rn@TVHtAb@^L`@PfHxFbBjA|AfAfAdA|BxBr@p@r@p@h@b@jAv@fH~EdEbDx@h@d@^~BnBVZPXlA`D|AdEXhAz@nD|AvFp@jCFRnBrHt@tC\\rArAxEXdAxArFNh@LXt@t@rD~CtD`DzBjBnAdANb@N|@B\\NzAN|AP|BJ~@Z|CPjBRrCLnABTR|Ad@~BNl@Nj@p@dCJf@Pz@Fd@DXDdAEn@MvAFj@DP^fAn@tBXvAXtAj@hCT~@@BnAdEJ|@H~AH|AJz@V|BXhBXdCf@dETnATvA@J\\jBv@nEJl@Tv@p@bBv@rCJbADzA`@pBbAbFPx@hAnFj@nCNLhAf@PFr@XLV@h@ApAAB?fAJhA?~@QpACT@v@T`AjBjFxCpI~C`JZjAnAjD~AhE|AfEzAtEfCdHf@vA`C`HPn@L`@N\\|@dCz@xBpA|DP|@Hf@RhAJd@`ClLTfA`@xBDTNb@H^DN\\fArAlErAbErCxH^|@Pd@fAfDnB~FhBrE\\x@n@lB~BzGzAnE|@bCx@xBPj@v@vBn@tBlBfF|A`ErA|DJRlAtB\\h@PXZd@RRvDrD^^LLr@v@x@jAT~@RnALlB?DZhCD^x@pJ\\|A\\`AbAtCN`@|AhE^`ApBrFxAlDNXXv@fAzCt@dCZvAJvABj@P`Ah@hBTv@Tj@f@r@XP^PXNb@\\Ch@TdB\\|AL^\\t@Pb@j@nCFj@?Z?\\Af@O~AY~DCfAIrB?HEl@WdEG~@WpBPz@@PE^[bAa@`Aa@`AeAbC{@tBy@nB_@n@]r@i@z@Ud@kBbDM\\GPIV[fACHCHGLOZ]h@iA~Aa@r@w@hAW`@gAbAgE~CaErCa@d@mCjE[f@m@t@W`@URu@r@aEfD}FpEcAfAgAbBS^i@t@aAzAg@t@W`@sA|BU\\aE|GsCtE{AxCa@~@ABcCvFcBjDi@`BIX_BrEKZO`@Wz@MnAC~@OtAKp@c@`CSjBE|@Ed@SjA{@lCq@hBsAbDsAdD_@z@mCvGgCfGwBdFa@`Ac@`AcAbCiBhE}D~IkAjCk@xAcBfEaBfEmCfHM^Wp@eBrDw@~AsAnCyDfIs@xAqAnCiCrF}@lBaBfDwBrEyAvB_FbGeDdEi@p@}@jAyB|CWX}@hA]v@uAxDs@~BcAfCGTKv@BhAD^TfBXrAlAbEl@rALTv@lAp@n@\\VtAj@TPFL?Ze@nDFf@xAtHBR@L@zA@vAE^IVeBlBiArA{AnBa@j@m@tAIRGVu@fDG\\YdAYp@KPk@~@Oj@i@hE[rCOpBAVFp@^bALPV^j@v@?NIJkAd@Q^CJCV?vBSbCITOJsA\\gAh@eBz@oCvAKTAVHRd@~@^t@zBlCBDDRF~CF~ACJo@d@aAj@aAl@g@b@SXWt@cArDg@~Ao@dBOVg@v@W\\CB{B~Ba@^s@j@cAd@y@Tg@LwDx@MD_Ab@qAp@w@^c@Nm@HgADo@DqDl@g@HeDv@m@`@KLkEjFaDrCaGbF[XwAlAq@d@gAj@w@`@WTCBQf@CVGx@_AtEEPGf@?h@RpAPbAPj@AXJt@Rp@|A|DFf@?NAbFC~EEnF@XB~C?FNtC@F^pFRvC\\nCVbA\\p@bB~CJh@?`@?PMnAGl@QrAIZk@xBg@vAe@dA}DlIOXe@|@UdAGl@Cb@?fCBh@BTVv@bArBz@lALPTf@Z`ATfA`@pAbApCVx@~@~Ct@lBvAtCf@`ApAtC|@pBd@nANp@R`CP~A?BA^Md@[t@[l@Qp@AR?P@TDf@ZpBDl@AHEb@O^k@nA]zAi@tCW~Ae@tBIX]~@_BhDe@nAUx@YjAU~AGvA?`@CLdApCZt@`AdCTh@h@bAh@x@vBvDf@n@DDl@n@BDr@~@dBvBTd@\\t@d@h@bAjA|@jAd@b@bBhAbBhANLz@p@j@x@X`@Rl@Hr@T~CDd@?BP`B?r@I\\a@r@KPG^?Tr@bDj@jCTx@Px@hArC`CzEVf@jCjEbBrCTj@Dj@?lBBfCRpDRzC@FLt@Bv@BbB?\\FlEA`BGh@O^EF[`@uAjBgAvAQVgAbCUt@EZ?LGdAKjAOnBGvAAd@?tD?RBjD@z@CtK?tEGpCAdAAdD?|A?p@HxBD^Fb@Hd@d@~DRpFHr@Ft@TrD@`@J`BN|@Rl@f@`APRJPHd@?|@A^C|BFn@T|@d@lBDz@Cv@Gf@GVMf@y@hDUpAAxAFpDDpCBjBXxH@j@Bv@ZrDHvA?tGAvBBf@Cl@@z@@r@?fBXdDPzAHtA@x@HhEAz@@bAHlG?`A?`@Cp@YxECPMtDEl@Iv@SjAs@rDgAlEOhACVMxAGrACvAMjAShAC`@Jz@Hv@PnBd@zEB\\@p@@dACjAEr@Qx@u@lBGVGh@Bt@j@zC@n@E^ENQz@Gz@B^Pv@p@hBTl@Xz@FTb@tETp@n@zAlBhERh@LVd@bAVr@h@|ABH~@xAN^Nf@dAjGRfA`@nAxAlDp@hAHRpAlCL`@H^BZIlAATG\\KXQV]r@ERCn@KRo@d@[ZEFc@r@If@En@Df@BTLd@Rj@DX?NAn@k@|CGXYv@g@dAKZELYnBYv@Y`@aBfBw@lAUZo@r@s@|AYR[LM@i@@eAr@i@Nc@B_BKaEe@{AH_@HWPM`@@`@\\nBVtC@l@?LGhA@r@Fb@n@pAJ`@?\\?@EFCDID{BLc@DaDt@QFOHMRSr@AVFt@h@hCJt@LfALxBDjBG~AWzA_@dAu@nBGPu@dCITi@lAeA~BkC|Fw@hBaAxBwC|GQj@Ih@WhBSl@e@v@CB[d@MNUl@Wz@I\\iA~EQjAkC`PMx@Gr@?l@FVb@zA~@fCJ\\Hl@?f@Mr@kBtIIjAGfB]~I@~@JrADp@Fb@L`B?bAM|BKpBSfFCZIrAOdCK`AE^If@O`AYjB]rBOv@UbDGf@OnBCtC?~D?n@?v@AnD?bBRxAHh@LlB?t@E`@UjBOj@M\\_@lAq@`Ec@xCIxA@fCDh@b@nAb@rA`@pBb@jDBPHhAFdJ?@Gd@S\\SNIDgA`@ODo@PA@SPGPGv@QhDK|B?NDd@Nb@PXBDNRrAzAx@dAT`@\\|@Tv@Jf@R|@v@jA`@t@HP\\z@VpBRfCBbEGdAUv@Wx@@XJd@?@P\\TPv@PdAH`AVf@XTZ^x@J`@BLb@dCHh@PbAv@hCV|@`ApCd@bAnBjDv@lAN\\JXJ^|BfIJr@Br@Cz@Gx@B`@JRZV|@d@v@d@PRTt@P`APn@|AvEJh@Dd@Af@MfBUd@YVw@x@AF?LFZRh@LPDFRXh@b@bA`@zBz@fAf@l@Xp@ZPJPRLb@?H@LIx@Uz@EXEr@B\\BHXt@FLP^j@`Bj@lAZb@TVl@^`A`@^J|Ab@fA^tBnAPJZJdD^pCl@^RDHFLFp@CtACZAXDh@@BPh@x@rAR^LXL\\n@tCl@pDHvACr@e@bDg@~BIb@m@hCYlAWxBWvDA^Hd@`@r@l@bAd@x@Zf@NV^z@l@xBNj@Hd@h@pBb@t@f@l@~@jADFfAz@hAx@|AtAf@j@VRx@lAXf@PT@@JH`A~@l@f@`@Nj@Dn@FZLJPH`@XPRLNLTB\\@xCJLFb@l@NTXd@jB|CT\\~AhCbAxAvAzBDDRt@Dd@C`@AHSh@MTm@l@oAx@w@v@KJKV?^Lh@Zd@XTXBRCPIb@Od@?`@Ld@d@Vb@Pj@JpBHbDGv@U`ACFONk@VGPDZJF`@AXHNNFZ@~@@H@FLd@NTRJt@LPLFJFXH|@@p@MfC?BI\\QJQB]AS@{@RUPCNBLRNRNfBz@r@b@d@^\\`@`@fAv@jCh@bAx@rATf@DNDt@GnBa@~E?VDb@^lAfA~BjBbDh@r@XRTJnA`@dATtCD~@CtAAtDEjCC`CJ`@Dt@VTRr@jA`@lA\\|A^tB`@fBl@~B\\bAVh@bArBl@nAV\\PZXRb@RnBx@@?fCbAz@Tr@FX?NAp@KnFyA`Be@bBe@rA]dASjA?|BRx@Ld@PPJb@ZXXzBxDb@j@hBxAxAfATXHRHXD^ElAKf@On@Cd@AZ@~BJpEB`B@lALjEB`@V`CPz@r@|B@DXz@b@l@zAfBx@dAhBfB\\^lA~Ax@hAlAjALRJTBXCPa@|@G^Ax@DXCvAGl@IjAOvAa@xAm@bCYdBARAf@EXU\\{@RKNQ|@ATFRDR@|@@d@YvAQXKPg@`BYb@CLBRRt@?j@Er@ZpCAbBJzB\\hBPtB?j@CNGj@W`AIbAWt@s@dDK`A@zBItAE^ETCPBXLNNFPALGJQDIJo@Fc@Be@HeATeAl@iBx@eCTe@RWDGXWVMb@?ZBVLFLAZAJe@hCSfC?Z@HZv@ATU`@g@r@EVDl@?LKl@GXA\\Hn@R|@?h@Ch@D\\LLVLj@ZDFDDFRBLFJPVXT~@j@PRHPH^BbAMxEI^WX]Hc@@kBUI?QDIH@NNRt@t@Z\\xAp@TRXJv@VtB`AJXI`@Sr@@JJJJLv@b@Zt@Hb@F`@XlAVh@HJLDb@?ZKVOf@e@v@UfB[RMNQR]TO`@Gf@C`@M\\]RU\\SNAN?~@DxAPt@?rAQ`@Al@Cb@MZUPe@DW@K?WWu@@_@Li@r@iCf@}@Ra@l@q@dB}Ad@W|Ao@d@GbACHCRGd@[HIn@m@hBaAdAc@PELChBG@?fAYfBHn@Gl@QXUN[T}@l@eELg@HGJBHNFx@AlBGf@o@nDIPURo@^u@^y@T[B[QKA]?gAb@_AJUHUX}@rAcA|AYl@EJSTaAl@kA`@OL?Bg@fBEd@?l@DFL@LEJ]BU?KDQXc@VUn@_@|CwAVIp@Dp@ETEVKr@e@`@o@b@oABALAPBFBv@j@TFd@BPEf@Uv@w@ZKLCjBS\\OROPYBKFUBq@^u@`@w@`@}@f@w@HELGVAXEJGJML]?OSo@BWNULING~Dw@JCf@Af@HfAZ\\F~Bh@\\Dd@@f@E`@Kr@[xA[jAAv@LjDrA~@Tb@@bDF\\E^Kd@WpAi@HC\\EbAFrCFzACp@Ix@SpEAb@?fAGlAInC]XEn@Mp@Mp@MJCh@QXWp@e@TMRA^HRF|@d@h@^r@\\V@HANGdAy@\\QZE\\Df@VpA~@^P@?\\Hv@HfBDhDNb@BZBl@NNN@B@JETo@lBEL?F?ZHr@Pd@b@z@V|@@v@Ix@Q\\QTERD^J`@@h@EdAG`@@VJ\\~B|CBD@J?PMt@MzABv@D`@F^Tn@Th@@FB^CTGL]b@{@n@KJ]^c@JK?KCyAi@e@?YFIFMHa@f@_@z@WfAo@zCEh@BTBFFDf@LTBZCPDf@\\TRb@Xd@JbAAf@EbASz@Qj@SNGv@k@d@k@`@YVEV?h@J`@RtClBn@j@LRh@d@^LTDn@DjADTHTPHRj@lDJ^FJBHPn@Dj@HNFFn@`@r@n@P`@DT@VC|B@nBLl@NTRX^XPFTFvAZj@H\\DLFFT?TCDCB}@XaB`AWNEL@RLRXd@P`@AZGLIJIDs@No@V_Al@k@d@w@bA[L_@@k@MSMOK]GUBQLu@v@k@Zm@Pm@LWPCDEFCTDP\\x@HV@h@O\\WZiAvBi@t@w@v@a@^qAz@UXq@lAgAz@UVS^CFMb@An@ETO`@MP_@nAMd@Id@[bAKVaAfBYdA]|AQ^IJMP_@`@u@d@a@Rg@PoDdB]Ny@j@i@p@IX?b@BTRdB?B?JAFIt@QfAEXB`@Vn@Xf@b@n@Vb@Vr@@j@G\\Oz@@ZX~@ZfAPn@Lj@Ah@_@tAMZG`@Ev@DdC?jAB^P|@Nl@Pp@Zz@l@tB\\z@v@bATR`@l@FT@d@OlAi@nCGTy@zCKTc@~@GTS|@Cf@Lb@RZ`@^LL^p@Lj@Rz@BHDZH^b@hBLn@l@xCBdAC^CJCPq@fDI`A?RFl@ZzBNhAL`ABN`@jAx@|AZf@rA~BbAnBZf@nArBl@dAdAlB@@nBlDPVl@|@hBjC`AvA\\`@~C~Ep@hAfFxHFHn@z@V\\Xj@PTv@pA`@l@xArBXZ~@v@~ChCb@\\~ApANXZv@x@rBVr@Tr@h@nB^lAT|@FRX~Ah@|DRv@DJnAfDn@zAb@fAn@vAl@lA\\Z^NnC`AhAb@`A\\j@R`DlApBn@~CjAh@Th@^`AbAv@|@j@bAvAfDd@|AV|ATbCNdBPvA^pDt@`GHZP|@|AzFj@nB^pAt@|Af@v@^p@H\\RfDGx@Y`D_@zEDb@HL@@TJvAFRDRRLXVrARl@v@r@vBfBl@p@PZLRpCfFx@dBRd@^vALXFJt@lA|@nA`DrERVvAjBfEzFjB`CtArB~@|ANj@JfA@XD~@Lv@b@pA^n@d@b@t@l@jI~GVLt@BpB?jAIRCdAK^Dt@`@r@f@fAl@R@d@GxBc@pAC\\@|ADXHvAl@~@XfALt@Rd@Lt@XlAj@FDb@Zn@l@bAv@TTd@\\~@x@tAdAt@p@XXTXFJXn@Hf@H|@PpBDpAAfAAd@_@hC@R?HJj@FNXZrCtB^h@h@tBj@nBXn@bAbB~@fBf@`ARb@d@n@f@b@VNj@NJ@n@@j@ClFq@ZCJ?JDdAt@`@\\^Rf@Pv@NV?BUKc@k@mBUJm@\\_@\\GNKVeAtCs@~A]jBQd@EHIJGLs@bAiAbBu@bAe@n@ADAF?LA`@?BDvC?TB~BEXCJQj@mAzBe@~@sArB[XYXaA~@WRe@\\sDnBm@h@a@^QPy@`AQTe@r@k@|@INUd@gAlBm@lAk@xAEHw@zB]l@k@t@_BtB_A`BWn@aAbCUj@Qd@M^GXGv@AR?b@HfHChAKZm@rA]r@SXQHSR}@pAIXE\\I`DCtCGpCEtAAJOdAi@xBSjAALAb@\\lIIjBWdCEpCFtEAhBKh@Sh@aArCoAdEcBpH_BfHiAnFCJKXoA|Cs@rA_@p@OTe@n@QTs@z@U\\aAvAYj@ADCFEXAn@?FBrAAbAG^GN?@gB|BWj@]hAwAxHi@nCy@vDKx@e@rEK|@IVM^UFy@XgAl@WLg@V_CnAiCdAgA`@OH_EfBaBl@aA^s@\\QHQJMF_Aj@c@X{BfBeCdCKPu@jAm@lA]f@Q`@Yv@i@bAc@v@uAdDu@`BYv@c@~@w@`ByA|BMTQ^}@nBa@`AS^{ApBkAnA]Z_Ad@_@La@L}Ct@OBYDqATcEl@}@BA?cAAw@CmAJwBh@_@Nm@X{@d@OLKJOPg@n@w@nAc@n@k@x@m@hA[t@MVKVy@fBKXKVkEhKO`@y@|BGVI^YbAcAlHUtB_@dDKzAGlA?b@?bAFtB?X@d@JzD@z@N~ALhAMFcAh@gE|BkCvAaDbB}Ax@eDdBaCpAuBrAyBtAmAv@IFgBhAkDzB[TMLMLEHe@dAM`@g@|AKTY`@}BzB{AvBiBdCmBzCWb@KVYl@Sf@A@Of@qAxD[z@kAvC_A~BQ`@GPkArCMZMXQf@g@xAqAjFIz@?rCAxDWvBGfAGfA]rCM`AMbA@f@Jh@^~At@nCF`@Hd@JhAF`ADv@RnBBf@Bt@HhFEl@SbAWh@g@l@uAnAsAxAnBjECh@WfBKlAB`@`AlDH|@DhDAnDAf@?RCjACx@EzEAf@ApBA~A@d@B|@D|@BbAEjBGhBAPWpFAHGjAKr@Kp@u@hEId@Ih@?l@@vDAL_@|Bo@`D[vAGX{@rEc@fAm@~AiAdCUd@mAtB]`Ae@vBQx@Qz@ENWpAs@xDIXQj@IV_AlCiB`EOXGLo@dAEFoA~AGJiC`Ca@bAW|@YnAk@xCs@nD]vAOhAu@nEI~@@jAz@dLNpAx@xBvArDBJBZAZ}@bIGf@[dDg@lFCZMjAQjAk@|CMt@AL?bAAnAIV_@d@_@VOLEHAJOhDA^?R?ZJ^NR`AvAr@v@hBzAnB`Bt@p@TL^Pt@TnAVfARbARJDNH^^d@x@HNzAhCv@pAHPj@`BJ\\~@lDFT`@fBd@hBBFr@jCJb@F\\@HPdAVjBPvA@Hh@rEX`Av@dCLXHJ|B`DHL|A|BfA~B^j@FLNn@^lDBV@x@KxAC^YrCAJUzBYlCOjAGl@CV?x@@^P`F?l@D`BBjBBrA?P@t@JbDH|ED~AFTRzBF^d@rCRfALf@Rd@^h@t@t@~@n@dBfAbClAZNjLhHp@j@R^XNn@f@n@b@DDXRFDL@x@j@LHHFFFZ`@~AzBhAbBDDf@v@bB`CBRhAxAl@x@v@pA|@rALRFJbAj@tASHx@?Hp@Th@Pb@PnEpAxD~@XJRHDDPVDHTp@ZbAJZPh@~@|CL^f@xA\\hAPj@Nb@Fd@t@bCN^\\~@Vz@XbAHXLd@HZN`@DLRb@|@bAh@`@RLz@b@hCEBn@Jb@\\fARh@l@~Ab@nAAl@C~@Cn@?b@AlAMDi@T]Lg@`@iC`Bp@hAZf@NVPZXf@R^^n@PPb@^JVBFNdBDr@FXR`@~A_A`Aa@dBi@NE~A_@pBK^ILGj@]Na@TeAPi@Xs@bBLh@DnDTvAB`@@tAG\\C`AD|@NbATvAJdAN`@@T?N@hBFAFQdAK^UxBW`CEx@lBJPDYhBENGJABMXa@^wA`Ai@bAqAtCIL_@f@kAbCOVY|@SjAQfBKtA]~CWlCGv@UvCE`@WxBMfAIPq@A}A_@c@Ky@AA?_@B}ANs@DGJA@@JLDnBR^LVT^t@j@fAZt@DT@JAlB@VCbCEhCAn@Kf@Qn@[jAEd@KVILiApAw@r@o@`AITKh@OrBOz@]dAQh@Iv@?dE?JCr@A~BGbDEdA?NAp@B|@Bh@TbC@^@`AGlEI~EC|@Mt@K\\]nAWnAOlAGdA@bAJr@XjAJXh@x@fDrDp@x@VHzCbB`@b@RV`@p@d@f@|B`Bp@l@zArAV\\d@t@LRVt@\\jA`@`BPp@j@lBfA`D`@jBJr@TjATnAd@pC^lBh@pBL`@Lb@`@lBNzB@TFd@BTPz@@d@AZGvA?d@Z`BH\\VrC?LIjCwAPWJGL_@vCCFSHaCr@KLKRMZqBnEA@i@|Be@jCWxE}@fGWfCEj@Gj@ENKLoDvCCTDlBA`F[vBK`@Ob@OZOl@c@tBYv@SzBO`@CNaDvD@FIV{@jAO`@ALL|B@Lb@pCJ`APtEDVDb@P~CTnC?P?p@BX`AzGFZH\\N^Vv@ZbBN`B@LKnBK~@w@AmADaBLqBN"}, "startLocation": {"latLng": {"latitude": 15.715214500000002, "longitude": 74.7004285}}, "endLocation": {"latLng": {"latitude": 15.596807900000002, "longitude": 73.7403234}}, "steps": [{"distanceMeters": 531, "staticDuration": "85s", "polyline": {"encodedPolyline": "ak|~Au||fM{@f@iKhGk@XgAn@e@VgDlB_@P"}, "startLocation": {"latLng": {"latitude": 15.715214500000002, "longitude": 74.7004285}}, "endLocation": {"latLng": {"latitude": 15.7192481, "longitude": 74.69777309999999}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head northwest on NH Service Road toward SH 138/SH31\nPass by Aslesha Farms (on the right)"}, "localizedValues": {"distance": {"text": "0.5 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 781, "staticDuration": "170s", "polyline": {"encodedPolyline": "id}~Aal|fMHRf@p@N\\PZBBLFv@\\HNJh@h@v@T\\`A|Az@`ABZAZClABzABp@@t@?TRx@BJx@fBDRF`@Gd@Sr@CHI`@k@n@MR"}, "startLocation": {"latLng": {"latitude": 15.7192481, "longitude": 74.69777309999999}}, "endLocation": {"latLng": {"latitude": 15.717384999999998, "longitude": 74.69149279999999}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left at Khan Fruits Shop onto SH 138/SH31\nPass by SHRI DANESHWARI CLOTH EMPORIUM (on the right)"}, "localizedValues": {"distance": {"text": "0.8 km"}, "staticDuration": {"text": "3 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 39684, "staticDuration": "4064s", "polyline": {"encodedPolyline": "sx|~Ayd{fMSl@<PERSON>FAN@RF`@R~@Np@`@`B@p@?lCDn@|BpKN~@?TA^c@~Cc@vD[xCu@fH?p@?PF|AX`Cl@zEz@jGLLR@fAYNEREX@nAd@LDZTLX`AvGPfAr@zE^lCPhAb@tCn@rE`A~G`@tBBZLxAr@jF~@~Fr@jEPnAjBrNXjBp@zEv@bF^lCf@dDv@`G^bDnA~H`@vCh@tD^dCfB|LHb@`@hBJ^HVZz@n@hBb@nA|@pCj@bBfBlEhBnETl@|@dCb@dATj@|@|BPd@Tn@l@rANh@xBnGp@fB\\|@zAxDj@fAPVvApBj@t@|@lAp@x@nClDrAjBbA`Bz@lAh@z@bAfBr@|A`@n@^fADNBn@EdDA~AUxA?TCdAFrBNbAXzADR\\rAz@~DNn@hAzEXbAn@`CNf@hAzE\\zA~@`E`@|Ab@`B`A|DVnAn@hDPp@Xz@Pd@l@tAZZvAn@|@ZbAb@l@TB@b@Rn@TVHtAb@^L`@PfHxFbBjA|AfAfAdA|BxBr@p@r@p@h@b@jAv@fH~EdEbDx@h@d@^~BnBVZPXlA`D|AdEXhAz@nD|AvFp@jCFRnBrHt@tC\\rArAxEXdAxArFNh@LXt@t@rD~CtD`DzBjBnAdANb@N|@B\\NzAN|AP|BJ~@Z|CPjBRrCLnABTR|Ad@~BNl@Nj@p@dCJf@Pz@Fd@DXDdAEn@MvAFj@DP^fAn@tBXvAXtAj@hCT~@@BnAdEJ|@H~AH|AJz@V|BXhBXdCf@dETnATvA@J\\jBv@nEJl@Tv@p@bBv@rCJbADzA`@pBbAbFPx@hAnFj@nCNLhAf@PFr@XLV@h@ApAAB?fAJhA?~@QpACT@v@T`AjBjFxCpI~C`JZjAnAjD~AhE|AfEzAtEfCdHf@vA`C`HPn@L`@N\\|@dCz@xBpA|DP|@Hf@RhAJd@`ClLTfA`@xBDTNb@H^DN\\fArAlErAbErCxH^|@Pd@fAfDnB~FhBrE\\x@n@lB~BzGzAnE|@bCx@xBPj@v@vBn@tBlBfF|A`ErA|DJRlAtB\\h@PXZd@RRvDrD^^LLr@v@x@jAT~@RnALlB?DZhCD^x@pJ\\|A\\`AbAtCN`@|AhE^`ApBrFxAlDNXXv@fAzCt@dCZvAJvABj@P`Ah@hBTv@Tj@f@r@XP^PXNb@\\Ch@TdB\\|AL^\\t@Pb@j@nCFj@?Z?\\Af@O~AY~DCfAIrB?HEl@WdEG~@WpBPz@@PE^[bAa@`Aa@`AeAbC{@tBy@nB_@n@]r@i@z@Ud@kBbDM\\GPIV[fACHCHGLOZ]h@iA~Aa@r@w@hAW`@gAbAgE~CaErCa@d@mCjE[f@m@t@W`@URu@r@aEfD}FpEcAfAgAbBS^i@t@aAzAg@t@W`@sA|BU\\aE|GsCtE{AxCa@~@ABcCvFcBjDi@`BIX_BrEKZO`@Wz@MnAC~@OtAKp@c@`CSjBE|@Ed@SjA{@lCq@hBsAbDsAdD_@z@mCvGgCfGwBdFa@`Ac@`AcAbCiBhE}D~IkAjCk@xAcBfEaBfEmCfHM^Wp@eBrDw@~AsAnCyDfIs@xAqAnCiCrF}@lBaBfDwBrEyAvB_FbGeDdEi@p@}@jAyB|CWX}@hA]v@uAxDs@~BcAfCGTKv@BhAD^TfBXrAlAbEl@rALTv@lAp@n@\\VtAj@TPFL?Ze@nDFf@xAtHBR@L@zA@vAE^IVeBlBiArA{AnBa@j@m@tAIRGVu@fDG\\YdAYp@KPk@~@Oj@i@hE[rCOpBAVFp@^bALPV^j@v@?NIJkAd@Q^CJCV?vBSbCITOJsA\\gAh@eBz@oCvAKTAVHRd@~@^t@zBlCBDDRF~CF~ACJo@d@aAj@aAl@g@b@SXWt@cArDg@~Ao@dBOVg@v@W\\CB{B~Ba@^s@j@cAd@y@Tg@LwDx@MD_Ab@qAp@w@^c@Nm@HgADo@DqDl@g@HeDv@m@`@KLkEjFaDrCaGbF[XwAlAq@d@gAj@w@`@WTCBQf@CVGx@_AtEEPGf@?h@RpAPbAPj@AXJt@Rp@|A|DFf@?NAbFC~EEnF@XB~C?FNtC@F^pFRvC\\nCVbA\\p@bB~CJh@?`@?PMnAGl@QrAIZk@xBg@vAe@dA}DlIOXe@|@UdAGl@Cb@?fCBh@BTVv@bArBz@lALPTf@Z`ATfA`@pAbApCVx@~@~Ct@lBvAtCf@`ApAtC|@pBd@nANp@R`CP~A?BA^Md@[t@[l@Qp@AR?P@TDf@ZpBDl@AHEb@O^k@nA]zAi@tCW~Ae@tBIX]~@_BhDe@nAUx@YjAU~AGvA?`@CL"}, "startLocation": {"latLng": {"latitude": 15.717384999999998, "longitude": 74.69149279999999}}, "endLocation": {"latLng": {"latitude": 15.695643500000001, "longitude": 74.3699954}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_LEFT", "instructions": "Slight left onto MK Hubli - Khanapur Rd/SH31\nContinue to follow SH31\nPass by <PERSON><PERSON>ee Gajanana Online Centre (on the left in 950m)"}, "localizedValues": {"distance": {"text": "39.7 km"}, "staticDuration": {"text": "1 hour 8 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 56815, "staticDuration": "5448s", "polyline": {"encodedPolyline": "wpx~Aok|dMdApCZt@`AdCTh@h@bAh@x@vBvDf@n@DDl@n@BDr@~@dBvBTd@\\t@d@h@bAjA|@jAd@b@bBhAbBhANLz@p@j@x@X`@Rl@Hr@T~CDd@?BP`B?r@I\\a@r@KPG^?Tr@bDj@jCTx@Px@hArC`CzEVf@jCjEbBrCTj@Dj@?lBBfCRpDRzC@FLt@Bv@BbB?\\FlEA`BGh@O^EF[`@uAjBgAvAQVgAbCUt@EZ?LGdAKjAOnBGvAAd@?tD?RBjD@z@CtK?tEGpCAdAAdD?|A?p@HxBD^Fb@Hd@d@~DRpFHr@Ft@TrD@`@J`BN|@Rl@f@`APRJPHd@?|@A^C|BFn@T|@d@lBDz@Cv@Gf@GVMf@y@hDUpAAxAFpDDpCBjBXxH@j@Bv@ZrDHvA?tGAvBBf@Cl@@z@@r@?fBXdDPzAHtA@x@HhEAz@@bAHlG?`A?`@Cp@YxECPMtDEl@Iv@SjAs@rDgAlEOhACVMxAGrACvAMjAShAC`@Jz@Hv@PnBd@zEB\\@p@@dACjAEr@Qx@u@lBGVGh@Bt@j@zC@n@E^ENQz@Gz@B^Pv@p@hBTl@Xz@FTb@tETp@n@zAlBhERh@LVd@bAVr@h@|ABH~@xAN^Nf@dAjGRfA`@nAxAlDp@hAHRpAlCL`@H^BZIlAATG\\KXQV]r@ERCn@KRo@d@[ZEFc@r@If@En@Df@BTLd@Rj@DX?NAn@k@|CGXYv@g@dAKZELYnBYv@Y`@aBfBw@lAUZo@r@s@|AYR[LM@i@@eAr@i@Nc@B_BKaEe@{AH_@HWPM`@@`@\\nBVtC@l@?LGhA@r@Fb@n@pAJ`@?\\?@EFCDID{BLc@DaDt@QFOHMRSr@AVFt@h@hCJt@LfALxBDjBG~AWzA_@dAu@nBGPu@dCITi@lAeA~BkC|Fw@hBaAxBwC|GQj@Ih@WhBSl@e@v@CB[d@MNUl@Wz@I\\iA~EQjAkC`PMx@Gr@?l@FVb@zA~@fCJ\\Hl@?f@Mr@kBtIIjAGfB]~I@~@JrADp@Fb@L`B?bAM|BKpBSfFCZIrAOdCK`AE^If@O`AYjB]rBOv@UbDGf@OnBCtC?~D?n@?v@AnD?bBRxAHh@LlB?t@E`@UjBOj@M\\_@lAq@`Ec@xCIxA@fCDh@b@nAb@rA`@pBb@jDBPHhAFdJ?@Gd@S\\SNIDgA`@ODo@PA@SPGPGv@QhDK|B?NDd@Nb@PXBDNRrAzAx@dAT`@\\|@Tv@Jf@R|@v@jA`@t@HP\\z@VpBRfCBbEGdAUv@Wx@@XJd@?@P\\TPv@PdAH`AVf@XTZ^x@J`@BLb@dCHh@PbAv@hCV|@`ApCd@bAnBjDv@lAN\\JXJ^|BfIJr@Br@Cz@Gx@B`@JRZV|@d@v@d@PRTt@P`APn@|AvEJh@Dd@Af@MfBUd@YVw@x@AF?LFZRh@LPDFRXh@b@bA`@zBz@fAf@l@Xp@ZPJPRLb@?H@LIx@Uz@EXEr@B\\BHXt@FLP^j@`Bj@lAZb@TVl@^`A`@^J|Ab@fA^tBnAPJZJdD^pCl@^RDHFLFp@CtACZAXDh@@BPh@x@rAR^LXL\\n@tCl@pDHvACr@e@bDg@~BIb@m@hCYlAWxBWvDA^Hd@`@r@l@bAd@x@Zf@NV^z@l@xBNj@Hd@h@pBb@t@f@l@~@jADFfAz@hAx@|AtAf@j@VRx@lAXf@PT@@JH`A~@l@f@`@Nj@Dn@FZLJPH`@XPRLNLTB\\@xCJLFb@l@NTXd@jB|CT\\~AhCbAxAvAzBDDRt@Dd@C`@AHSh@MTm@l@oAx@w@v@KJKV?^Lh@Zd@XTXBRCPIb@Od@?`@Ld@d@Vb@Pj@JpBHbDGv@U`ACFONk@VGPDZJF`@AXHNNFZ@~@@H@FLd@NTRJt@LPLFJFXH|@@p@MfC?BI\\QJQB]AS@{@RUPCNBLRNRNfBz@r@b@d@^\\`@`@fAv@jCh@bAx@rATf@DNDt@GnBa@~E?VDb@^lAfA~BjBbDh@r@XRTJnA`@dATtCD~@CtAAtDEjCC`CJ`@Dt@VTRr@jA`@lA\\|A^tB`@fBl@~B\\bAVh@bArBl@nAV\\PZXRb@RnBx@@?fCbAz@Tr@FX?NAp@KnFyA`Be@bBe@rA]dASjA?|BRx@Ld@PPJb@ZXXzBxDb@j@hBxAxAfATXHRHXD^ElAKf@On@Cd@AZ@~BJpEB`B@lALjEB`@V`CPz@r@|B@DXz@b@l@zAfBx@dAhBfB\\^lA~Ax@hAlAjALRJTBXCPa@|@G^Ax@DXCvAGl@IjAOvAa@xAm@bCYdBARAf@EXU\\{@RKNQ|@ATFRDR@|@@d@YvAQXKPg@`BYb@CLBRRt@?j@Er@ZpCAbBJzB\\hBPtB?j@CNGj@W`AIbAWt@s@dDK`A@zBItAE^ETCPBXLNNFPALGJQDIJo@Fc@Be@HeATeAl@iBx@eCTe@RWDGXWVMb@?ZBVLFLAZAJe@hCSfC?Z@HZv@ATU`@g@r@EVDl@?LKl@GXA\\Hn@R|@?h@Ch@D\\LLVLj@ZDFDDFRBLFJPVXT~@j@PRHPH^BbAMxEI^WX]Hc@@kBUI?QDIH@NNRt@t@Z\\xAp@TRXJv@VtB`AJXI`@Sr@@JJJJLv@b@Zt@Hb@F`@XlAVh@HJLDb@?ZKVOf@e@v@UfB[RMNQR]TO`@Gf@C`@M\\]RU\\SNAN?~@DxAPt@?rAQ`@Al@Cb@MZUPe@DW@K?WWu@@_@Li@r@iCf@}@Ra@l@q@dB}Ad@W|Ao@d@GbACHCRGd@[HIn@m@hBaAdAc@PELChBG@?fAYfBHn@Gl@QXUN[T}@l@eELg@HGJBHNFx@AlBGf@o@nDIPURo@^u@^y@T[B[QKA]?gAb@_AJUHUX}@rAcA|AYl@EJSTaAl@kA`@OL?Bg@fBEd@?l@DFL@LEJ]BU?KDQXc@VUn@_@|CwAVIp@Dp@ETEVKr@e@`@o@b@oABALAPBFBv@j@TFd@BPEf@Uv@w@ZKLCjBS\\OROPYBKFUBq@^u@`@w@`@}@f@w@HELGVAXEJGJML]?OSo@BWNULING~Dw@JCf@Af@HfAZ\\F~Bh@\\Dd@@f@E`@Kr@[xA[jAAv@LjDrA~@Tb@@bDF\\E^Kd@WpAi@HC\\EbAFrCFzACp@Ix@SpEAb@?fAGlAInC]XEn@Mp@Mp@MJCh@QXWp@e@TMRA^HRF|@d@h@^r@\\V@HANGdAy@\\QZE\\Df@VpA~@^P@?\\Hv@HfBDhDNb@BZBl@NNN@B@JETo@lBEL?F?ZHr@Pd@b@z@V|@@v@Ix@Q\\QTERD^J`@@h@EdAG`@@VJ\\~B|CBD@J?PMt@MzABv@D`@F^Tn@Th@@FB^CTGL]b@{@n@KJ]^c@JK?KCyAi@e@?YFIFMHa@f@_@z@WfAo@zCEh@BTBFFDf@LTBZCPDf@\\TRb@Xd@JbAAf@EbASz@Qj@SNGv@k@d@k@`@YVEV?h@J`@RtClBn@j@LRh@d@^LTDn@DjADTHTPHRj@lDJ^FJBHPn@Dj@HNFFn@`@r@n@P`@DT@VC|B@nBLl@NTRX^XPFTFvAZj@H\\DLFFT?TCDCB}@XaB`AWNEL@RLRXd@P`@AZGLIJIDs@No@V_Al@k@d@w@bA[L_@@k@MSMOK]GUBQLu@v@k@Zm@Pm@LWPCDEFCTDP\\x@HV@h@O\\WZiAvBi@t@w@v@a@^qAz@UXq@lAgAz@UVS^CFMb@An@ETO`@MP_@nAMd@Id@[bAKVaAfBYdA]|AQ^IJMP_@`@u@d@a@Rg@PoDdB]Ny@j@i@p@IX?b@BTRdB?B?JAFIt@QfAEXB`@Vn@Xf@b@n@Vb@Vr@@j@G\\Oz@@ZX~@ZfAPn@Lj@Ah@_@tAMZG`@Ev@DdC?jAB^P|@Nl@Pp@Zz@l@tB\\z@v@bATR`@l@FT@d@OlAi@nCGTy@zCKTc@~@GTS|@Cf@Lb@RZ`@^LL^p@Lj@Rz@BHDZH^b@hBLn@l@xCBdAC^CJCPq@fDI`A?RFl@ZzBNhAL`ABN`@jAx@|AZf@rA~BbAnBZf@nArBl@dAdAlB@@nBlDPVl@|@hBjC`AvA\\`@~C~Ep@hAfFxHFHn@z@V\\Xj@PTv@pA`@l@xArBXZ~@v@~ChCb@\\~ApANXZv@x@rBVr@Tr@h@nB^lAT|@FRX~Ah@|DRv@DJnAfDn@zAb@fAn@vAl@lA\\Z^NnC`AhAb@`A\\j@R`DlApBn@~CjAh@Th@^`AbAv@|@j@bAvAfDd@|AV|ATbCNdBPvA^pDt@`GHZP|@|AzFj@nB^pAt@|Af@v@^p@H\\RfDGx@Y`D_@zEDb@HL@@TJvAFRDRRLXVrARl@v@r@vBfBl@p@PZLRpCfFx@dBRd@^vALXFJt@lA|@nA`DrERVvAjBfEzFjB`CtArB~@|ANj@JfA@XD~@Lv@b@pA^n@d@b@t@l@jI~GVLt@BpB?jAIRCdAK^Dt@`@r@f@fAl@R@d@GxBc@pAC\\@|ADXHvAl@~@XfALt@Rd@Lt@XlAj@FDb@Zn@l@bAv@TTd@\\~@x@tAdAt@p@XXTXFJXn@Hf@H|@PpBDpAAfAAd@_@hC@R?HJj@FNXZrCtB^h@h@tBj@nBXn@bAbB~@fBf@`ARb@d@n@f@b@VNj@NJ@n@@j@ClFq@ZCJ?JDdAt@`@\\^Rf@Pv@NV?"}, "startLocation": {"latLng": {"latitude": 15.695643500000001, "longitude": 74.3699954}}, "endLocation": {"latLng": {"latitude": 15.564951800000001, "longitude": 74.00818559999999}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left at jamboti cross onto NH 748AA\nPass by Shri <PERSON>humika Mata Temple (on the left in 53.1 km)"}, "localizedValues": {"distance": {"text": "56.8 km"}, "staticDuration": {"text": "1 hour 31 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 97, "staticDuration": "19s", "polyline": {"encodedPolyline": "}__~AevubMBUKc@k@mB"}, "startLocation": {"latLng": {"latitude": 15.564951800000001, "longitude": 74.00818559999999}}, "endLocation": {"latLng": {"latitude": 15.565212700000002, "longitude": 74.0090276}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left at Corner photo studio & stationery to stay on NH 748AA\nPass by the pharmacy (on the left)"}, "localizedValues": {"distance": {"text": "0.1 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 5706, "staticDuration": "566s", "polyline": {"encodedPolyline": "qa_~Am{ub<PERSON><PERSON><PERSON>@\\_@\\GNKVeAtCs@~A]jBQd@EHIJGLs@bAiAbBu@bAe@n@ADAF?LA`@?BDvC?TB~BEXCJQj@mAzBe@~@sArB[XYXaA~@WRe@\\sDnBm@h@a@^QPy@`AQTe@r@k@|@INUd@gAlBm@lAk@xAEHw@zB]l@k@t@_BtB_A`BWn@aAbCUj@Qd@M^GXGv@AR?b@HfHChAKZm@rA]r@SXQHSR}@pAIXE\\I`DCtCGpCEtAAJOdAi@xBSjAALAb@\\lIIjBWdCEpCFtEAhBKh@Sh@aArCoAdEcBpH_BfHiAnFCJKXoA|Cs@rA_@p@OTe@n@QTs@z@U\\aAvAYj@ADCFEXAn@?FBrAAbAG^GN?@gB|BWj@]hAwAxHi@nCy@vDKx@e@rEK|@IVM^"}, "startLocation": {"latLng": {"latitude": 15.565212700000002, "longitude": 74.0090276}}, "endLocation": {"latLng": {"latitude": 15.583644000000001, "longitude": 73.96155139999999}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left at Dessai Nagar onto Mapusa - Valpoi Rd/SH 1\nContinue to follow SH 1\nPass by Shri Dattatreya Temple (on the right)"}, "localizedValues": {"distance": {"text": "5.7 km"}, "staticDuration": {"text": "9 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 4137, "staticDuration": "280s", "polyline": {"encodedPolyline": "wtb~AurlbMUFy@XgAl@WLg@V_CnAiCdAgA`@OH_EfBaBl@aA^s@\\QHQJMF_Aj@c@X{BfBeCdCKPu@jAm@lA]f@Q`@Yv@i@bAc@v@uAdDu@`BYv@c@~@w@`ByA|BMTQ^}@nBa@`AS^{ApBkAnA]Z_Ad@_@La@L}Ct@OBYDqATcEl@}@BA?cAAw@CmAJwBh@_@Nm@X{@d@OLKJOPg@n@w@nAc@n@k@x@m@hA[t@MVKVy@fBKXKVkEhKO`@y@|BGVI^YbAcAlHUtB_@dDKzAGlA?b@?bAFtB?X@d@JzD@z@N~ALhA"}, "startLocation": {"latLng": {"latitude": 15.583644000000001, "longitude": 73.96155139999999}}, "endLocation": {"latLng": {"latitude": 15.6050765, "longitude": 73.9342649}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right onto Bicholim City Bypass Rd\nPass by Bicholim City Bypass(Police checkpoint) (on the right in 1.1 km)"}, "localizedValues": {"distance": {"text": "4.1 km"}, "staticDuration": {"text": "5 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 13798, "staticDuration": "1457s", "polyline": {"encodedPolyline": "wzf~AchgbMMFcAh@gE|BkCvAaDbB}Ax@eDdBaCpAuBrAyBtAmAv@IFgBhAkDzB[TMLMLEHe@dAM`@g@|AKTY`@}BzB{AvBiBdCmBzCWb@KVYl@Sf@A@Of@qAxD[z@kAvC_A~BQ`@GPkArCMZMXQf@g@xAqAjFIz@?rCAxDWvBGfAGfA]rCM`AMbA@f@Jh@^~At@nCF`@Hd@JhAF`ADv@RnBBf@Bt@HhFEl@SbAWh@g@l@uAnAsAxAnBjECh@WfBKlAB`@`AlDH|@DhDAnDAf@?RCjACx@EzEAf@ApBA~A@d@B|@D|@BbAEjBGhBAPWpFAHGjAKr@Kp@u@hEId@Ih@?l@@vDAL_@|Bo@`D[vAGX{@rEc@fAm@~AiAdCUd@mAtB]`Ae@vBQx@Qz@ENWpAs@xDIXQj@IV_AlCiB`EOXGLo@dAEFoA~AGJiC`Ca@bAW|@YnAk@xCs@nD]vAOhAu@nEI~@@jAz@dLNpAx@xBvArDBJBZAZ}@bIGf@[dDg@lFCZMjAQjAk@|CMt@AL?bAAnAIV_@d@_@VOLEHAJOhDA^?R?ZJ^NR`AvAr@v@hBzAnB`Bt@p@TL^Pt@TnAVfARbARJDNH^^d@x@HNzAhCv@pAHPj@`BJ\\~@lDFT`@fBd@hBBFr@jCJb@F\\@HPdAVjBPvA@Hh@rEX`Av@dCLXHJ|B`DHL|A|BfA~B^j@FLNn@^lDBV@x@KxAC^YrCAJUzBYlCOjAGl@CV?x@@^P`F?l@D`BBjBBrA?P@t@JbDH|ED~AFTRzBF^d@rCRfALf@Rd@^h@t@t@~@n@dBfAbClAZNjLhHp@j@R^XNn@f@n@b@DDXRFDL@x@j@LHHFFFZ`@~AzBhAbBDDf@v@bB`CBRhAxAl@x@v@pA|@rALRFJbAj@tAS"}, "startLocation": {"latLng": {"latitude": 15.6050765, "longitude": 73.9342649}}, "endLocation": {"latLng": {"latitude": 15.608314299999998, "longitude": 73.8240707}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at Sharvani onto Mapusa - Bicholim Rd\nPass by Dhumaskar Hospital (on the right in 9.3 km)"}, "localizedValues": {"distance": {"text": "13.8 km"}, "staticDuration": {"text": "24 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 32, "staticDuration": "11s", "polyline": {"encodedPolyline": "}ng~AmwqaMHx@"}, "startLocation": {"latLng": {"latitude": 15.608314299999998, "longitude": 73.8240707}}, "endLocation": {"latLng": {"latitude": 15.6082638, "longitude": 73.8237802}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at Karaswada Char rasta to stay on Mapusa - Bicholim Rd"}, "localizedValues": {"distance": {"text": "32 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 6, "staticDuration": "1s", "polyline": {"encodedPolyline": "sng~AsuqaM?H"}, "startLocation": {"latLng": {"latitude": 15.6082638, "longitude": 73.8237802}}, "endLocation": {"latLng": {"latitude": 15.608259999999998, "longitude": 73.82372649999999}}, "navigationInstruction": {"maneuver": "STRAIGHT", "instructions": "Continue straight past Mapusa"}, "localizedValues": {"distance": {"text": "6 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 1194, "staticDuration": "170s", "polyline": {"encodedPolyline": "sng~<PERSON><PERSON><PERSON><PERSON><PERSON>@Th@Pb@PnEpAxD~@XJRHDDPVDHTp@ZbAJZPh@~@|CL^f@xA\\hAPj@Nb@Fd@t@bCN^\\~@Vz@XbAHXLd@HZN`@DLRb@|@bAh@`@RLz@b@"}, "startLocation": {"latLng": {"latitude": 15.608259999999998, "longitude": 73.82372649999999}}, "endLocation": {"latLng": {"latitude": 15.6018652, "longitude": 73.8156767}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left at Domino's Pizza onto Duler Ground Rd\nPass by Mayur Plaza (on the right)"}, "localizedValues": {"distance": {"text": "1.2 km"}, "staticDuration": {"text": "3 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 77, "staticDuration": "13s", "polyline": {"encodedPolyline": "uff~A_cpaMhCE"}, "startLocation": {"latLng": {"latitude": 15.6018652, "longitude": 73.8156767}}, "endLocation": {"latLng": {"latitude": 15.6011764, "longitude": 73.81571319999999}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_LEFT", "instructions": "Slight left at Dhuler to stay on Duler Ground Rd\nPass by JAWA (on the right)"}, "localizedValues": {"distance": {"text": "77 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 364, "staticDuration": "60s", "polyline": {"encodedPolyline": "kbf~AecpaMBn@Jb@\\fARh@l@~Ab@nAAl@C~@Cn@?b@AlA"}, "startLocation": {"latLng": {"latitude": 15.6011764, "longitude": 73.81571319999999}}, "endLocation": {"latLng": {"latitude": 15.***************, "longitude": 73.**************}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right after <PERSON> Apartment (on the right)\nPass by Shiv<PERSON>ing (on the left)"}, "localizedValues": {"distance": {"text": "0.4 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 53, "staticDuration": "9s", "polyline": {"encodedPolyline": "c~e~A_ooaMMDi@T]L"}, "startLocation": {"latLng": {"latitude": 15.***************, "longitude": 73.**************}}, "endLocation": {"latLng": {"latitude": 15.6009265, "longitude": 73.8122654}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at The Saraswat Co-op Bank Limited onto Balkrishna Bhosale Marg\nPass by Bharat Laundry (on the left)"}, "localizedValues": {"distance": {"text": "53 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 122, "staticDuration": "21s", "polyline": {"encodedPolyline": "y`f~AumoaMg@`@iC`B"}, "startLocation": {"latLng": {"latitude": 15.6009265, "longitude": 73.8122654}}, "endLocation": {"latLng": {"latitude": 15.***************, "longitude": 73.8116091}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_LEFT", "instructions": "Slight left at Sahniwas Apartments onto Duler Hill Rd"}, "localizedValues": {"distance": {"text": "0.1 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 364, "staticDuration": "54s", "polyline": {"encodedPolyline": "kff~AqioaMp@hAZf@NVPZXf@R^^n@PPb@^JVBFNdBDr@FXR`@"}, "startLocation": {"latLng": {"latitude": 15.***************, "longitude": 73.8116091}}, "endLocation": {"latLng": {"latitude": 15.6002738, "longitude": 73.8087049}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left at The Event Co.\nPass by <PERSON><PERSON><PERSON> (on the left)"}, "localizedValues": {"distance": {"text": "0.4 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 467, "staticDuration": "54s", "polyline": {"encodedPolyline": "u|e~AkwnaM~A_A`Aa@dBi@NE~A_@pBK^ILGj@]Na@TeAPi@Xs@"}, "startLocation": {"latLng": {"latitude": 15.6002738, "longitude": 73.8087049}}, "endLocation": {"latLng": {"latitude": 15.5969595, "longitude": 73.81087579999999}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left at Meenaxi Tailor onto Duler-Marna-Siolim Rd\nPass by Woodfire Station (on the right)"}, "localizedValues": {"distance": {"text": "0.5 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 841, "staticDuration": "124s", "polyline": {"encodedPolyline": "_he~A_eoa<PERSON>b<PERSON>h@DnDTvAB`@@tAG\\C`AD|@NbATvAJdAN`@@T?N@hBFAFQdAK^UxBW`CEx@"}, "startLocation": {"latLng": {"latitude": 15.5969595, "longitude": 73.81087579999999}}, "endLocation": {"latLng": {"latitude": 15.591937799999998, "longitude": 73.808182}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at S<PERSON><PERSON> Mahamaya Telecom Services\nPass by <PERSON><PERSON> (on the left)"}, "localizedValues": {"distance": {"text": "0.8 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 62, "staticDuration": "17s", "polyline": {"encodedPolyline": "shd~ActnaMlBJ"}, "startLocation": {"latLng": {"latitude": 15.591937799999998, "longitude": 73.808182}}, "endLocation": {"latLng": {"latitude": 15.591385199999998, "longitude": 73.80812209999999}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left at <PERSON><PERSON><PERSON> Bullet mechanic toward Anjuna Mapusa Rd/Mapusa - Anjuna - Chapora Rd"}, "localizedValues": {"distance": {"text": "62 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 5452, "staticDuration": "718s", "polyline": {"encodedPolyline": "eed~A<PERSON><PERSON><PERSON>DYhBENGJABMXa@^wA`Ai@bAqAtCIL_@f@kAbCOVY|@SjAQfBKtA]~CWlCGv@UvCE`@WxBMfAIPq@A}A_@c@Ky@AA?_@B}ANs@DGJA@@JLDnBR^LVT^t@j@fAZt@DT@JAlB@VCbCEhCAn@Kf@Qn@[jAEd@KVILiApAw@r@o@`AITKh@OrBOz@]dAQh@Iv@?dE?JCr@A~BGbDEdA?NAp@B|@Bh@TbC@^@`AGlEI~EC|@Mt@K\\]nAWnAOlAGdA@bAJr@XjAJXh@x@fDrDp@x@VHzCbB`@b@RV`@p@d@f@|B`Bp@l@zArAV\\d@t@LRVt@\\jA`@`BPp@j@lBfA`D`@jBJr@TjATnAd@pC^lBh@pBL`@Lb@`@lBNzB@TFd@BTPz@@d@AZGvA?d@Z`BH\\VrC?LIjC"}, "startLocation": {"latLng": {"latitude": 15.591385199999998, "longitude": 73.80812209999999}}, "endLocation": {"latLng": {"latitude": 15.5900241, "longitude": 73.76471269999999}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at the church onto Anjuna Mapusa Rd/Mapusa - Anjuna - Chapora Rd\nPass by the church (on the right)"}, "localizedValues": {"distance": {"text": "5.5 km"}, "staticDuration": {"text": "12 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 1773, "staticDuration": "261s", "polyline": {"encodedPolyline": "s|c~AmdfaMwAPWJG<PERSON>_@vCCFSHaCr@KLKRMZqBnEA@i@|Be@jCWxE}@fGWfCEj@Gj@ENKLoDvCCTDlBA`F[vBK`@Ob@OZOl@c@tBYv@SzBO`@CNaDvD"}, "startLocation": {"latLng": {"latitude": 15.5900241, "longitude": 73.76471269999999}}, "endLocation": {"latLng": {"latitude": 15.5960714, "longitude": 73.7504799}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at St Michael's Church Cemetry onto Mapusa - Anjuna - Chapora Rd\nPass by the church (on the left in 1.2 km)"}, "localizedValues": {"distance": {"text": "1.8 km"}, "staticDuration": {"text": "4 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 1108, "staticDuration": "194s", "polyline": {"encodedPolyline": "mbe~<PERSON><PERSON><PERSON><PERSON>@FIV{@jAO`@ALL|B@Lb@pCJ`APtEDVDb@P~CTnC?P?p@BX`AzGFZH\\N^Vv@ZbBN`B@LKnBK~@"}, "startLocation": {"latLng": {"latitude": 15.5960714, "longitude": 73.7504799}}, "endLocation": {"latLng": {"latitude": 15.5950824, "longitude": 73.7404853}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left after the gas station (on the right)\nPass by I Love Bell Bottoms (on the left in 1 km)"}, "localizedValues": {"distance": {"text": "1.1 km"}, "staticDuration": {"text": "3 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 193, "staticDuration": "36s", "polyline": {"encodedPolyline": "g|d~AamaaMw@AmADaBLqBN"}, "startLocation": {"latLng": {"latitude": 15.5950824, "longitude": 73.7404853}}, "endLocation": {"latLng": {"latitude": 15.596807900000002, "longitude": 73.7403234}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at Goa Coffee\nDestination will be on the left"}, "localizedValues": {"distance": {"text": "0.2 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "134 km"}, "duration": {"text": "3 hours 51 mins"}, "staticDuration": {"text": "3 hours 51 mins"}}}], "distanceMeters": 712215, "duration": "49074s", "staticDuration": "49074s", "polyline": {"encodedPolyline": "sx|mAe`oyMKPb@Zn@d@l@`@LHp@h@d@Z|@j@h@`@h@\\fAx@B@LHf@\\PLn@b@VPRN`@Xl@`@PLDBp@d@XRh@^RNf@\\^V\\V~@l@PNJHPJjAn@FB\\NXLd@Tz@`@n@XRJFBLFn@\\ZNx@\\TJPDt@RNBn@DX@V@V?bDDT?rCBL?p@@pA?j@?h@Ar@AfAAtAEd@G|AQz@SDAnB}@zBeAdCmAbFaCnAm@zE}B|Aq@|BiAHE^[PQf@o@fAuA\\a@dB{Bj@u@`@i@t@_At@aAfDoEl@y@n@{@j@y@zB}C|@iAf@c@n@SxDu@lB]nAUr@OPCnCi@|AYl@MjB_@|A[fAQjDg@`BSd@ED?x@G`Ge@`@ETA`@CNANC|Ec@z@IbAKbCUn@IfD_@fAOzEq@rEo@d@KHAbDe@|@IrFq@LAlBSTAv@I\\GHAREpBa@j@MHCVIx@WvAe@r@UhAc@`@MDANEHEv@[\\Mr@U|Ai@TIz@UHCtA_@x@SbBc@H]TeATaAT{@DQJa@F[Jg@BIT_ADOPu@FUR_Ap@cDRcATiAd@gBLa@Rk@^y@JULYVk@nAqCdA_CVi@xAiDVk@v@gBb@aALYjAeCtBcFh@oAPa@N]Te@\\o@DE\\WZMf@KRGFALClBW`BYn@KFApAOdAOb@ILAxBa@zCe@bAO\\Ev@M|AW^EdAGf@Cb@ArAA^AV?dAA`@?t@?dA?X?^?F?~@I@?j@MPGPGPKbAu@t@m@~@w@DElAeAzBiBhA}@XY\\WTSvBeBXWNM^[`Ay@VU|AmAb@_@^YNMzCmC\\WLQVs@Hg@@KVyBVaCBSXiCTqBDa@Fg@@KJqAH_ALgCBc@?ILcCJuAF_A@WFwA?k@?q@Ae@IyA?c@AmA?_@A_@CoAEaB?QAYCcAAa@GkA?YCIBCDAB?f@@bI~@l@HVD`@Fz@Hl@Hp@Jv@JVBF?vEf@j@FnDVtBFt@?nBB`@?`EDj@?`BBhDD~@@`BB^?nGHvCAJA`@E|Eo@`Ca@fG}@|Ba@bLqB`Cc@|A[xAY\\GfB_@|KoBtBYh@INA^E~AKJAjE?v@@fBKnFa@hGe@h@Gx@G~D]j@EtBUvBKzBKlEUfAIj@GrCErA?`BNTB~@JlAPvARdAN~ATj@HvBTj@HfBF`@@zFPzAD|GRB?|@B~DJ`EN~AFJ@vFTjBJfDVnAJxGn@fANtHfAvIlA|Df@dAJ|BR\\BnCTZBb@BhL|@XBnBZRHfAPPDd@NRHl@\\\\V~@hA@@t@jAlAhAj@^zAf@jB~@v@VfBJl@Jz@NvAJfAVzBt@dA`@B?VHRHTP`@Zn@n@JJ`C`C~EnEJHbBzALJhAv@FBdG~BhBr@jE`BNF~ElBfBp@zCjAfBp@ND~An@tChAnFpBp@T`Ab@VJj@XTHt@\\p@ZfAd@vB`A|@b@|@^XDl@IPIJOd@yDD_@j@iFFm@L_AJw@TcBR{@FUJWNGJ]p@mB@AXcAj@eBDQx@qBd@cANWNUt@aBxC_FPWt@yAx@{AHObCqEpD{HzAqDtAoCf@{@Ra@HQxAaClBiCLOb@g@RUlAkAhA{@XUx@i@h@Yr@]`Bo@fBaApGqDhAu@\\[^Uv@m@f@_@dAeATW|ByC?[b@m@`@i@TYX_@NSbHwIbB{BnH}IfEoElAkA^?hCkCNOzAeBnAgA|AeAtBmAfAo@jBgAtA_AZWv@q@|AqA`@]nDwCpCaCRQnBsAfEqDfAmAl@u@z@qA|BaEh@aAp@_Bz@iCnB{H~EmRV_A\\qAVw@j@uAhBgDh@iAn@gAp@y@nAoAt@q@VUxAkAxJcHnA{@zAgA\\UROp@g@hAw@n@e@\\Wb@_@TSb@a@RSpA_B~BcDfA{AVa@Zi@\\o@f@mAXo@ZeAf@uBX_BJg@fAyFJe@nAwGH]ZqAb@eBjCyI~@cEJe@VkAR{@R{@Ry@z@uCx@uCZ_A\\aA^{@P_@v@wAPYPYn@aAx@gAHKJO`FsGhBiCj@_AZm@v@kBZ{@Vu@ZkARaAH[Hk@PkARqBFcADeADaAHwARiBt@mDxAoHf@_DDa@R{Bv@}PF_AP}ATqAfAaGFYXsBNgAJeAHkA`@gEVuCD_@Dc@D_@PsBLyAFs@D_@L{AHo@^_DD_@Jg@XmAx@qCx@qBn@oArAwBzA{BbBgCdBeCvFqIT]f@y@|@uAf@w@b@s@j@aALWh@mAJUXs@Lc@x@{CJg@RiAxAkIFYLq@xBuLNw@|CiQNw@n@eDFYH]VkAd@_B\\}@Ti@bAoB|BcE`@q@`@q@r@aAb@e@l@k@bB_Bn@k@pA_Bl@y@l@sA`@}ADWFc@Hu@DeABy@E_ACUKcASeAu@uDSiBAsBJmBz@kFv@eDb@}Aj@eBt@sBnAuC~@eBrBeDdAyA`ByB~AeBXWZW|F_GrAuAbEcEjB{B|AqBjA}AvAeB~CeET[z@qAZq@rBiDh@o@f@k@|B{BbAmAv@kAf@cAtAsCZi@v@kAhCmDhB{Bn@}@zBaC~CaCdF}C~@m@pA{@bBqAn@o@z@{@vBqCbB}BzB}Cj@u@dF_HVc@t@mA^u@Z}@b@_Bt@eDhAoEJa@|AiGS[AYJm@H_@TO`@OLAj@}BbBgFt@sC~AgGdA{DZFoAbFkBbHOr@GVy@vDi@lBWdAkAtEUhAeCvJoAdDo@fBqAhCq@dAkCjDqBnCk@v@sCzD{@hAk@v@{@dAiBrBYVk@f@{AfAyBxAw@f@uEpCo@h@cBvAuA~AyD|EW`@iB`CyAfC{@dBs@xA]f@W\\w@z@o@p@k@f@_BfBm@|@eDtFaEnFmDnEu@`AY^i@v@mApAg@h@aB|AqCpC}A`BqBjBcDfDoA~Am@x@iBlC?@kAnBg@|@Yl@oAlCk@|ASj@Y`Ai@nBeA~ESlAMt@SlBAxA@\\Bl@ZnBt@nDN`AFh@FlA?p@?j@MrAGb@Mr@WdAGNa@dAo@fAy@bAmApAu@p@u@r@_Av@e@f@s@fAa@t@mC|E_AbBs@dBc@xAa@tBGTWvAYlAMn@a@xBk@jDk@~Ce@hCOx@iBbK[|AGZk@fDm@dDWvA}@nDSn@Wn@MZs@xAs@lAMTw@nAkBvCU\\{ItM_@h@_AnAkBrCeAnBS\\Yp@a@z@Wt@Qf@q@fCMp@WvAKnAQzACVIv@]bDCZO~AATEZGv@iAtLKfAMhA[lBE\\}@tE_@rBKv@En@Eb@OnCi@fMGz@KbAC`@Mr@]nBuAlHw@lDW~BSrEWxCKbAGXQ|@a@jBiAjDu@`BS\\gAlBaEnF}BzCKNkA~Am@z@OVOTOV]n@Qb@Q`@Q`@Qd@IVUx@oA`EW|@Ux@u@zC]vAOz@y@hDIZgCtI[fAMn@Kd@I\\WjAy@lEKd@gA|FIb@CJa@tBWbAw@`CEJoAhC{@rA_@d@qAlBeAtA{AnBm@j@u@j@kBxAeBjASL[RcD`Ca@Vi@^sHnFyAhAWTs@p@g@f@kAnAe@r@m@nAcA`By@bBQ\\g@pAe@`BU~@]rASx@w@vCqAhF_BnGiAbEoAdDy@zA}@`BiB|CORs@v@qBtBwGfFu@p@eDrCiBzAc@^sC~Bw@n@oElCgCvAm@^aAp@eBfB_A~@OLYX}@|@Gf@UTw@v@g@d@a@^iBnBg@l@a@f@eCrC_FdG}AnBe@l@k@v@sE|Fi@N}@fAkCjDuAnAeChBiBnA_@RuGrDmBbAi@Pw@`@aAh@}@l@yAlAs@t@UVgBxBw@dAu@lAw@tAi@bAaDhH{@fBkCtFm@tA[|@k@vA_A`BUd@c@v@]n@cB`D]j@o@vAIPs@~Ak@vASd@kArB_@x@m@vAM`@EJEPIX]dBKr@_@xCCV[tCgA~IKp@QhAmAtGWxAQ`ACv@YrAs@bCcApCgD|JkA|CgCjHgAbDg@bBm@~Co@|Fm@tGsA~LIh@S|@kCbIoBrFaA~Cc@rBWlBIhBOfMObEAp@SjGOxBIv@QzASrBa@dEMhAu@|FG`@YpAy@nC}@pCAHi@~AsB`GqAzDo@nAe@~@sGnLIPgAvBo@lBm@fC{AvI{BvMQz@eAbGy@vDqA~H_@~ASp@q@dBq@hAk@v@i@l@QPgAv@kJnFu@b@kAz@uBlBcBrBaAbBcBtDg@`Ai@lAWn@eB|Dw@`BSd@q@fBUt@SxAEnA?pA?r@@TAPB~C?v@EdDMjCo@jIEd@Eb@Gj@Il@i@`EQz@G\\Md@k@`B{@bB{@rAi@n@e@d@u@n@[TQLSPwFfEaAx@cBhA_UrPwPdMsCtBqDnCoBxAgAv@wAdAkA|@eAv@k@`@WRKHi@^e@\\k@b@g@\\k@`@e@Zo@h@IFUNyAlA{@t@iDbDKJoBlBWRQPiB`B[^iGxFoAjAYVgA|@]TaBhA{A~@a@XkCrAgDzAsCbAaBd@eBb@eBX}Bh@oAV[Fw@NcGnA{Bd@kB\\kD|@OD}Bn@}DfAeAZu@TmCv@oBj@iAd@mAn@aBbAg@^k@ZgCnBIFKFsCvBoGlEwGzEq@f@oCpBmCjB}@p@UNGFgBnAc@Z_Aj@gC`B]VUZe@f@}AdAkBnAeFnD_D~BcAt@wJjHeE~C_BjAQNa@ZiC`BuBdBNTMFULwDhCKTUZaAhA_@t@MdACv@@b@BxCIdC_@xDe@tDYjBQnAk@tDI^m@vB[`A]z@gAhDe@tA}HzTaAnCaDbI{@vBm@xAi@jAu@lBk@xAaD|Hk@|Aq@lB_BlFoAdFsAzHa@`DMjAYxCMlBQhEGz@AtD@jAHvDLxBDr@V~Bn@rE\\jBf@zBTx@~AbFd@nAp@~AjBzDfB~CzIjPvClFf@|@lBvCnBnC~ApBvEjG|@nAtBxClAjB~AvCrBxEhC~HNn@j@rCl@|DFb@RlBHfAVbDNnBJ~@\\zBX~A^~ALn@XdBPrAHjALrCB`AD|B@bADfDHzG@v@@j@@v@PjL@x@@f@@j@DvCH~AT`B\\nAl@`B\\r@\\n@\\f@fCpC^`@lAtAvA|Bj@pANb@Nd@FP^dBHh@DZDb@DxA?t@IbDGdCCv@IpBQvHGvBA|@KzDIjDQbHAnDH`CFx@PrBZjBnAnFfBtH|@rEf@`DF\\p@lFDr@XlD^rFNfFDvD?tC?n@ArBKlE[|FYrDKlAy@tG{@pFaA`FADGRCL{@nDiArEs@zC{@xEi@vD[dDc@dFc@~Fi@|Fa@tD}@rF{@zEmA|Go@nDi@rCo@hDs@bEiAbGuAtGKl@oCtMm@rCoCnMkAtFq@dDKd@Op@iBdHe@~Bw@fDm@zB[jAaBjFMb@cChJk@hBi@`Bk@jBmDrKaA~CeAdDiAzD}@|CcCvIiFnQ]dAkA~Ci@jAMVoAdCgApBmApBaBpC{CdFgLpRiJpOcDpF}@xAKNaAtAW\\UVmBbBeAp@yBhAi@Ro@TODmCx@eB\\yEzAeA^mGxBQJa@PiAp@mAbAs@n@{@dACHqA~AcAjB_DfEc@l@yBvCaBzBcI~JwAfBaAjAwCvCiChC]\\m@l@sCpCKJKHQT{@v@cDlDcC|CgEjFIJuDvEc@j@mErFmIfKSVu@`AKJmB`C{D`FkC`D_AbAkAfAyApAo@d@c@ViEfCmD|AuA`@wBn@gBf@eAXw@TeAVaBd@oCp@eG~AsCv@iBh@]HyCx@_IlBwB^eBRW@oCF{A?cDOwAM_@EkCa@q@KmJmAc@GyEu@c@GkEq@[EaKwAaDWeEIoJK_EAqFAyIFyCDy@@eHEuMSkEEY?kPBi@?yC?cLQaACuCI_MYyBEcCOiAIo@GoOaBkBQ]EiBM]CeJi@e@C]AWCySkAuBK_HIa@?oDBwDAkB@oEC_QD{E@gC@_R@uMBqB@yO@mFH}ADmLb@uBHwUz@uA?yACwD[m@IqAScE_AiD{@q@OkEu@{Ek@wBWoAO_CY}Dg@}Gy@_@G}AQ}Ca@}Fq@_Da@aDYeES}AEYAmBIsFK}EQkACwCI}CG[A}BGoJ[gFa@cEg@yBYa@GaNeB{Ee@_DUcBMcGW_EAuA?aCCcFHeKh@YB{Hp@mBLcGHiOFgFDaFCoAA{FQcCMaAGkHa@qKk@cBA[AcCCcA@[@{@BePnA_@@}Ep@yDb@sABu@CeCIoBIaBIsHo@uIq@mDSmD]iJgA}AO_@EmAKoCSsDUoC?e@Fm@Vc@ZYb@]bAK`@Id@ETMv@Qd@MPWP}@~ECNM`AMVUnA_@bCGV]xAGRKXOPq@vDc@~BY|AuB`MMv@Q`AwAjIg@fB[z@qAjCeC|EgA|BkM~V_@r@mCzFu@|AUb@yApC{ItQsHrPsA`De@dAUh@a@|@{@hBGLCD}BxEeBxD_NbZe@bAwAvCmDxHqD|H}AtDQf@u@vBIRk@|A_A~BM\\]bAEPERsApF}ApG_A|DQv@y@fDmAfFwAtFeAlE]|AQl@g@|AOf@s@jBsAlDMZwDpKy@~Bk@|Ae@lAiCzG_@dAcA~CoDzJmA~Ce@vAiL~]u@xBy@lCyCzIiBvFgBvFwAfEsD|KkAbEUz@wAvFy@lDSv@Y`Ak@zBsBxHWbAWdAo@dCM`@]jAc@jAsAvCe@fAkAjBg@v@IL{@jAuAxAsGrFcAz@EDkGfFsAhAaEfDgB~A_ChBgAx@uAv@gCjAqA^sN~E}DlA_J~CoA`@aA\\s@\\SL_Bz@i@`@wAt@qA~@iBzAgBvAm@n@sB|BWVk@l@_C~B_A|@gBtAaAv@UPi@f@q@j@iBbBaA`A{GtGmCbCwKdKEDcQhPgD~C{@z@kAfAiItHaGrFqApAo@l@qDbD{CnCUVoClCqArAOL}@~@o@j@gFvDoCpBu@l@iDhCqAfAiCvB_A~@SV}@hAw@hAqAlBo@bA}CtEq@~@e@\\w@fAoB`DcB~ByBbDQXu@bAyEdHmCtDmAzBeBnCg@p@k@z@QRs@|@UZsBnC_@f@qAhBeAtAYZAB_B~AgBdBo@l@QPo@l@i@j@uBdCmAfB_J`Ls@|@qA`Ce@xAcBdEeA|B_@v@kC~E}AdCeErGOVaCtDmD|F}CpEyFfJe@v@iDpFo@`AgBrC_ArAoDrFOVsHjLiDhFaC~DcF|H}FdJiDnFS\\}@xAk@`AMP{AbCkBvCy@rA_@l@_AfB_AnBm@nAmB|D_CzEm@pA{AvCk@jA}DdJ_BvDc@jAaAjDg@|AGN[`AQf@Wt@c@rAq@`BgCtE}@~AyA|BUp@u@tAi@`AaBzCgCxEwB~DS^[l@oBlDo@hA}BxDqA`B]`@oApAeBfBwCtC{AzA{@~@iGhGoDjDmAlAkMrM{@|@eEhEaAz@yAtA}FdGeKpKs@t@_EbEoAnAyA|AKLg@h@_BbBkFfFcF`FsBrByAxAkAjAaC`CkBhB{AzA_B|Aq@p@q@p@k@j@gBdBmFlFaE~Ds@r@mAjAURQRi@f@sFpFcBfB_@^iBdBi@d@cD`DeBdBqApA{AxAiAjAcAbA[Ze@b@uBrB_BdBiBtBqCvCyAbBsAzAMLGFkBhBy@~@aAzAgCfEKReAfB[b@qBxBu@r@iAhAsFfFcA`AqBrBkJjJe@d@ED_CxBc@ZgBbA{@Z}@Vk@Ne@JsYbG_ARiB`@mAb@cCf@oIxASDa@H[HKBg@Nw@XMHq@b@_@ZkArAkLvRiB|CgCrDgGjHkE~EuBjCqIzJe@r@w@tAu@`Ba@|@S`@Sb@_DlHgAjCqApCu@nAy@`A}@|@qDvCm@n@[ZiB|B_@j@eDlFuFzIyFzHiEfFuExF]`@y@dA_DxDcCxCqAzAiBhBoClCsGdGeFfE{ApA{DjD[VqD~Ca@\\eAbAsAnAaBpAe@f@SVs@z@aE|EcAtAo@vAw@|Bs@dCW~@Qd@GPGPm@`Ak@p@cAbA{A`A_G|CcDrB_ChBUXyBlBg@d@s@t@wB~Bg@f@_B~A{CbDkE|EWX{BdC}FjGoDvDu@x@sJlJqBrBuDjESTIJ_@`@_@b@qCfCqAhA]X{@l@y@h@uHvE_Ah@uAz@{D`C_Ah@yBvAk@b@k@d@cAlAsAvBc@~@{AtC{DxHS\\qCrFeBdDe@|@oAdCi@r@aAnBi@jAe@bA{@`CCHSn@[z@Od@[fAcAdCk@jAc@z@W`@iCjEcDdFi@z@[h@kAnBwAlCaCbES\\U\\o@fAi@|@eElGuAtBqFfJQZkBfDmCxE}@~Ae@~@sCbFc@z@qElIYf@U`@mDrFoBdCw@t@sAbAYRe@ZyCbBOJi@ZsEnCqAv@]RWTg@r@Od@Ib@GnA?\\?fBCdBIlAWvAUx@_@|@Uh@o@`A]b@q@n@y@n@uKpHu@l@e@\\CBsBxAcCdB}B~Ac@\\cAv@MHoEzD}GlGuApA{EhEwG|FkEpDeBtAaAl@yB|@y@VcATqAXeIpByBj@mCp@gCl@uBh@cHdBk@NkAXWFYFc@L{A^cHbByCt@[FKHqBn@oCp@cCd@{@TiGzAgB`@yA^e@La@HyBf@sA\\gE`Ai@N{@RuIzBqA\\oEfAsD~@eAVgCn@o@PyFrAk@PqLvCuC|@c@J]HuA`@iB^eCn@iDt@]JgKfCmAZGBa@JkBd@uCv@wD~@yC|@uCbAgCfAwDrB_BbAwA`AsBbBkBbBcClCiApAw@nAcBdCa@t@_@l@EFYr@yCzGOZYl@Wp@eCdG[p@mAtCy@dCo@hAsEvKcFlL_CvFCHu@`BmBpEe@dA_AjBm@fAwAjBg@h@iA`Aw@l@y@f@}Az@{@\\eA\\}@h@OFyBn@aBf@mDjAgGlBaFvA{Af@{@XwAb@c@Lw@BiAX_]`KgK~CeF|AiEpAeCr@qEtA{Bt@eGrBgC~@oBj@kAb@kN|EiA`@sKxDoCdAq@T_Bj@{@ZqDlAMFyCdA}Aj@QDw@XwAp@o@\\OHy@f@_DdCqB`BkGfF{JdIOJ]ZsKzImGdFs@j@URsIbHuBdBqJzHcAx@iGdF[VC@_FbEyGnFsC`C{@t@aGpEOLeDhCcDfC{L|JcFdE}HrG_F`E{FvE_Ap@aCpAaDzAYLu@^uBnA_CjA{XhM}C|AIDiRxIwLrFuG~CeAd@mAj@gAl@c@Z[T{AdAuEzDqOrNwJ~I{@x@_CxBeEvDmAfAcAn@_EnDuEfEyBjBmErCu@d@yIvF}E|Ca@Xo@f@{@v@yDtEgBdBaCdB{BfAiK~DuJlDeBp@kCpA_C|AiA`AiFlFmMzMcDhDiAtAmCtDw@nAqAvBa@v@mAlC{AvDWv@iEzLaAnCgA~Cu@nBy@hBu@rAcA|Au@dAgAjAaKbJwAhAiBnAsAr@sAl@iBp@mA`@sF|AmXzHsA^kLzCiBj@cG`BgAZ}C~@eGhBmF~As@TaAXkBj@a@LgAZwW`He@L_M~CkJdCwKxCSFyCz@wEtA_AViAXSD_@LoB\\yANw@BqBG_DU}@Mk@G]E_AK}AKcD@uAP_B\\oC~@wKbEkOtFeC~@uAn@aDfA_EfBiDbBaCnAkEfCyFvCgBr@mBl@}DjA_Ez@iBXuBVqDZ_CVaDj@aDl@{Bp@UFcDhA_IzCoDzAyFlCeE|Aa@TgPpH_@P_JbEcAd@q@ZoCpAgCjAUJaH~CyAp@cD|AaGnCc@RyAn@}LvF]Nc@RoClA}Ax@gAh@wIzDYNYL{BbA}EzBs@\\ULgOvH}Ax@{CvAcErByDjBeD~A{BhA{BhA{Az@mFdDuErCuErCeI`FyEpCqSlM]TiDrBeRpLaF|CeNpIiFbDkEhC{ZfR{BxAGL_HxDeBfAwBtAe@ZWLwBpAoHlEqG~DwM`IeMhIcMpIgNdJiI`GwBxAuDvB_D~ASJ[NaEbBUJkCdAyEjBo@VeIbDOFu@XeBp@mAd@{DzAuB|@aA\\}B~@cMrEgGzBwK`EwEdBsFpB{E~Aa@LyGvBODOD}FlByJbDc@L{Br@_@LyAf@uBv@iDzAyHxDi@XkL~F_CjAqAp@y@b@yCzAwC|Ac@TgDjBu@b@mPhKaBbA_@ToG~DqJbGqDpBuAj@QHyCbAkDz@[HwB\\iKnAaBRmD`@cAJi@FcMvAkALyAP[BSBcCViIt@sBPkEZy@DcEVoI\\oBF_ABeI\\aKZ_ENY@uERwKp@sHd@gKl@{@DO@uF\\oCLoCDgCBmGDkFBuFDkEBsMJi@B_BJcBPq@HyCd@uATmAXqAb@e@Ry@`@uA`AmAlACBGFu@x@cBjBMPu@z@gHhIKNkArAcAjAwA~AyApBoAvAuOnQcBrB{DjFiAjAONmAjAqCtCcFzFgFbGiFdGsGtHSTkC~CwA~AWX}@x@uBzAaBt@iFbBmBl@m@PwT|Gk@P[JwLrDqAh@cU`H[JcP`FmBx@eHlDaAh@kBdA[RcAv@_BdBw@nAU`@qEvIS^cApB{DrHk@`Ac@v@gKbS{BjEe@|@uHrNu@vA}AbD_@dAcAjDq@pCeCpKW|@u@fDa@hBw@bDg@rBkEpQyIx^}CnMiAxEK`@St@_@jAm@|Ag@bAk@`AsB~CmEnFiDdEgBtB_C~CWZY`@OXk@|@kAlBkAdCyA|D{IjUgCvGeCpGCFEHIVaD|HaHtPgBpEMVwEbLaHvQyArDaEhKIRoAvCw@bBaK~Sg@fAqDdIyBbFQf@}ClIm@`BITeAhCu@fBiAjCkBpDgBfD}EdIQXoDbFq@z@{@z@m@b@eAv@eAp@_B|@{@b@s@`@e@Z{@j@_CnBiCfC_AbAwAxAwA~A{@x@c@\\_Ax@iC|BsDvC{@n@qDrCsEnDc@^uBdB{@n@s@h@eA`A_ExEcCrC_HdIyClDiFhG{J|KaAfA{DvE_IhJuDvEqAzAq@z@iBpBwCpDcAnAmClDUZsB|BmL~NyC|Dw@fAaJhM{@vAeDvGwAvCkEdJ_ApBeA|BwA~CcAfBuHtOsAtCqAhCWh@_A`BiAlBaQxYiB~Ck@jAiAnC]bAq@hCi@~B}BpMm@rDe@`COr@gBtIWlAyB|KGZsDvQw@bEg@hCk@bCMh@]hB_@tAETkAtD{BrG}AbFsBhGwEtM}AfEuA~C{@vAcApAs@v@c@b@qAjAyEbD{LnIuB|AsBpB}B|CuJfN_DdEoFpGgEfFqBlBwApA_Ax@aBrAkCzBoAfAmB~AqAhA}CjCkA~@_GjF}HvHyAzAkGdGq@p@wGbHiYzZsLnMkPxQgDjDy@`AGx@cAtAuAvB[j@IPWp@OX]v@sArBc@h@ONg@f@cCxDuBdDqAjBa@b@cBpBi@?k@l@eNlLqB|A}AvAaFfEuCdCyEpEaF`F}EzEqGjGiAfAeAhAyNnNqApAqGhGmElEwBrBwAvAeCzBuCzBoClB_SvNwAjAc@d@qAxAwDbFaBzBoDbFeDrEY^eCnDuB`C_A~@uCfCeDjCaBvAeBzAyAvAKL_ChCgBpBIHoEhEa@`@i@f@i@f@iBfBiBhBIHsAbBs@bAOTEFYf@i@hA{@xBwChKi@rBsClKGViBxHIXETuAxG{AhH_@lBaFzUi@|BgAtFy@bCaCdFKTc@~@_@x@gA`CiAvBg@r@a@f@{@z@}EhEwDdDw@h@YXWVq@l@wDxCqDdDkCxByDbDmBnB]Zk@h@o@p@wEtEuAnAg@^}At@{@Vq@Le@Hs@F{ABqAG}@MeBe@[K_A_@qD}AIEgEgBOGmDwAwGwB}@Qk@E{A?c@BeARmA^qHlCuCjA_CbAu@Xa@NaCbAmCbAcDpAwCfAyN~FsDbB]Pm@^]Xe@^k@j@uAlBe@~@k@xAe@fBW`BKtB?jBFbAl@pK@`BC`@QfAMd@IRIR_@j@SV[^[XoAv@sLnFe@RQH{IvDiF`CYJuIxDcBp@{BdA}Ax@EBiDlBoDtBmAr@sBlAGB_HdEcSjLGBiErCqJxFs\\dSsO`J_KbGgI|EsGzDkAn@_LxGoBbAkAd@qD~@yB\\cTpBaGj@oCNcBP_Iv@}AVQDODoIrBgA`@yEz@mEh@yR|BkFr@aBZcD`AoNhGqBz@_n@jX_Y|LeC`AqBh@{AXuBRuRxA_Il@uDl@cAViBl@g@RgB~@qKnGYPiCzAyCvBy@z@sHvHwUpWmDxCyAhAuD|C_K~HeLhJeBlA}@x@s@j@oBfB}ApAsElCaDtA}LvFug@bUoB`AuFdCyDhBqBz@}At@cIrDgDvAyHlDe@TyAp@}CvAuDjBqEpByCvAuBhAoEvCcAf@gBjA}AlAcBbAuCrBiIlFmNnJwEfDcGzDgAt@wA`AmCdB}LdIo@^yCxB}@t@yBtBmCvBiMnMmEvEuBlBQPk@j@yDpDwAlAeBpAQLmDpB}CtAq@ViC`Am@PkH|B}FxBoKnDgE~AkBl@cFlBaBp@gJbDcExAa@LeA`@{Ap@s@`@oAx@m@b@cCpBgMrMmEpEuBnBu@t@gLvL{@bAUXoArAoAlA}AnBs@bAiAjBiBlD]r@oAdCgBpD{ClGuAvBsBlCeLhMkDbEoI|JaBpBaFjGeIvJ{GfIyCvDkEdF}AxAuA`AaF|CaAf@gEfCo@`@iJ~FmAl@cCpAoC|AqHrDSLo@ZaG|CiAh@aBt@eBr@{DnAgBh@{Af@gF~AkA`@iIfCqCfAwAt@eCdBmCrB}L|JiJnHmC~BkEhDiCrB_ClB[V}ApAoDpCSPq@j@gFlEsAjAKHQN{@r@a@Z{BdBmChBMHo@\\{@f@iCdAs@Xe@NmBf@k@N{@NwAVaAJeBRsFp@}AN{Gp@eAJ{SrBSBcLjAmCXM@M@uBXgALmE|@uBd@wD`AoG`Bg@Lm@P_@HeBd@eBd@oBh@yCz@}@RgAVSD}@PgEp@aE^aCLoDJe@@yDFuAAgA?O?]Aa@AeLa@wE@m@?a@@mCByEVgBNcJz@m@HqJbA_J`AkFf@I@qD`@_BZaB^oBl@cC`AgAd@}A~@qEvCgAr@oCdBeEnC}@j@oCdBqFnD}CjBuA|@kMfImBlAs@d@{GlEi@\\uA|@s@d@cFfDsA~@o@f@YZa@d@OTk@z@qAvBoDtHi@z@uAtBaAxAwBfDeAdBw@jAW\\_BzB{ArB}FlIwDpFqBrCUX_NzR}EfHcBbCo@|@wBjDINm@hAKR[l@c@|@Wf@mC~FmFnKyGxMeBnDeAhBU^eA|AaAnAcBhB{BjB_Ar@mClByC|Bo@f@{GdFoHvFcAz@kDfCmAdAuB~A]VkBvAmG`FkHjFiF`EKH{ElDiB|AsBfCORsAjBg@|@oApC_AzCYvAIb@UxAK|@IfAG~@Cx@AlBAZBzD@d@?n@?|@?b@Ax@?`GAjHAj@B|I@rQ?fD?p@@J?xBAfC@pG?l@@nR?@?~C?bB?jCBpQ?xHFvB?VPxHFlATpEV`Ed@lGf@hFHj@^~Cd@nDZ|BnArH`@|BH`@XvATnA|EpXJd@Nz@bA`G|AtIdA~Ft@bE\\pCNjBDpCAnC?l@An@ShUElHGtD?J?JC`AIzBKhBANW`Dk@hEa@dCuAdGi@nBgDbKa@hAkBnF[~@M\\aBbFw@xB}AlE]`AmMn\\ITQb@oB`FkBzEQb@sFrNiM|[yEvL}@`C}CzHqJzV}A`EsBzF}BdGSd@Yh@{DbK]|@k@xAKVsAlDkA~CmApDqA`DQb@oCpHwBxFQh@qDrJ_@`BeG~OmI`TYv@_ItSq@lBoAhDOb@kEdLqGbQy@tB{JnU{HbRsHlQcDzHQ^yDfJqGhOyDhJgBhEyBjFeCrF_HrNsEpJgA|BcF|JsGnMuCrFe@z@gEjHcFlI_BtCmIdNeDpFwFlJeDpFe@x@mGjK{G~KgX~c@cFlIqArBwOxVmDdFyHtKyF`IwSvYaC|CkBtBkMfOo@t@}FpFeAt@{DjCuLrHg@XqD`CyFdDi@XOHmEpBi@RmAd@uCpAcAd@}An@oJxD]NcIhDyClAu@\\iAd@gDrAo@VaBt@sF`C{Af@]HoCn@qCr@c@J_@HyA`@qCdAMFiCvAuB|AcA`A}@bAiAzAILILwAlCe@dA{DvIa@`AmAnCg@dA{@dBS^k@`AMNa@n@Yd@kDxF_@j@c@r@_GtJcAnBIPk@`B_@tAUpAQxACTKpAG~A@pB@h@HnAjAbJNhAh@zDPzAfBnN\\nCJl@vA|Gz@rDhE`RLj@Np@|B~JH\\nApFd@tCNzBDdBA`CG`BIdB_AlREbA?NC^a@lQMzFEhBIhFCd@MtCM|BUpCSnBQdBiAdLsBxRgA~KC`@Eb@OfBK`AWdCw@~H}A`PCVGj@{@rICZM~AMbAqApMUhCGn@Ip@QhBe@jEU`Ce@zE]vCSrBGf@SlBCVy@rHe@jDe@pDuAfK_AlHk@nDwArHaAbFq@hEaD~P_@zB_AfFcAnFe@dCo@lDwB|LcAfGObBGdAa@rJIbBOdAUrASdAcFvPoFrQMb@aF`P}@tCu@jCqAlEkEzMkAlDcCjIYtAO|@QdBC\\IbBHnRF|EBxF?~@BfCCpDOxBQ~Ac@nBiAnD_D`JyFbPw@zBg@`BoCrH}ArEy@xBcAzD]`CKrAc@zJSzFMrCa@rD_@|B{@pD_BdGQn@mCxKiArEq@jCu@dEG`@Ij@}A~JuCnRSnAWzAQ~@iCvOCNk@~DgAhHm@tD]fB_@bC{AzJuBzLgA~Gm@tD{@bFg@|CU|Ao@|Hw@rK]vFQ`BYdBo@zCGZoBfIq@vCcFxTOp@u@bDoDrOoFlUkCnKkApEgArEWdAo@pBw@hBmAbCaEvHiD~GsDdGkDxESVaF|GwH|JcElF_AdAw@|@e@f@m@p@iAjAkKnK}KfLkEbEeGlGJNoGtG_ChCcDhEsBtC_EpFKPyArB{AxBg@x@]j@[j@gApBe@v@nA|@Ve@TpBUqBWd@oA}@GJaA|AUSeQzYS\\cDnFyJtPk@~@_C~DoAfB_F~Gg@t@_AxA{@dB[z@e@hBWtBG`BFjBH~@TzAVfAp@nBnBlFzBvHhBvGPr@r@lC\\rAHXr@pCjCxJ~@vFLp@RjAb@`C`@~ANp@Pt@dApEtD~NnFlShAfEb@fBz@bEx@~En@tEHnADp@?dCCpAWlCY`Be@rBqBnGSh@ABYx@Qj@kCzHc@pAo@nB_ApCeA|COf@Of@Yz@GNi@vAyApEM\\IV}B`HoH~T_BxEqDvK{ChJiFfPs@rBcBfF[x@Wt@qA|DyAjEY|@oDrK[~@oDzKkFzO{IbX[z@cCpHkIbWuF`QaBfFoApDHp@g@~AAFs@`B_@rAo@zA_@`AUp@Wr@W~@w@rB}AfEeB~EkEhM]`Ae@TWv@mAnDsBzFuAdEgAzCeE`M{G`SuBlGsAxD}DhLEP{H`UuLj]gDpJOd@_LjZaE|KaDnIgC`HENGPyCzIsBnGUr@eQth@}CxJ{Nnc@y@`CcHdTgFvOyHtUIVgAhDSl@u@~By@bD_@fBYpAi@pDwBtQo@lFGn@CRqAdLMbAK`AeAtIaA~GGd@iCrPkBxLOdAI`@[rB}@zCg@hAo@jAyArB_BzAi@d@cAn@aDbBmDhB_CjAeCnAgIfEy@`@eEtBeFjCw@d@yCzAc@TkEvBwBdAqBhAaBx@iFlCkAj@yAv@eH|CcAb@aF~AyD~@u@NoB`@}Er@UBa@DsBTkEXqDHeEFaEFO?W@qA@sBB_KN_UZqDFuEFuBDiAFmAFuBReD`@kB^iCn@K@a@LgAZ{Ah@kBn@wDdB{BnAy@d@sDrBoCxAsHhEMHqBdAGDoAp@eDnByAhAu@t@SR_BnB[d@[h@sAjC_B~CaAnBCFuElJ}CrGeBrDCF_B~CeBhDoV`g@k@jAy@hBkDhHqAdCkAdCuFbLMXcPb\\gBpDmBxDm@lAgFjKMV}GnNO\\{A|CyBpEgAdCQZaAfBiA|Bk@hAkB~DyAxCsC~FuBbEeB`DoAjCeAdBsC`GMVsAlC{@zAsE|JYh@GLGNoC|Fq@pBEPe@fBc@hCKpAEnBCb@ArATxFNhENpEJrCDjB?lAIdCG`A]dCg@|B_@fAm@dBy@`ByB|DyEdJuHrN_EfH}FfLaBbDiGpLc@x@iCbFiEdIuChFsElIy@rAeChFcAnB{LxUoIlOo@lAoFjKO\\eArCs@~BU|@Kh@K`@[zAeCbMk@nDuA`HqCpNwDtRyD~RKb@]hB_CnL}@tEeB~KkAdHE\\oA`IcDvSqAjIWxAUrAeEdX{BnNc@zBg@zBuA`F{AjEiBhEsBzDw@rAwBdDyFfI}FjIs@~@kQ~Vw@fAsNjSw@lAyAbCmBpDcDfHUd@u@dBqApCcBrDIRe@dAkBxD}@`BW`@aApAuA~AyBvBcAv@}BtA{ClB{EzC}A`A_@TiIlFmFlDoAt@mJdG_IdFoBtAoGbEgChBe@`@gAdAeBnBgBhC{A|CWp@a@bAiF`NiAlCq@hBiAtCgHbR_D`IKTs@lB[z@Qb@s@lBc@dAgBrEs@tAiAnBSZ[f@}@hAoApAcA|@_An@yGvEwB|ASLQLqM|IyFxDmBlAuDlCma@~X_S`N]TeGdEk@\\iDtBkGxDg@ZmEnCoFhDkAt@WPq@^i@X[PgAj@kCtAuCdBsDbCaMnHiJpFyBtAw@^qD|BiC|AsGlEYTkAz@e@ViB~AkBjBsF|EkM~KqBpBeAxA]j@CDg@`AOXk@zAc@nAy@lCO^[t@_A`Ba@h@_BhB]ZwCzBk@b@q@d@}B~AgCtB{BjCk@v@SZsB`DGLGL_@j@kBzCq@fAkB`D[j@c@n@q@hAm@nAyAdCoAlBuAtBmBdCcK|LiCbDoBdCqCbDmB|BaBzB{@bAuBdCyAhBsBzBmE`EgDzCmAlAs@l@_@^}@z@gA~@oEdEWVs@n@i@b@gBfBcA|@g@f@uDfDiBbBkAlAyArA_AfAuA~Am@t@qAfBgDrEmA`BqC~DmA~AyLxPaBbCUb@iAhBeCfFo@xASf@oB|Fe@`Bw@bD{@tDu@`D}@lESx@uBlJm@fB{@zAcAtA{@v@_GpE_BrAg@d@g@n@QX[n@o@dB[bBWjCQbD[vE_@fGSnC]bFUvDi@hJMtBEvACrBF~BSnJKlCIzFE`BQvCId@Q`AGVGXWv@_@p@e@n@]`@SNk@h@SPo@^sAv@wBpA{@j@kB`Bg@j@y@bA{@pAm@x@[PcClD{EnH_ElG_M|RwExGqCrEgGjJ}@hAeA~@{@d@i@VaBf@yATiFp@y@Rc@L_@LcAh@w@h@cBzAuFnFyMbMkAhAkPrO}BxB}CvCaXhWkAjAaBxAwBrBqChCyV~UYXk@l@m@t@aArAaAhBiGjLaB|CQ\\g@|@iAdBi@l@q@n@k@d@kAx@uDjB}Av@mAj@sA`Aw@n@s@t@y@hAo@hAk@rAqBjH]nAoBxGo@fCy@zCe@bBsArDcBbEyAnCkAjB_CxCa@d@U`@uAzAQPi@d@c@`@cPjM}BpB}AxAgAjAyBfCuCpDq@v@k@v@w@tA[r@]~@WbAQ`AObBClABvBBt@|@vMb@rGHpAHvABvCE|BQ`D_@`Dq@fEq@`EIh@Ih@oAxHy@`Fk@tDE\\]lBIj@G`@gBpLUzAc@jCwDnUoBjMg@hCi@fBWl@M^g@hAe@~@gA~AUXeDhDiDtDONoBvBeItIgLnLk@n@eAdAWVkEtEs@r@gBzAGFkChBuC|AqB|@kBr@{Bp@uGvAeIhBql@xLwHxAmGrAmE~@aE`A_Bf@mBl@gDjAsXlLUHgLvEoAd@oBr@mD`AaE~@cQxDmCj@kCj@{K`CoD|@iC~@WJe@Tw@^SJq@^eBjA_At@w@r@WVcAbAgFfGsA`B{GzHcF`GsEjFoAzAcDtDs@z@UXw@`AUZcKnLiAhAWXeEbFgEzEaAfAs@x@aHlIuAfBaAjBq@~Aw@tC]rBS|BEfBAbAL`CNjBLrAh@|FH|B@`@B|@?pAMnDSfCIj@a@bCo@jCi@fBaBtDUb@[j@{A~BmAzAoBrB[ZcJxGePxKyPjLyA`AeAt@wAbAuAtA}@~@W\\W\\wBdDcE~Gy@lAiApAURMLq@n@s@f@sBhAkAd@wC~@YHqAZw@RoHrBwGlBiGlBu@RgHxBeJhCi@Nw@VoDbAcI|Bu@Tm@PyBt@aA`@yBrAUNmAdAg@f@_HfH{FjG_SnSwDzDuQxQm@l@oFzFoFvFc@f@yE`FsN|NeQrQyLbMmDrDcFjFcMdMcDjDwLxLSPsK~KaFdFoC|C}AtAuCxC{CjDeBjBaEvEsCxCaB|A}@p@c@XcBzAkCrCSPoFjEaFrD_@VwClB}GzD[PuE|BqCjA_DlAoFfBgHpB{GbBqBj@mCr@_JtBwFrAcIvBa@LQF_A\\c@P{BbA{ClBkAz@qAlAwA|AcC|CgB~BuInLiDrE}@lAa@f@_@d@UXmA|AyEbHaCpD{BfDMRyDrFmDfFgD~EqCdEa@l@iB`CqB`C{CpDyAtAo@d@_At@eAp@eAl@sBdA}@b@kFdCeCbAaErAkJ|BE?oG`BqBh@_N~CiG|AgBn@iAj@a@RiGdDqDbB{N|HqIrEcG|CuC~A_@RuC|A}DpBwAv@mBhAKFiAv@m@d@o@h@aDzC}@z@KLWVyAzA{ArAe@d@w@v@Yr@K\\uLpLg@f@oFbFeJdJWXgBfBO[eCdCe@d@aC|BmBbBo@f@oChBcEfCeAp@sA|@oA~@}AtAg@f@kCtCgBpBOPSVi@l@UVKJMNi@n@_CjCIHMNi@j@}DvEcH~HaAjAcAjAa@h@qAfBQXW\\sDjFqC~Di@v@_@h@cArAgApA}@|@sDtCyBbBuD~CeIxG_HrFeCpBmGdFCDUR]\\yAjB{AfC{AjC_A`BkDlFw@dAgBzBgEjFwE|FeBxBIJuA|BKRKTEHQ^Yt@eBlEg@tAkA`Cm@~@qCfDaCrCe@f@mCxCeA|AiAnB_D|Gg@bA{@tAkD|Ei@p@iBdB{@t@[V{@p@{@j@yCbBo@XqAf@aGhBoA\\c@Ls@NyD`@gFh@yDd@w@Ls@JsC`@{Df@eCb@oC^sA\\aCv@_Ab@gCxAcAr@iCbC{@fAyBdDkA|Bo@tAeAlCc@`AqBxCy@`A_A~@sAdA{A`AiCjAkA`@yBh@qCh@cIbB}J|B_MdCcHxAgGpAuE~@cQtDoIdB_Cr@sEdB}FjC{@`@aBz@_B`AyEtDmB~AgAdAaCxBqCjCiE~DsC|B_@\\gAp@}Az@uClAyGlC{DbBe@Pe@PQH{LhFMDiFjCeCfAaErB{BhAgEfBcFhBqGjBoUrGiB^kALo@BqA@yIOsKSqMQmCGqBEy@Cq@A}EIs@CeDEcBEu@AuHMWA_JMw@CiBEmJOqJSo@AeAC_JQsJOaA@_G`@w@BwG^_DRwDReAFg@BoHb@gDNmTjBaCZmj@`KgZpF{Dd@uKtA}AR{QzB}JjAkBRwFx@sB\\SBuAPkFp@sKpA{IjAcNjBmBT_Gr@yIdAqC^}Dd@mNfB{a@|EcCVyBNg@D}AHcFPgKTuMZsAJoE|@oBf@mHpA_CRmCAkEF{N^s@BuADeDZiBb@kFnB{DrAyBf@yAn@cD`A_ItByEjAiBf@{Bv@mAh@a@LiAh@_DrAkCt@{AZsAP}AHiABaA?}@CgAGmC]q@MmN{ByEk@oEa@sHo@iD[wFs@kFw@MA}GaAeCY{@KUCaEWuBQ[CuAM_De@cEi@{@MuLeBsCa@oBYgMgBuDi@iDe@KAaGw@sD]gAGqBMeAEiFM[?y@?yBDuDHuAFyDNuAHE@e@FK@eFT{GZu@@Q?a@?M?gBEuHm@qMkAgAMyAQiBUyCc@qCc@yE}@eGeAa@KeJaBsHuAqASaCa@gEy@w@OsCi@cAS{JiCsD_AcASu@KaCQi@CsLw@{DUg@CsAAWD}BB_IRaHNc@@wADqCDkGPwJXyELe@@mAB{KTaABkA?mAGyB]wAc@UGuDmAkDkA_DaAcAWcBW_BOw@EaKQyGKwHOaAAmBEqCEuACi@@oAEgBCiCEoIMeBB{BLqC^kCd@wE|@yCl@s@LoFdAoDl@s@Lk@FmAJgBDiBEiAGeBWc@I_@ImDs@kC_@QAeCMk@AcEAeF?S?ob@AwFBmAAk@As@?u@AyCA_A?g@@cB?_A@w@@w@DeAH}@JsAPmObCeC`@m@J}Dn@yGdASB_BXwATcCb@qA\\UJ}AtAMLGHi@^_@\\{@dAk@nAg@fAg@dA}@jCM\\Wn@Qp@Wn@AF}B`GmAzC{@vBKDCTaAlC}ApE}@dCiB`FWr@mCzGcAjCENiBvE}@fCi@`BOb@mC|HKVKVwC~H_AfCk@xAgDrJ]n@e@t@]l@k@dAg@v@sBlCu@dA{@vA[p@Sj@YfAQtAS~A]fDIr@o@dHg@bGa@xEOlBOnBOdBOvDGbAQzBSjE_@nFKhCg@|JO`C[|EMhA[fBu@pCaBtEqE|LIVqCtJoCrHGPIP{@tBc@jAu@tBUr@Uv@Cz@W~AUz@o@jBc@jAe@`AsB`EeCvFc@fAYRWJYr@cBlDQ`@g@hAyBzE_BjDqFlLm@vAq@`BSd@aAtCmBdF{BnFkC`Gc@bAyBdFEHyAlDwA~CcB|DUl@MX_DbHq@~Aw@xBq@zAk@rAo@tAwCdGc@v@i@bAiCzEU^uDzGaBzCo@jAyAtCc@x@mBtCyBdDcBpC_CxDeAzAwDbGyA|BoB|CsA`CoB|C}C~Ec@r@ILq@bAaAzAy@~Aw@fBcArBw@~AcC|E_G|KU`@aEvHuIxOuA~B{B|Dc@x@s@pAaFfJW`@kC~EiBfDqBvDeCrEi@dAcAjBcB|C_BvCi@~@GJqA~Bg@|@[f@gAxA_AbACDaCtBuAhAMLqDzCmEtDuBtBIJSPeM|My@z@gCnCaAdAeAhAyB|BaGnGsBxBwAfCe@h@wAvAoEvEaCjCmBpBiA`A_AfAeGzG{F`G[ZkErE_CbCaGhGiAjA_BbBkArAiAhA_Ar@q@\\ULm@h@QNQTo@x@eA`BkA`Ce@hAWj@uBdFeA`CyBtFyBnFi@jAe@dAkBlEaDvHiApCaErJ_EhJ{BbFgBbEiAlC{BrFaD|HmCjGcB~DIR_@x@u@hBc@`A_AlB_@l@}@vAa@j@MPg@t@e@jAs@bA{@bA_BzAs@l@kB|AUNwEpDmA|@}@l@aD|B_DnB{@f@sDpBwEzBmD~Am@\\w@\\qAf@mARqLhFcBr@eEfBeH|CuCnAWJgCfA_H|CkHzCuBt@oBb@gBZaAJgBFmA@o@AsACkBSs@KsCo@{@WgBi@kC{@wAg@_FcBi@SaFcBgDcAcAWoB_@UEyAUwC_@mAKcBGeAAg@@iADcCT{B`@e@Nk@TeB~@_Bn@aBl@cDlAaC|@{G~BaBf@qBf@kBXm@J{CXwAFk@?gA?G?eBU_EMuCKsCKmBKgAMM?}DOsAI_CGuBKk@CuEQqACaADS@O?YFo@?aF]uDYeAIw@GiBOi@EuCOeMs@oEYoCS}DWwIu@}E]eBWaDYqGe@gAIUAc@CeCOoF[kLs@yBIeDC{CAoAAU?I?kF?kDIkCKI?m@A}A@{B?O@_BAcBJw@Fi@FgAPqBj@mAd@_Bv@[BOLGBSb@gBbBeApAUXgAjBc@bAGPmAbEOx@gAzE?@oA~FWbAy@|Cw@|BgErLmAlD_BjEo@xAcAdBiBbCmErFuBhCgBvBwDvE_@d@_AlAsDxEgCzC{@|@aC`CkD|CiGtEcCdBq@d@_@VyBdBcBbBgA~As@hAgAlCMd@}BhI_AdDoAnEO`@KZ}@~CiCfJuA~E_E`N[~AK|@SzBe@hGMxAIz@aBrSUxCCVIr@OrAw@zD}@xDa@vAeAtEK`@gFfSi@pBGVe@zBm@jCy@xC{AfDiEfIc@|@wFvLkArBw@t@MLm@d@[PsAr@_A\\iCv@g@PwE~AcB|@gA~@YZUVe@r@GFw@vAa@lAk@bCcBlIsAvGWhA]zAK`@c@nAk@hAu@lAeFhI{CxEm@lAc@vA[~A]~De@tF]hBc@zAi@lAg@v@cApAYh@aDnDiBpBwInJyFjGi@j@gHnIA@q@p@uC|Cc@d@wB`CcApAq@jAo@hBS|@SzAQfFKzBKtCGvACz@ChB@xAFp@NbAxBnLRrB@nAExAOzAId@I^mBnGOb@y@hCOh@a@pAiAhDeBdEw@|Ae@~@a@t@iAnBgElHaIzMg@lAM\\Oh@SdAMrAAb@CxAAz@ArPCvM?rCLvDNpB\\bCXzAh@tB~@`Cp@vAlBnDTb@|GrMpDbHfBhDvCdIhAhDjAfDL^fAjDNr@RpBBv@@p@?\\Ax@QvBShAk@jBe@hAs@hAmAxAaAz@mBjA{@b@kAt@y@`@oBhAoAbAw@x@cAzAYh@}A~CwAnCi@z@q@z@QPON_@ZgAt@w@b@{Aj@{Ab@oG|Ao@RcBt@aAn@w@p@kDzDyClDwBbCkGbHmBtBqC`DyB`CcI~IY\\}AbBeBlBqAxAoC~Cw@x@iAz@eFrD{BbBaLbIkFvDIDMFgBnAaAv@gChB_@X}EpDoBpAaKbGgE`C{HnEwIbF}BvAMHu@j@_@\\e@h@s@lAi@lAk@bBe@dBI^ERmAhFEPK`@gAfE_@fBi@|E[jCAHSrBUtAa@xAO`@Sf@Uf@_@h@uAfBQJkOlQa@f@A@s@fAi@pA[dAO|@c@|GQlCUhDGbAQdBWrAWz@o@vAg@z@Y^g@j@_Ax@u@h@{@h@kFbDqBnA{AdAeCfBg@XID{CnBcB|@qEnBk@RcA`@gEhBm@Xw@^u@d@gAbA}AhBmApAeAfA{@z@_BbAmAj@qCt@gHhByAj@eAn@}@t@{@z@m@|@y@~AwDdIs@jAq@v@kAdAwBtAc@XEBiIfFeEdCuBrAaC|AwAdAuAfAw@z@iA~@cDhDe@d@u@x@kCtCeAdAcDjCeAr@kBnAsBlAwCtAuB|@_@NcFhBkH`C}@XqC`A_AV[DaBTeHn@}B\\qBj@WJg@VuD`CuJtGkIpFsA|@WRkEpC_@TOJqHbFs@l@eL`IkAx@eG|Ea@^y@z@_CrCcA~@aBbA_CtAaCvAwC`BiAj@{HbDYLuFlCw@VgMdGeGrCkAf@YNaBr@yAj@oBj@qAZoMrCuCt@mAf@yAt@mKhHoAx@uDhCeAn@}HjFeAp@}@d@gA\\a@NcF`BgPbFqAj@uAz@cBrAyAjAcDlC{BjBgBjAkCxAuAl@gAb@sE`BuJlDaJ~C_Bf@yBr@kHdC]HsBr@iFpB_Al@iAdAUV_AnAqCxEi@~@ILEJy@xAoAxBYf@ILINQZaA~A_A|Aa@r@iBzC[h@wFnJeKdQ_AlAqAnAy@j@iAl@yAj@_IdCqVxHwH`CeBh@sGpBkBt@KBg@VmCzAiBjAeEjCgAr@a@VgEfCiBjAeAh@_A\\kAZg@LkFrA_Cv@eDx@qA^oHrBsDdA_B`@k@NaDz@w@t@kFvAuBl@wCvAKFe@XeAl@{@h@{@f@iKhGk@XgAn@e@VgDlB_@PHRf@p@N\\PZBBLFv@\\HNJh@h@v@T\\`A|Az@`ABZAZClABzABp@@t@?TRx@BJx@fBDRF`@Gd@Sr@CHI`@k@n@MRSl@AFAN@RF`@R~@Np@`@`B@p@?lCDn@|BpKN~@?TA^c@~Cc@vD[xCu@fH?p@?PF|AX`Cl@zEz@jGLLR@fAYNEREX@nAd@LDZTLX`AvGPfAr@zE^lCPhAb@tCn@rE`A~G`@tBBZLxAr@jF~@~Fr@jEPnAjBrNXjBp@zEv@bF^lCf@dDv@`G^bDnA~H`@vCh@tD^dCfB|LHb@`@hBJ^HVZz@n@hBb@nA|@pCj@bBfBlEhBnETl@|@dCb@dATj@|@|BPd@Tn@l@rANh@xBnGp@fB\\|@zAxDj@fAPVvApBj@t@|@lAp@x@nClDrAjBbA`Bz@lAh@z@bAfBr@|A`@n@^fADNBn@EdDA~AUxA?TCdAFrBNbAXzADR\\rAz@~DNn@hAzEXbAn@`CNf@hAzE\\zA~@`E`@|Ab@`B`A|DVnAn@hDPp@Xz@Pd@l@tAZZvAn@|@ZbAb@l@TB@b@Rn@TVHtAb@^L`@PfHxFbBjA|AfAfAdA|BxBr@p@r@p@h@b@jAv@fH~EdEbDx@h@d@^~BnBVZPXlA`D|AdEXhAz@nD|AvFp@jCFRnBrHt@tC\\rArAxEXdAxArFNh@LXt@t@rD~CtD`DzBjBnAdANb@N|@B\\NzAN|AP|BJ~@Z|CPjBRrCLnABTR|Ad@~BNl@Nj@p@dCJf@Pz@Fd@DXDdAEn@MvAFj@DP^fAn@tBXvAXtAj@hCT~@@BnAdEJ|@H~AH|AJz@V|BXhBXdCf@dETnATvA@J\\jBv@nEJl@Tv@p@bBv@rCJbADzA`@pBbAbFPx@hAnFj@nCNLhAf@PFr@XLV@h@ApAAB?fAJhA?~@QpACT@v@T`AjBjFxCpI~C`JZjAnAjD~AhE|AfEzAtEfCdHf@vA`C`HPn@L`@N\\|@dCz@xBpA|DP|@Hf@RhAJd@`ClLTfA`@xBDTNb@H^DN\\fArAlErAbErCxH^|@Pd@fAfDnB~FhBrE\\x@n@lB~BzGzAnE|@bCx@xBPj@v@vBn@tBlBfF|A`ErA|DJRlAtB\\h@PXZd@RRvDrD^^LLr@v@x@jAT~@RnALlB?DZhCD^x@pJ\\|A\\`AbAtCN`@|AhE^`ApBrFxAlDNXXv@fAzCt@dCZvAJvABj@P`Ah@hBTv@Tj@f@r@XP^PXNb@\\Ch@TdB\\|AL^\\t@Pb@j@nCFj@?Z?\\Af@O~AY~DCfAIrB?HEl@WdEG~@WpBPz@@PE^[bAa@`Aa@`AeAbC{@tBy@nB_@n@]r@i@z@Ud@kBbDM\\GPIV[fACHCHGLOZ]h@iA~Aa@r@w@hAW`@gAbAgE~CaErCa@d@mCjE[f@m@t@W`@URu@r@aEfD}FpEcAfAgAbBS^i@t@aAzAg@t@W`@sA|BU\\aE|GsCtE{AxCa@~@ABcCvFcBjDi@`BIX_BrEKZO`@Wz@MnAC~@OtAKp@c@`CSjBE|@Ed@SjA{@lCq@hBsAbDsAdD_@z@mCvGgCfGwBdFa@`Ac@`AcAbCiBhE}D~IkAjCk@xAcBfEaBfEmCfHM^Wp@eBrDw@~AsAnCyDfIs@xAqAnCiCrF}@lBaBfDwBrEyAvB_FbGeDdEi@p@}@jAyB|CWX}@hA]v@uAxDs@~BcAfCGTKv@BhAD^TfBXrAlAbEl@rALTv@lAp@n@\\VtAj@TPFL?Ze@nDFf@xAtHBR@L@zA@vAE^IVeBlBiArA{AnBa@j@m@tAIRGVu@fDG\\YdAYp@KPk@~@Oj@i@hE[rCOpBAVFp@^bALPV^j@v@?NIJkAd@Q^CJCV?vBSbCITOJsA\\gAh@eBz@oCvAKTAVHRd@~@^t@zBlCBDDRF~CF~ACJo@d@aAj@aAl@g@b@SXWt@cArDg@~Ao@dBOVg@v@W\\CB{B~Ba@^s@j@cAd@y@Tg@LwDx@MD_Ab@qAp@w@^c@Nm@HgADo@DqDl@g@HeDv@m@`@KLkEjFaDrCaGbF[XwAlAq@d@gAj@w@`@WTCBQf@CVGx@_AtEEPGf@?h@RpAPbAPj@AXJt@Rp@|A|DFf@?NAbFC~EEnF@XB~C?FNtC@F^pFRvC\\nCVbA\\p@bB~CJh@?`@?PMnAGl@QrAIZk@xBg@vAe@dA}DlIOXe@|@UdAGl@Cb@?fCBh@BTVv@bArBz@lALPTf@Z`ATfA`@pAbApCVx@~@~Ct@lBvAtCf@`ApAtC|@pBd@nANp@R`CP~A?BA^Md@[t@[l@Qp@AR?P@TDf@ZpBDl@AHEb@O^k@nA]zAi@tCW~Ae@tBIX]~@_BhDe@nAUx@YjAU~AGvA?`@CLdApCZt@`AdCTh@h@bAh@x@vBvDf@n@DDl@n@BDr@~@dBvBTd@\\t@d@h@bAjA|@jAd@b@bBhAbBhANLz@p@j@x@X`@Rl@Hr@T~CDd@?BP`B?r@I\\a@r@KPG^?Tr@bDj@jCTx@Px@hArC`CzEVf@jCjEbBrCTj@Dj@?lBBfCRpDRzC@FLt@Bv@BbB?\\FlEA`BGh@O^EF[`@uAjBgAvAQVgAbCUt@EZ?LGdAKjAOnBGvAAd@?tD?RBjD@z@CtK?tEGpCAdAAdD?|A?p@HxBD^Fb@Hd@d@~DRpFHr@Ft@TrD@`@J`BN|@Rl@f@`APRJPHd@?|@A^C|BFn@T|@d@lBDz@Cv@Gf@GVMf@y@hDUpAAxAFpDDpCBjBXxH@j@Bv@ZrDHvA?tGAvBBf@Cl@@z@@r@?fBXdDPzAHtA@x@HhEAz@@bAHlG?`A?`@Cp@YxECPMtDEl@Iv@SjAs@rDgAlEOhACVMxAGrACvAMjAShAC`@Jz@Hv@PnBd@zEB\\@p@@dACjAEr@Qx@u@lBGVGh@Bt@j@zC@n@E^ENQz@Gz@B^Pv@p@hBTl@Xz@FTb@tETp@n@zAlBhERh@LVd@bAVr@h@|ABH~@xAN^Nf@dAjGRfA`@nAxAlDp@hAHRpAlCL`@H^BZIlAATG\\KXQV]r@ERCn@KRo@d@[ZEFc@r@If@En@Df@BTLd@Rj@DX?NAn@k@|CGXYv@g@dAKZELYnBYv@Y`@aBfBw@lAUZo@r@s@|AYR[LM@i@@eAr@i@Nc@B_BKaEe@{AH_@HWPM`@@`@\\nBVtC@l@?LGhA@r@Fb@n@pAJ`@?\\?@EFCDID{BLc@DaDt@QFOHMRSr@AVFt@h@hCJt@LfALxBDjBG~AWzA_@dAu@nBGPu@dCITi@lAeA~BkC|Fw@hBaAxBwC|GQj@Ih@WhBSl@e@v@CB[d@MNUl@Wz@I\\iA~EQjAkC`PMx@Gr@?l@FVb@zA~@fCJ\\Hl@?f@Mr@kBtIIjAGfB]~I@~@JrADp@Fb@L`B?bAM|BKpBSfFCZIrAOdCK`AE^If@O`AYjB]rBOv@UbDGf@OnBCtC?~D?n@?v@AnD?bBRxAHh@LlB?t@E`@UjBOj@M\\_@lAq@`Ec@xCIxA@fCDh@b@nAb@rA`@pBb@jDBPHhAFdJ?@Gd@S\\SNIDgA`@ODo@PA@SPGPGv@QhDK|B?NDd@Nb@PXBDNRrAzAx@dAT`@\\|@Tv@Jf@R|@v@jA`@t@HP\\z@VpBRfCBbEGdAUv@Wx@@XJd@?@P\\TPv@PdAH`AVf@XTZ^x@J`@BLb@dCHh@PbAv@hCV|@`ApCd@bAnBjDv@lAN\\JXJ^|BfIJr@Br@Cz@Gx@B`@JRZV|@d@v@d@PRTt@P`APn@|AvEJh@Dd@Af@MfBUd@YVw@x@AF?LFZRh@LPDFRXh@b@bA`@zBz@fAf@l@Xp@ZPJPRLb@?H@LIx@Uz@EXEr@B\\BHXt@FLP^j@`Bj@lAZb@TVl@^`A`@^J|Ab@fA^tBnAPJZJdD^pCl@^RDHFLFp@CtACZAXDh@@BPh@x@rAR^LXL\\n@tCl@pDHvACr@e@bDg@~BIb@m@hCYlAWxBWvDA^Hd@`@r@l@bAd@x@Zf@NV^z@l@xBNj@Hd@h@pBb@t@f@l@~@jADFfAz@hAx@|AtAf@j@VRx@lAXf@PT@@JH`A~@l@f@`@Nj@Dn@FZLJPH`@XPRLNLTB\\@xCJLFb@l@NTXd@jB|CT\\~AhCbAxAvAzBDDRt@Dd@C`@AHSh@MTm@l@oAx@w@v@KJKV?^Lh@Zd@XTXBRCPIb@Od@?`@Ld@d@Vb@Pj@JpBHbDGv@U`ACFONk@VGPDZJF`@AXHNNFZ@~@@H@FLd@NTRJt@LPLFJFXH|@@p@MfC?BI\\QJQB]AS@{@RUPCNBLRNRNfBz@r@b@d@^\\`@`@fAv@jCh@bAx@rATf@DNDt@GnBa@~E?VDb@^lAfA~BjBbDh@r@XRTJnA`@dATtCD~@CtAAtDEjCC`CJ`@Dt@VTRr@jA`@lA\\|A^tB`@fBl@~B\\bAVh@bArBl@nAV\\PZXRb@RnBx@@?fCbAz@Tr@FX?NAp@KnFyA`Be@bBe@rA]dASjA?|BRx@Ld@PPJb@ZXXzBxDb@j@hBxAxAfATXHRHXD^ElAKf@On@Cd@AZ@~BJpEB`B@lALjEB`@V`CPz@r@|B@DXz@b@l@zAfBx@dAhBfB\\^lA~Ax@hAlAjALRJTBXCPa@|@G^Ax@DXCvAGl@IjAOvAa@xAm@bCYdBARAf@EXU\\{@RKNQ|@ATFRDR@|@@d@YvAQXKPg@`BYb@CLBRRt@?j@Er@ZpCAbBJzB\\hBPtB?j@CNGj@W`AIbAWt@s@dDK`A@zBItAE^ETCPBXLNNFPALGJQDIJo@Fc@Be@HeATeAl@iBx@eCTe@RWDGXWVMb@?ZBVLFLAZAJe@hCSfC?Z@HZv@ATU`@g@r@EVDl@?LKl@GXA\\Hn@R|@?h@Ch@D\\LLVLj@ZDFDDFRBLFJPVXT~@j@PRHPH^BbAMxEI^WX]Hc@@kBUI?QDIH@NNRt@t@Z\\xAp@TRXJv@VtB`AJXI`@Sr@@JJJJLv@b@Zt@Hb@F`@XlAVh@HJLDb@?ZKVOf@e@v@UfB[RMNQR]TO`@Gf@C`@M\\]RU\\SNAN?~@DxAPt@?rAQ`@Al@Cb@MZUPe@DW@K?WWu@@_@Li@r@iCf@}@Ra@l@q@dB}Ad@W|Ao@d@GbACHCRGd@[HIn@m@hBaAdAc@PELChBG@?fAYfBHn@Gl@QXUN[T}@l@eELg@HGJBHNFx@AlBGf@o@nDIPURo@^u@^y@T[B[QKA]?gAb@_AJUHUX}@rAcA|AYl@EJSTaAl@kA`@OL?Bg@fBEd@?l@DFL@LEJ]BU?KDQXc@VUn@_@|CwAVIp@Dp@ETEVKr@e@`@o@b@oABALAPBFBv@j@TFd@BPEf@Uv@w@ZKLCjBS\\OROPYBKFUBq@^u@`@w@`@}@f@w@HELGVAXEJGJML]?OSo@BWNULING~Dw@JCf@Af@HfAZ\\F~Bh@\\Dd@@f@E`@Kr@[xA[jAAv@LjDrA~@Tb@@bDF\\E^Kd@WpAi@HC\\EbAFrCFzACp@Ix@SpEAb@?fAGlAInC]XEn@Mp@Mp@MJCh@QXWp@e@TMRA^HRF|@d@h@^r@\\V@HANGdAy@\\QZE\\Df@VpA~@^P@?\\Hv@HfBDhDNb@BZBl@NNN@B@JETo@lBEL?F?ZHr@Pd@b@z@V|@@v@Ix@Q\\QTERD^J`@@h@EdAG`@@VJ\\~B|CBD@J?PMt@MzABv@D`@F^Tn@Th@@FB^CTGL]b@{@n@KJ]^c@JK?KCyAi@e@?YFIFMHa@f@_@z@WfAo@zCEh@BTBFFDf@LTBZCPDf@\\TRb@Xd@JbAAf@EbASz@Qj@SNGv@k@d@k@`@YVEV?h@J`@RtClBn@j@LRh@d@^LTDn@DjADTHTPHRj@lDJ^FJBHPn@Dj@HNFFn@`@r@n@P`@DT@VC|B@nBLl@NTRX^XPFTFvAZj@H\\DLFFT?TCDCB}@XaB`AWNEL@RLRXd@P`@AZGLIJIDs@No@V_Al@k@d@w@bA[L_@@k@MSMOK]GUBQLu@v@k@Zm@Pm@LWPCDEFCTDP\\x@HV@h@O\\WZiAvBi@t@w@v@a@^qAz@UXq@lAgAz@UVS^CFMb@An@ETO`@MP_@nAMd@Id@[bAKVaAfBYdA]|AQ^IJMP_@`@u@d@a@Rg@PoDdB]Ny@j@i@p@IX?b@BTRdB?B?JAFIt@QfAEXB`@Vn@Xf@b@n@Vb@Vr@@j@G\\Oz@@ZX~@ZfAPn@Lj@Ah@_@tAMZG`@Ev@DdC?jAB^P|@Nl@Pp@Zz@l@tB\\z@v@bATR`@l@FT@d@OlAi@nCGTy@zCKTc@~@GTS|@Cf@Lb@RZ`@^LL^p@Lj@Rz@BHDZH^b@hBLn@l@xCBdAC^CJCPq@fDI`A?RFl@ZzBNhAL`ABN`@jAx@|AZf@rA~BbAnBZf@nArBl@dAdAlB@@nBlDPVl@|@hBjC`AvA\\`@~C~Ep@hAfFxHFHn@z@V\\Xj@PTv@pA`@l@xArBXZ~@v@~ChCb@\\~ApANXZv@x@rBVr@Tr@h@nB^lAT|@FRX~Ah@|DRv@DJnAfDn@zAb@fAn@vAl@lA\\Z^NnC`AhAb@`A\\j@R`DlApBn@~CjAh@Th@^`AbAv@|@j@bAvAfDd@|AV|ATbCNdBPvA^pDt@`GHZP|@|AzFj@nB^pAt@|Af@v@^p@H\\RfDGx@Y`D_@zEDb@HL@@TJvAFRDRRLXVrARl@v@r@vBfBl@p@PZLRpCfFx@dBRd@^vALXFJt@lA|@nA`DrERVvAjBfEzFjB`CtArB~@|ANj@JfA@XD~@Lv@b@pA^n@d@b@t@l@jI~GVLt@BpB?jAIRCdAK^Dt@`@r@f@fAl@R@d@GxBc@pAC\\@|ADXHvAl@~@XfALt@Rd@Lt@XlAj@FDb@Zn@l@bAv@TTd@\\~@x@tAdAt@p@XXTXFJXn@Hf@H|@PpBDpAAfAAd@_@hC@R?HJj@FNXZrCtB^h@h@tBj@nBXn@bAbB~@fBf@`ARb@d@n@f@b@VNj@NJ@n@@j@ClFq@ZCJ?JDdAt@`@\\^Rf@Pv@NV?BUKc@k@mBUJm@\\_@\\GNKVeAtCs@~A]jBQd@EHIJGLs@bAiAbBu@bAe@n@ADAF?LA`@?BDvC?TB~BEXCJQj@mAzBe@~@sArB[XYXaA~@WRe@\\sDnBm@h@a@^QPy@`AQTe@r@k@|@INUd@gAlBm@lAk@xAEHw@zB]l@k@t@_BtB_A`BWn@aAbCUj@Qd@M^GXGv@AR?b@HfHChAKZm@rA]r@SXQHSR}@pAIXE\\I`DCtCGpCEtAAJOdAi@xBSjAALAb@\\lIIjBWdCEpCFtEAhBKh@Sh@aArCoAdEcBpH_BfHiAnFCJKXoA|Cs@rA_@p@OTe@n@QTs@z@U\\aAvAYj@ADCFEXAn@?FBrAAbAG^GN?@gB|BWj@]hAwAxHi@nCy@vDKx@e@rEK|@IVM^UFy@XgAl@WLg@V_CnAiCdAgA`@OH_EfBaBl@aA^s@\\QHQJMF_Aj@c@X{BfBeCdCKPu@jAm@lA]f@Q`@Yv@i@bAc@v@uAdDu@`BYv@c@~@w@`ByA|BMTQ^}@nBa@`AS^{ApBkAnA]Z_Ad@_@La@L}Ct@OBYDqATcEl@}@BA?cAAw@CmAJwBh@_@Nm@X{@d@OLKJOPg@n@w@nAc@n@k@x@m@hA[t@MVKVy@fBKXKVkEhKO`@y@|BGVI^YbAcAlHUtB_@dDKzAGlA?b@?bAFtB?X@d@JzD@z@N~ALhAMFcAh@gE|BkCvAaDbB}Ax@eDdBaCpAuBrAyBtAmAv@IFgBhAkDzB[TMLMLEHe@dAM`@g@|AKTY`@}BzB{AvBiBdCmBzCWb@KVYl@Sf@A@Of@qAxD[z@kAvC_A~BQ`@GPkArCMZMXQf@g@xAqAjFIz@?rCAxDWvBGfAGfA]rCM`AMbA@f@Jh@^~At@nCF`@Hd@JhAF`ADv@RnBBf@Bt@HhFEl@SbAWh@g@l@uAnAsAxAnBjECh@WfBKlAB`@`AlDH|@DhDAnDAf@?RCjACx@EzEAf@ApBA~A@d@B|@D|@BbAEjBGhBAPWpFAHGjAKr@Kp@u@hEId@Ih@?l@@vDAL_@|Bo@`D[vAGX{@rEc@fAm@~AiAdCUd@mAtB]`Ae@vBQx@Qz@ENWpAs@xDIXQj@IV_AlCiB`EOXGLo@dAEFoA~AGJiC`Ca@bAW|@YnAk@xCs@nD]vAOhAu@nEI~@@jAz@dLNpAx@xBvArDBJBZAZ}@bIGf@[dDg@lFCZMjAQjAk@|CMt@AL?bAAnAIV_@d@_@VOLEHAJOhDA^?R?ZJ^NR`AvAr@v@hBzAnB`Bt@p@TL^Pt@TnAVfARbARJDNH^^d@x@HNzAhCv@pAHPj@`BJ\\~@lDFT`@fBd@hBBFr@jCJb@F\\@HPdAVjBPvA@Hh@rEX`Av@dCLXHJ|B`DHL|A|BfA~B^j@FLNn@^lDBV@x@KxAC^YrCAJUzBYlCOjAGl@CV?x@@^P`F?l@D`BBjBBrA?P@t@JbDH|ED~AFTRzBF^d@rCRfALf@Rd@^h@t@t@~@n@dBfAbClAZNjLhHp@j@R^XNn@f@n@b@DDXRFDL@x@j@LHHFFFZ`@~AzBhAbBDDf@v@bB`CBRhAxAl@x@v@pA|@rALRFJbAj@tASHx@?Hp@Th@Pb@PnEpAxD~@XJRHDDPVDHTp@ZbAJZPh@~@|CL^f@xA\\hAPj@Nb@Fd@t@bCN^\\~@Vz@XbAHXLd@HZN`@DLRb@|@bAh@`@RLz@b@hCEBn@Jb@\\fARh@l@~Ab@nAAl@C~@Cn@?b@AlAMDi@T]Lg@`@iC`Bp@hAZf@NVPZXf@R^^n@PPb@^JVBFNdBDr@FXR`@~A_A`Aa@dBi@NE~A_@pBK^ILGj@]Na@TeAPi@Xs@bBLh@DnDTvAB`@@tAG\\C`AD|@NbATvAJdAN`@@T?N@hBFAFQdAK^UxBW`CEx@lBJPDYhBENGJABMXa@^wA`Ai@bAqAtCIL_@f@kAbCOVY|@SjAQfBKtA]~CWlCGv@UvCE`@WxBMfAIPq@A}A_@c@Ky@AA?_@B}ANs@DGJA@@JLDnBR^LVT^t@j@fAZt@DT@JAlB@VCbCEhCAn@Kf@Qn@[jAEd@KVILiApAw@r@o@`AITKh@OrBOz@]dAQh@Iv@?dE?JCr@A~BGbDEdA?NAp@B|@Bh@TbC@^@`AGlEI~EC|@Mt@K\\]nAWnAOlAGdA@bAJr@XjAJXh@x@fDrDp@x@VHzCbB`@b@RV`@p@d@f@|B`Bp@l@zArAV\\d@t@LRVt@\\jA`@`BPp@j@lBfA`D`@jBJr@TjATnAd@pC^lBh@pBL`@Lb@`@lBNzB@TFd@BTPz@@d@AZGvA?d@Z`BH\\VrC?LIjCwAPWJGL_@vCCFSHaCr@KLKRMZqBnEA@i@|Be@jCWxE}@fGWfCEj@Gj@ENKLoDvCCTDlBA`F[vBK`@Ob@OZOl@c@tBYv@SzBO`@CNaDvD@FIV{@jAO`@ALL|B@Lb@pCJ`APtEDVDb@P~CTnC?P?p@BX`AzGFZH\\N^Vv@ZbBN`B@LKnBK~@w@AmADaBLqBN"}, "description": "Varthur - Sarjapur Rd and NH 4/NH 44/NH 48", "warnings": ["This route has tolls.", "This route includes a highway."], "viewport": {"low": {"latitude": 12.6853815, "longitude": 73.7403234}, "high": {"latitude": 15.7192481, "longitude": 77.9320709}}, "travelAdvisory": {}, "localizedValues": {"distance": {"text": "712 km"}, "duration": {"text": "13 hours 38 mins"}, "staticDuration": {"text": "13 hours 38 mins"}}, "routeLabels": ["DEFAULT_ROUTE"], "polylineDetails": {}}], "geocodingResults": {}}