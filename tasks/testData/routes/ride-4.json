{"routes": [{"legs": [{"distanceMeters": 21272, "duration": "2291s", "staticDuration": "2291s", "polyline": {"encodedPolyline": "sx|mAe`oyMKPb@Zn@d@l@`@LHp@h@d@Z|@j@h@`@h@\\fAx@B@LHf@\\PLn@b@VPRN`@Xl@`@PLDBp@d@XRh@^RNf@\\^V\\V~@l@PNJHPJjAn@FB\\NXLd@Tz@`@n@XRJFBLFn@\\ZNx@\\TJPDt@RNBn@DX@V@V?bDDT?rCBL?p@@pA?j@?h@Ar@AfAAtAEd@G|AQz@SDAnB}@zBeAdCmAbFaCnAm@zE}B|Aq@|BiAHE^[PQf@o@fAuA\\a@dB{Bj@u@`@i@t@_At@aAfDoEl@y@n@{@j@y@zB}C|@iAf@c@n@SxDu@lB]nAUr@OPCnCi@|AYl@MjB_@|A[fAQjDg@`BSd@ED?x@G`Ge@`@ETA`@CNANC|Ec@z@IbAKbCUn@IfD_@fAOzEq@rEo@d@KHAbDe@|@IrFq@LAlBSTAv@I\\GHAREpBa@j@MHCVIx@WvAe@r@UhAc@`@MDANEHEv@[\\Mr@U|Ai@TIz@UHCtA_@x@SbBc@H]TeATaAT{@DQJa@F[Jg@BIT_ADOPu@FUR_Ap@cDRcATiAd@gBLa@Rk@^y@JULYVk@nAqCdA_CVi@xAiDVk@v@gBb@aALYjAeCtBcFh@oAPa@N]Te@\\o@DE\\WZMf@KRGFALClBW`BYn@KFApAOdAOb@ILAxBa@zCe@bAO\\Ev@M|AW^EdAGf@Cb@ArAA^AV?dAA`@?t@?dA?X?^?F?~@I@?j@MPGPGPKbAu@t@m@~@w@DElAeAzBiBhA}@XY\\WTSvBeBXWNM^[`Ay@VU|AmAb@_@^YNMzCmC\\WLQVs@Hg@@KVyBVaCBSXiCTqBDa@Fg@@KJqAH_ALgCBc@?ILcCJuAF_A@WFwA?k@?q@Ae@IyA?c@AmA?_@A_@CoAEaB?QAYCcAAa@GkA?YCIBCDAB?f@@bI~@l@HVD`@Fz@Hl@Hp@Jv@JVBF?vEf@j@FnDVtBFt@?nBB`@?`EDj@?`BBhDD~@@`BB^?nGHvCAJA`@E|Eo@`Ca@fG}@|Ba@bLqB`Cc@|A[xAY\\GfB_@|KoBtBYh@INA^E~AKJAjE?v@@fBKnFa@hGe@h@Gx@G~D]j@EtBUvBKzBKlEUfAIj@GrCErA?`BNTB~@JlAPvARdAN~ATj@HvBTj@HfBF`@@zFPzAD|GRB?|@B~DJ`EN~AFJ@vFTjBJfDVnAJxGn@fANtHfAvIlA|Df@dAJ|BR\\BnCTZBb@BhL|@XBnBZRHfAPPDd@NRHl@\\\\V~@hA@@t@jAlAhAj@^zAf@jB~@v@VfBJl@Jz@NvAJfAVzBt@dA`@B?VHRHTP`@Zn@n@JJ`C`C~EnEJHbBzALJhAv@FBdG~BhBr@jE`BNF~ElBfBp@zCjAfBp@ND~An@tChAnFpBp@T`Ab@VJj@XTHt@\\p@ZfAd@vB`A|@b@|@^XDl@IPIJOd@yDD_@j@iFFm@L_AJw@TcBR{@FUJWNGJ]p@mB@AXcAj@eBDQx@qBd@cANWFI"}, "startLocation": {"latLng": {"latitude": 12.9321025, "longitude": 77.7422734}}, "endLocation": {"latLng": {"latitude": 12.7766925, "longitude": 77.77766280000002}}, "steps": [{"distanceMeters": 12, "staticDuration": "4s", "polyline": {"encodedPolyline": "sx|mAe`oyMKP"}, "startLocation": {"latLng": {"latitude": 12.9321025, "longitude": 77.7422734}}, "endLocation": {"latLng": {"latitude": 12.932161299999999, "longitude": 77.7421804}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head northwest toward Varthur - Sarjapur Rd"}, "localizedValues": {"distance": {"text": "12 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 6113, "staticDuration": "691s", "polyline": {"encodedPolyline": "_y|mAs_oyMb@Zn@d@l@`@LHp@h@d@Z|@j@h@`@h@\\fAx@B@LHf@\\PLn@b@VPRN`@Xl@`@PLDBp@d@XRh@^RNf@\\^V\\V~@l@PNJHPJjAn@FB\\NXLd@Tz@`@n@XRJFBLFn@\\ZNx@\\TJPDt@RNBn@DX@V@V?bDDT?rCBL?p@@pA?j@?h@Ar@AfAAtAEd@G|AQz@SDAnB}@zBeAdCmAbFaCnAm@zE}B|Aq@|BiAHE^[PQf@o@fAuA\\a@dB{Bj@u@`@i@t@_At@aAfDoEl@y@n@{@j@y@zB}C|@iAf@c@n@SxDu@lB]nAUr@OPCnCi@|AYl@MjB_@|A[fAQjDg@`BSd@ED?x@G`Ge@`@ETA`@CNANC|Ec@z@IbAKbCUn@IfD_@fAOzEq@rEo@d@KHAbDe@|@IrFq@LAlBSTAv@I\\GHAREpBa@j@MHCVIx@WvAe@r@UhAc@`@MDANEHEv@[\\Mr@U|Ai@TIz@UHCtA_@x@SbBc@"}, "startLocation": {"latLng": {"latitude": 12.932161299999999, "longitude": 77.7421804}}, "endLocation": {"latLng": {"latitude": 12.8833302, "longitude": 77.7524057}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left onto Varthur - Sarjapur Rd\nPass by Chrysalis High School, Varthur (on the left)"}, "localizedValues": {"distance": {"text": "6.1 km"}, "staticDuration": {"text": "12 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 4739, "staticDuration": "530s", "polyline": {"encodedPolyline": "ygsmAq_qyMH]TeATaAT{@DQ<PERSON><PERSON>@F[Jg@BIT_ADOPu@FUR_Ap@cDRcATiAd@gBLa@Rk@^y@JULYVk@nAqCdA_CVi@xAiDVk@v@gBb@aALYjAeCtBcFh@oAPa@N]Te@\\o@DE\\WZMf@KRGFALClBW`BYn@KFApAOdAOb@ILAxBa@zCe@bAO\\Ev@M|AW^EdAGf@Cb@ArAA^AV?dAA`@?t@?dA?X?^?F?~@I@?j@MPGPGPKbAu@t@m@~@w@DElAeAzBiBhA}@XY\\WTSvBeBXWNM^[`Ay@VU|AmAb@_@^YNMzCmC\\WLQVs@Hg@@KVyBVaCBSXiCTqBDa@Fg@@KJqAH_ALgCBc@?ILcCJuAF_A@WFwA?k@?q@Ae@IyA?c@AmA?_@A_@CoAEaB?QAYCcAAa@GkA?Y"}, "startLocation": {"latLng": {"latitude": 12.8833302, "longitude": 77.7524057}}, "endLocation": {"latLng": {"latitude": 12.8609785, "longitude": 77.783585}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left after <PERSON>ke wala (on the left)\nPass by the gas station (on the left in 3.1 km)"}, "localizedValues": {"distance": {"text": "4.7 km"}, "staticDuration": {"text": "9 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 6765, "staticDuration": "587s", "polyline": {"encodedPolyline": "c|nmAkbwyMCIBCDAB?f@@bI~@l@HVD`@Fz@Hl@Hp@Jv@JVBF?vEf@j@FnDVtBFt@?nBB`@?`EDj@?`BBhDD~@@`BB^?nGHvCAJA`@E|Eo@`Ca@fG}@|Ba@bLqB`Cc@|A[xAY\\GfB_@|KoBtBYh@INA^E~AKJAjE?v@@fBKnFa@hGe@h@Gx@G~D]j@EtBUvBKzBKlEUfAIj@GrCErA?`BNTB~@JlAPvARdAN~ATj@HvBTj@HfBF`@@zFPzAD|GRB?|@B~DJ`EN~AFJ@vFTjBJfDVnAJxGn@fANtHfAvIlA|Df@dAJ|BR\\BnCTZBb@BhL|@XBnBZRH"}, "startLocation": {"latLng": {"latitude": 12.8609785, "longitude": 77.783585}}, "endLocation": {"latLng": {"latitude": 12.8010752, "longitude": 77.782827}}, "navigationInstruction": {"maneuver": "ROUNDABOUT_LEFT", "instructions": "At the roundabout, take the 2nd exit onto NH 948A\nPass by IIFL Gold Loan (on the right)"}, "localizedValues": {"distance": {"text": "6.8 km"}, "staticDuration": {"text": "10 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 2780, "staticDuration": "308s", "polyline": {"encodedPolyline": "wecmAu}vyMfAPPDd@NRHl@\\\\V~@hA@@t@jAlAhAj@^zAf@jB~@v@VfBJl@Jz@NvAJfAVzBt@dA`@B?VHRHTP`@Zn@n@JJ`C`C~EnEJHbBzALJhAv@FBdG~BhBr@jE`BNF~ElBfBp@zCjAfBp@ND~An@tChAnFpBp@T`Ab@VJj@XTHt@\\p@ZfAd@vB`A|@b@|@^"}, "startLocation": {"latLng": {"latitude": 12.8010752, "longitude": 77.782827}}, "endLocation": {"latLng": {"latitude": 12.779499999999999, "longitude": 77.77061979999999}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "Continue onto NH 948A"}, "localizedValues": {"distance": {"text": "2.8 km"}, "staticDuration": {"text": "5 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 533, "staticDuration": "95s", "polyline": {"encodedPolyline": "{~~lAkqtyMXDl@IPIJOd@yDD_@j@iFFm@L_AJw@TcBR{@FUJWNG"}, "startLocation": {"latLng": {"latitude": 12.779499999999999, "longitude": 77.77061979999999}}, "endLocation": {"latLng": {"latitude": 12.777988299999999, "longitude": 77.7749302}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left"}, "localizedValues": {"distance": {"text": "0.5 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 330, "staticDuration": "76s", "polyline": {"encodedPolyline": "mu~lAiluyMJ]p@mB@AXcAj@eBDQx@qBd@cANWFI"}, "startLocation": {"latLng": {"latitude": 12.777988299999999, "longitude": 77.7749302}}, "endLocation": {"latLng": {"latitude": 12.7766925, "longitude": 77.77766280000002}}, "navigationInstruction": {"maneuver": "MERGE", "instructions": "Merge onto NH 4/NH 44/NH 48\nToll road\nDestination will be on the left"}, "localizedValues": {"distance": {"text": "0.3 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "21.3 km"}, "duration": {"text": "38 mins"}, "staticDuration": {"text": "38 mins"}}}, {"distanceMeters": 55061, "duration": "3555s", "staticDuration": "3555s", "polyline": {"encodedPolyline": "im~lAk}uyMFKt@aBxC_FPWt@yAx@{AHObCqEpD{HzAqDtAoCf@{@Ra@HQxAaClBiCLOb@g@RUlAkAhA{@XUx@i@h@Yr@]`Bo@fBaApGqDhAu@\\[^Uv@m@f@_@dAeATW|ByC?[b@m@`@i@TYX_@NSbHwIbB{BnH}IfEoElAkA^?hCkCNOzAeBnAgA|AeAtBmAfAo@jBgAtA_AZWv@q@|AqA`@]nDwCpCaCRQnBsAfEqDfAmAl@u@z@qA|BaEh@aAp@_Bz@iCnB{H~EmRV_A\\qAVw@j@uAhBgDh@iAn@gAp@y@nAoAt@q@VUxAkAxJcHnA{@zAgA\\UROp@g@hAw@n@e@\\Wb@_@TSb@a@RSpA_B~BcDfA{AVa@Zi@\\o@f@mAXo@ZeAf@uBX_BJg@fAyFJe@nAwGH]ZqAb@eBjCyI~@cEJe@VkAR{@R{@Ry@z@uCx@uCZ_A\\aA^{@P_@v@wAPYPYn@aAx@gAHKJO`FsGhBiCj@_AZm@v@kBZ{@Vu@ZkARaAH[Hk@PkARqBFcADeADaAHwARiBt@mDxAoHf@_DDa@R{Bv@}PF_AP}ATqAfAaGFYXsBNgAJeAHkA`@gEVuCD_@Dc@D_@PsBLyAFs@D_@L{AHo@^_DD_@Jg@XmAx@qCx@qBn@oArAwBzA{BbBgCdBeCvFqIT]f@y@|@uAf@w@b@s@j@aALWh@mAJUXs@Lc@x@{CJg@RiAxAkIFYLq@xBuLNw@|CiQNw@n@eDFYH]VkAd@_B\\}@Ti@bAoB|BcE`@q@`@q@r@aAb@e@l@k@bB_Bn@k@pA_Bl@y@l@sA`@}ADWFc@Hu@DeABy@E_ACUKcASeAu@uDSiBAsBJmBz@kFv@eDb@}Aj@eBt@sBnAuC~@eBrBeDdAyA`ByB~AeBXWZW|F_GrAuAbEcEjB{B|AqBjA}AvAeB~CeET[z@qAZq@rBiDh@o@f@k@|B{BbAmAv@kAf@cAtAsCZi@v@kAhCmDhB{Bn@}@zBaC~CaCdF}C~@m@pA{@bBqAn@o@z@{@vBqCbB}BzB}Cj@u@dF_HVc@t@mA^u@Z}@b@_Bt@eDhAoEJa@|AiGdAeDj@}BbBgFt@sC~AgGdA{Db@kBpAuGh@}Cb@}BDQ~@gEb@iBPo@d@oBf@mBLc@b@eBbC}Iz@{C|@wCr@aCp@mCt@{Cj@eCt@yCTcA`@_CBWDk@?oAC}@g@wGCmBH_CRkCT{CLkBD_@HyA?MJyEr@iFPqAPiABQXkBpBcNfAyHDYFk@PoABKPkDNuCN}BPwAZ_B`@eBz@sBd@eAxA_C~@eAhCyBz@u@j@i@hAw@`BoAnAkAz@gA`AgBxA}CtAaD|AwD`@cAZo@r@sBLi@PwABgCGkIEeCQuKCmBEaE@_@?SB}@L{AF[RaAZsAdAqEnAsFnAqFLg@|AuF`@_BjBcHNe@d@iBdCqIVgAn@}DPeBDa@b@eEh@_GDc@Fc@D[^aDj@qF\\cDr@aHHw@^kCZuAv@mC\\_AtAkEV{@jAmD|@sChBaGFURi@|AkE|AsEdAqDV{BNqCVqCLgA^uBPm@dAaC|@oBz@}ANYRc@d@{@b@cAzCqF`D}GpAoCp@gAf@q@z@_ApEcFr@iAhAuBlD_H~A}CdBgDzA{Cf@wA~AoFbAyDj@mBdD_MNk@t@iCh@gB|@_DT{@tAcFPi@|@iDt@mCXcAh@yCt@uCNi@Vu@bAqBhB_Dp@gBXgAbAwDlAoHp@qELw@^yBb@mCV}AlAuHv@sFd@kCnAyHTuA\\wAJa@b@oAN]bAyBhEgJx@iBzAyCjC{Fb@yANUVm@|BqFHOl@uAzBcFn@aBf@uBZwBp@sGLwA`AuJ@ILaAJ]Li@^gAx@aBDGbBmB`@a@b@WfAm@xCmAjFiBpDgBtB{AvAkA`DqC\\[fB_BXWlEcEhAkAvA_BfEeF|@{@~AoAxN_JdAo@fAo@hBy@\\MpBo@~NoD~Ag@RIl@_@|@u@rAsA`CqC~@mAv@cAd@k@T[d@_@hCaDfAwANSrCeDx@eAhH{IvD{E|EuFj@y@^q@Vk@\\{@Ri@Z{@Pc@zAwDJW~AyEdC{GhAoC`B}Dl@}AL]t@{B\\qARiATiBTsB^{EDy@RqC?c@?aBAe@MkBMsD@yA?CLoF?_@EaBWaCs@_EQyAGq@MmBAmC@y@@kC@i@LaBVmBd@oCJs@^_CTmAnBcLn@gC\\aAVm@FKf@y@tCoDpA}Ad@k@PUZi@j@iAFSbDgIRi@J]r@iB^cBN{@DUPiAr@oHToBLcAJs@TaAfBgFr@kBNa@jAmDJKL]v@uBPg@p@cB|@iCp@}AbAcBhBeB`@YbAk@zAs@hKkDhCeAtA}@rAoAf@k@R[pE_G^g@xBqCvAiBp@_Av@aA^c@l@}@`@w@L[l@sB\\_Bf@iDDW@k@D_@Fa@H]Rs@t@kBb@o@fAsAVYh@i@n@q@bEkEh@s@\\k@Vk@Xu@\\oAf@gDNy@`@_B\\yAj@iBr@kCL]Ps@dBsFr@aChB}FdBsFDKL[N_@Vc@fCqDfDiEX_@n@w@fCaDlCqDz@kAx@wARe@hDyGn@uA~A_DFOXm@^q@Va@r@gAFI~@cApAiA~DeDz@e@t@o@`GsELKv@u@HI|@mApCaFh@s@xA_Br@g@dDgCpCcB`DuBJIb@Yf@_@hAq@z@q@hA}@f@g@VYlAuA@?tBmCj@u@rA}A\\a@hAmAhAaAz@w@dC{BtAwAT[LM|H}IhBuBhEaFh@k@tAqADCfAw@nEwC~@g@dA}@p@k@z@{@|DyDtDuD`AmAn@_Al@iAZq@j@wARq@lByFxBiGH]xAeEXy@t@yAx@sA~BsCLMzAiBlB{BxBmC~@kAlAqAzBiBd@UnAw@ZOb@UnAo@vAi@JEnA]bAS"}, "startLocation": {"latLng": {"latitude": 12.7766925, "longitude": 77.77766280000002}}, "endLocation": {"latLng": {"latitude": 12.5429478, "longitude": 78.202068}}, "steps": [{"distanceMeters": 2090, "staticDuration": "213s", "polyline": {"encodedPolyline": "im~lAk}uyMFKt@aBxC_FPWt@yAx@{AHObCqEpD{HzAqDtAoCf@{@Ra@HQxAaClBiCLOb@g@RUlAkAhA{@XUx@i@h@Yr@]`Bo@fBaApGqDhAu@\\[^Uv@m@f@_@dAeATW|ByC"}, "startLocation": {"latLng": {"latitude": 12.7766925, "longitude": 77.77766280000002}}, "endLocation": {"latLng": {"latitude": 12.7644922, "longitude": 77.7918558}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head southeast on NH 4/NH 44/NH 48\nPass by Oneness Travels - Attibel<PERSON> Pickup (on the left)"}, "localizedValues": {"distance": {"text": "2.1 km"}, "staticDuration": {"text": "4 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 16, "staticDuration": "3s", "polyline": {"encodedPolyline": "aa|lAcvxyM?["}, "startLocation": {"latLng": {"latitude": 12.7644922, "longitude": 77.7918558}}, "endLocation": {"latLng": {"latitude": 12.764494299999999, "longitude": 77.7920019}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left"}, "localizedValues": {"distance": {"text": "16 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 452, "staticDuration": "75s", "polyline": {"encodedPolyline": "aa|lA_wxyMb@m@`@i@TYX_@NSbHwIbB{B"}, "startLocation": {"latLng": {"latitude": 12.764494299999999, "longitude": 77.7920019}}, "endLocation": {"latLng": {"latitude": 12.7618563, "longitude": 77.7951729}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at MRF TYRES WHEEL ALGN BALANCE\nPass by TamilNadu RTO Check Post (on the left)"}, "localizedValues": {"distance": {"text": "0.5 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 472, "staticDuration": "55s", "polyline": {"encodedPolyline": "sp{lAyjyyMnH}IfEoElAkA"}, "startLocation": {"latLng": {"latitude": 12.7618563, "longitude": 77.7951729}}, "endLocation": {"latLng": {"latitude": 12.7589486, "longitude": 77.7983423}}, "navigationInstruction": {"maneuver": "STRAIGHT", "instructions": "Continue straight"}, "localizedValues": {"distance": {"text": "0.5 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 18, "staticDuration": "2s", "polyline": {"encodedPolyline": "m~zlAs~yyM^?"}, "startLocation": {"latLng": {"latitude": 12.7589486, "longitude": 77.7983423}}, "endLocation": {"latLng": {"latitude": 12.7587856, "longitude": 77.798345}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right toward NH 4/NH 44/NH 48"}, "localizedValues": {"distance": {"text": "18 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 52013, "staticDuration": "3207s", "polyline": {"encodedPolyline": "m}zlAs~yyMhCkCNOzAeBnAgA|AeAtBmAfAo@jBgAtA_AZWv@q@|AqA`@]nDwCpCaCRQnBsAfEqDfAmAl@u@z@qA|BaEh@aAp@_Bz@iCnB{H~EmRV_A\\qAVw@j@uAhBgDh@iAn@gAp@y@nAoAt@q@VUxAkAxJcHnA{@zAgA\\UROp@g@hAw@n@e@\\Wb@_@TSb@a@RSpA_B~BcDfA{AVa@Zi@\\o@f@mAXo@ZeAf@uBX_BJg@fAyFJe@nAwGH]ZqAb@eBjCyI~@cEJe@VkAR{@R{@Ry@z@uCx@uCZ_A\\aA^{@P_@v@wAPYPYn@aAx@gAHKJO`FsGhBiCj@_AZm@v@kBZ{@Vu@ZkARaAH[Hk@PkARqBFcADeADaAHwARiBt@mDxAoHf@_DDa@R{Bv@}PF_AP}ATqAfAaGFYXsBNgAJeAHkA`@gEVuCD_@Dc@D_@PsBLyAFs@D_@L{AHo@^_DD_@Jg@XmAx@qCx@qBn@oArAwBzA{BbBgCdBeCvFqIT]f@y@|@uAf@w@b@s@j@aALWh@mAJUXs@Lc@x@{CJg@RiAxAkIFYLq@xBuLNw@|CiQNw@n@eDFYH]VkAd@_B\\}@Ti@bAoB|BcE`@q@`@q@r@aAb@e@l@k@bB_Bn@k@pA_Bl@y@l@sA`@}ADWFc@Hu@DeABy@E_ACUKcASeAu@uDSiBAsBJmBz@kFv@eDb@}Aj@eBt@sBnAuC~@eBrBeDdAyA`ByB~AeBXWZW|F_GrAuAbEcEjB{B|AqBjA}AvAeB~CeET[z@qAZq@rBiDh@o@f@k@|B{BbAmAv@kAf@cAtAsCZi@v@kAhCmDhB{Bn@}@zBaC~CaCdF}C~@m@pA{@bBqAn@o@z@{@vBqCbB}BzB}Cj@u@dF_HVc@t@mA^u@Z}@b@_Bt@eDhAoEJa@|AiGdAeDj@}BbBgFt@sC~AgGdA{Db@kBpAuGh@}Cb@}BDQ~@gEb@iBPo@d@oBf@mBLc@b@eBbC}Iz@{C|@wCr@aCp@mCt@{Cj@eCt@yCTcA`@_CBWDk@?oAC}@g@wGCmBH_CRkCT{CLkBD_@HyA?MJyEr@iFPqAPiABQXkBpBcNfAyHDYFk@PoABKPkDNuCN}BPwAZ_B`@eBz@sBd@eAxA_C~@eAhCyBz@u@j@i@hAw@`BoAnAkAz@gA`AgBxA}CtAaD|AwD`@cAZo@r@sBLi@PwABgCGkIEeCQuKCmBEaE@_@?SB}@L{AF[RaAZsAdAqEnAsFnAqFLg@|AuF`@_BjBcHNe@d@iBdCqIVgAn@}DPeBDa@b@eEh@_GDc@Fc@D[^aDj@qF\\cDr@aHHw@^kCZuAv@mC\\_AtAkEV{@jAmD|@sChBaGFURi@|AkE|AsEdAqDV{BNqCVqCLgA^uBPm@dAaC|@oBz@}ANYRc@d@{@b@cAzCqF`D}GpAoCp@gAf@q@z@_ApEcFr@iAhAuBlD_H~A}CdBgDzA{Cf@wA~AoFbAyDj@mBdD_MNk@t@iCh@gB|@_DT{@tAcFPi@|@iDt@mCXcAh@yCt@uCNi@Vu@bAqBhB_Dp@gBXgAbAwDlAoHp@qELw@^yBb@mCV}AlAuHv@sFd@kCnAyHTuA\\wAJa@b@oAN]bAyBhEgJx@iBzAyCjC{Fb@yANUVm@|BqFHOl@uAzBcFn@aBf@uBZwBp@sGLwA`AuJ@ILaAJ]Li@^gAx@aBDGbBmB`@a@b@WfAm@xCmAjFiBpDgBtB{AvAkA`DqC\\[fB_BXWlEcEhAkAvA_BfEeF|@{@~AoAxN_JdAo@fAo@hBy@\\MpBo@~NoD~Ag@RIl@_@|@u@rAsA`CqC~@mAv@cAd@k@T[d@_@hCaDfAwANSrCeDx@eAhH{IvD{E|EuFj@y@^q@Vk@\\{@Ri@Z{@Pc@zAwDJW~AyEdC{GhAoC`B}Dl@}AL]t@{B\\qARiATiBTsB^{EDy@RqC?c@?aBAe@MkBMsD@yA?CLoF?_@EaBWaCs@_EQyAGq@MmBAmC@y@@kC@i@LaBVmBd@oCJs@^_CTmAnBcLn@gC\\aAVm@FKf@y@tCoDpA}Ad@k@PUZi@j@iAFSbDgIRi@J]r@iB^cBN{@DUPiAr@oHToBLcAJs@TaAfBgFr@kBNa@jAmDJKL]v@uBPg@p@cB|@iCp@}AbAcBhBeB`@YbAk@zAs@hKkDhCeAtA}@rAoAf@k@R[pE_G^g@xBqCvAiBp@_Av@aA^c@l@}@`@w@L[l@sB\\_Bf@iDDW@k@D_@Fa@H]Rs@t@kBb@o@fAsAVYh@i@n@q@bEkEh@s@\\k@Vk@Xu@\\oAf@gDNy@`@_B\\yAj@iBr@kCL]Ps@dBsFr@aChB}FdBsFDKL[N_@Vc@fCqDfDiEX_@n@w@fCaDlCqDz@kAx@wARe@hDyGn@uA~A_DFOXm@^q@Va@r@gAFI~@cApAiA~DeDz@e@t@o@`GsELKv@u@HI|@mApCaFh@s@xA_Br@g@dDgCpCcB`DuBJIb@Yf@_@hAq@z@q@hA}@f@g@VYlAuA@?tBmCj@u@rA}A\\a@hAmAhAaAz@w@dC{BtAwAT[LM|H}IhBuBhEaFh@k@tAqADCfAw@nEwC~@g@dA}@p@k@z@{@|DyDtDuD`AmAn@_Al@iAZq@j@wARq@lByFxBiGH]xAeEXy@t@yAx@sA~BsCLMzAiBlB{BxBmC~@kAlAqAzBiBd@UnAw@ZOb@UnAo@vAi@JEnA]bAS"}, "startLocation": {"latLng": {"latitude": 12.7587856, "longitude": 77.798345}}, "endLocation": {"latLng": {"latitude": 12.5429478, "longitude": 78.202068}}, "navigationInstruction": {"maneuver": "MERGE", "instructions": "Merge onto NH 4/NH 44/NH 48\nToll road\nPass by the lake (on the right in 2.7 km)\nDestination will be on the left"}, "localizedValues": {"distance": {"text": "52.0 km"}, "staticDuration": {"text": "53 mins"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "55.1 km"}, "duration": {"text": "59 mins"}, "staticDuration": {"text": "59 mins"}}}, {"distanceMeters": 63623, "duration": "3516s", "staticDuration": "3516s", "polyline": {"encodedPolyline": "mxpkA}yh|Mt@MzBa@b@Gp[yFt@SjQ{C`B]tA[tA]rDcA`HyBpCeAr@Y~BaAtFgCr@_@pBcAhHeEpHaFxCoBzG_F`Ao@h@a@tDkCZUtKsHtGsEfEsCBCbJkGZU|HqFxAcAvEcDtOwKfAu@r@c@h@W`Ae@`Cy@x@UhCc@t@KvAMtCKt@?z@@fEPL@J@|@Ll@Ln@N|A^n@NTFvDbALDB?vBp@rEbBh@\\`FvBvB`A^P`IjDfAb@zBz@zCx@~@NTDjEb@r@FRBp@Hr@FzALjBPF@zEb@dCTn@Hb@DdAJrAPhAPpB^tCt@zEjBnBx@pC~@|Bh@`AL|Hv@|ANpCTfA@xZ|C~@HzD`@|C<PERSON>d<PERSON><PERSON>@dENfBD\\@bA?xA@jAAlLW~KWRA`AGxASx@OpEmAfBa@hBKrEi@pBSpAO^OxDc@zAUbCe@rCu@~CiA`EgB|CsA`Ac@RGbBk@f@Kl@KlAMnBGrCHN@v@FnKl@D?pCBfCGrBOvBWxCm@jCy@rCiAdDgBFEd@WzCaBvHeE|Au@nCiAbGwBrBo@rNmEZKjGmBfJoChK}CnEuAhD}@x@KtBMnB@nAHv@Jb@Fz@JZDbJrAd@FtB\\lRnCRBzB\\jL`Bv@JvCZdSrAjN~@zARfAVr@TLDrAj@|A|@n@h@dA~@zG|GLLr@r@tCxCJTfAjAnCdCf@`@r@b@p@XNFl@Rx@Pl@J|@FbABhACvAOHClD[|@Q`LkAfCOrD_@HAlDSjEUdDUzEk@nDe@bAK\\Al@AvAAv@DvANnBZ~B`@vB^lJpApCZdD^zARhALrLr@b@BjKn@fBNpIbBdCf@lE|@fDt@lCj@rG`B|Bd@|AThCRxCNzDR~CNjBJjAHfE^xALhPnAZDdE\\pBLn@@zB@`BC|CG`Mu@|EU`Dc@pASnEq@~GcA^E`CUxCIzIOjM]hEEzCCnDCdDDnDPfBLf@Db@DrFn@lGx@tAXhHvApFnAbAZbAf@fAl@hHlEr@b@jClAlNhE|E|AzBz@bDtA|IxDxErBp@VnBl@j@LhANlCLjB?pGGpDGtCEfAClDQ`Ko@z@E|Hi@`Im@xEi@lF_@J?`CKrB?t@BvAJ|ATZFZFdFfAxGzAdDr@zBh@rA^p@Xl@ZRJ`Ar@zBfBhA`ApCvBbG~Fh@f@fB`B`Az@fAbArD|DnEpF`@l@FFVR|@f@b@ZVNb@PtA`@VFvATbBLf@BbP|@t@DvCVbDXtALtDj@`C`@hBXjC\\vCTrLz@|Ij@hJp@tBR`DVrFd@lEPvBBV@n@?^ApDCnJEdACnAG|AKpMqAlCWpHw@dNyApAOl@KPC`ASnBk@zAk@v@]fAo@~@k@lFiD|@e@~@_@v@UZIxB]pAKrAA~ABjBPpATbA\\tB|@TJv@d@nAbAp@r@b@h@xDzFnAlBvBpCPTt@t@FFv@f@LHnAj@hAZbAND@`@B@?jADtAAbJY`@AfDGn@?jD@rGFhEFzDAd@@rAB~B@bBB`@?jBBb@?|KOv@?`EN|@J~ARrCl@D?rA^jA`@r@Xb@F~CdBjBrAbDnCnAdAfAr@d@XvAj@r@RZF`@Hr@HX@nADlACfG[tHc@rAAnBBxBPrAPtGpBvAf@dJjCrAZdF~@|Dj@hGt@vAXrA\\|@`@pB|@`EjB|@b@r@NtCfAz@VbAZnAd@h@Tt@^zAbAxAnApEzDHHr@j@|BbBlFrDj@`@VNxBnA|At@tBv@fBf@lAXPFvAVf@DxAPL@~CR`C@d@?hBAdENdBRfBd@f@RlB|@nDlBd@XjAl@xDlBhI|D^NfDrA~Bt@nI`CnBp@fAh@dAj@x@j@jAz@`ExCnEnDd@\\zDlCp@`@vAx@v@d@zIhEvDvA~Ah@`JhChEjALBfGhBh@Rn@Zj@Zj@Xb@T`At@d@d@t@x@bPjTd@n@jCrDvBnCx@dAzBxCpMhQbAnAn@x@l@t@rCdDjDdDXV|AtAf@d@~DnDzDnDd@`@vArAhHpGhFtEtLjKf@b@TTxBjBhC`Cz@t@VTRRLHVVh@d@vKzJxApAhA|@bAz@`BjAhCbBzBlALDhCnAp@Z^P`DnAfF`B`AVjAXbDv@zLpCfCn@vD|@jEbAzKfCtEdAtFpAzA\\pInBzA\\tBd@RFnAXlCn@tGxAdFlAfAVj@NzAZlH`B|FtAjAX^HZN`Cf@dBZrCp@rCp@VF~M|CxDz@jCv@zEdB\\LdEtBvAp@rBhAlBnA~GhFhG~E~DbDfBvAhDpCxGtFHF`HvFzJfI`@\\tDzCPNhCjBbH|FTZtAdAtEtCfB`ArFhCdBn@b@NdHzB`Cj@jDn@|HhA~MfBzInApEf@bIr@fJv@zBNrGn@jBPvFf@~VvBt@FjR~Ar@Fr@FzGl@hUpBtGl@xFd@b@D`AHlE`@N@pHl@dBNfThB`K|@f@FXF`@F|ARzIxArAVbCj@bDz@fCr@tA^l@LfDn@hARfCf@nARxKhBzAThFz@tHtAJB|KpBdFt@fALbEd@`ALrDd@rDf@pARj@HfBV~AV`@HTDl@RlBt@TLfAn@d@\\ZTz@r@xAnAlCrB~@p@pBlApCfBINaF}CgC_BM^?Nk@hA"}, "startLocation": {"latLng": {"latitude": 12.5429478, "longitude": 78.202068}}, "endLocation": {"latLng": {"latitude": 12.024792600000001, "longitude": 78.0981455}}, "steps": [{"distanceMeters": 63293, "staticDuration": "3454s", "polyline": {"encodedPolyline": "mxpkA}yh|Mt@MzBa@b@Gp[yFt@SjQ{C`B]tA[tA]rDcA`HyBpCeAr@Y~BaAtFgCr@_@pBcAhHeEpHaFxCoBzG_F`Ao@h@a@tDkCZUtKsHtGsEfEsCBCbJkGZU|HqFxAcAvEcDtOwKfAu@r@c@h@W`Ae@`Cy@x@UhCc@t@KvAMtCKt@?z@@fEPL@J@|@Ll@Ln@N|A^n@NTFvDbALDB?vBp@rEbBh@\\`FvBvB`A^P`IjDfAb@zBz@zCx@~@NTDjEb@r@FRBp@Hr@FzALjBPF@zEb@dCTn@Hb@DdAJrAPhAPpB^tCt@zEjBnBx@pC~@|Bh@`AL|Hv@|ANpCTfA@xZ|C~@HzD`@|C<PERSON>d<PERSON><PERSON>@dENfBD\\@bA?xA@jAAlLW~KWRA`AGxASx@OpEmAfBa@hBKrEi@pBSpAO^OxDc@zAUbCe@rCu@~CiA`EgB|CsA`Ac@RGbBk@f@Kl@KlAMnBGrCHN@v@FnKl@D?pCBfCGrBOvBWxCm@jCy@rCiAdDgBFEd@WzCaBvHeE|Au@nCiAbGwBrBo@rNmEZKjGmBfJoChK}CnEuAhD}@x@KtBMnB@nAHv@Jb@Fz@JZDbJrAd@FtB\\lRnCRBzB\\jL`Bv@JvCZdSrAjN~@zARfAVr@TLDrAj@|A|@n@h@dA~@zG|GLLr@r@tCxCJTfAjAnCdCf@`@r@b@p@XNFl@Rx@Pl@J|@FbABhACvAOHClD[|@Q`LkAfCOrD_@HAlDSjEUdDUzEk@nDe@bAK\\Al@AvAAv@DvANnBZ~B`@vB^lJpApCZdD^zARhALrLr@b@BjKn@fBNpIbBdCf@lE|@fDt@lCj@rG`B|Bd@|AThCRxCNzDR~CNjBJjAHfE^xALhPnAZDdE\\pBLn@@zB@`BC|CG`Mu@|EU`Dc@pASnEq@~GcA^E`CUxCIzIOjM]hEEzCCnDCdDDnDPfBLf@Db@DrFn@lGx@tAXhHvApFnAbAZbAf@fAl@hHlEr@b@jClAlNhE|E|AzBz@bDtA|IxDxErBp@VnBl@j@LhANlCLjB?pGGpDGtCEfAClDQ`Ko@z@E|Hi@`Im@xEi@lF_@J?`CKrB?t@BvAJ|ATZFZFdFfAxGzAdDr@zBh@rA^p@Xl@ZRJ`Ar@zBfBhA`ApCvBbG~Fh@f@fB`B`Az@fAbArD|DnEpF`@l@FFVR|@f@b@ZVNb@PtA`@VFvATbBLf@BbP|@t@DvCVbDXtALtDj@`C`@hBXjC\\vCTrLz@|Ij@hJp@tBR`DVrFd@lEPvBBV@n@?^ApDCnJEdACnAG|AKpMqAlCWpHw@dNyApAOl@KPC`ASnBk@zAk@v@]fAo@~@k@lFiD|@e@~@_@v@UZIxB]pAKrAA~ABjBPpATbA\\tB|@TJv@d@nAbAp@r@b@h@xDzFnAlBvBpCPTt@t@FFv@f@LHnAj@hAZbAND@`@B@?jADtAAbJY`@AfDGn@?jD@rGFhEFzDAd@@rAB~B@bBB`@?jBBb@?|KOv@?`EN|@J~ARrCl@D?rA^jA`@r@Xb@F~CdBjBrAbDnCnAdAfAr@d@XvAj@r@RZF`@Hr@HX@nADlACfG[tHc@rAAnBBxBPrAPtGpBvAf@dJjCrAZdF~@|Dj@hGt@vAXrA\\|@`@pB|@`EjB|@b@r@NtCfAz@VbAZnAd@h@Tt@^zAbAxAnApEzDHHr@j@|BbBlFrDj@`@VNxBnA|At@tBv@fBf@lAXPFvAVf@DxAPL@~CR`C@d@?hBAdENdBRfBd@f@RlB|@nDlBd@XjAl@xDlBhI|D^NfDrA~Bt@nI`CnBp@fAh@dAj@x@j@jAz@`ExCnEnDd@\\zDlCp@`@vAx@v@d@zIhEvDvA~Ah@`JhChEjALBfGhBh@Rn@Zj@Zj@Xb@T`At@d@d@t@x@bPjTd@n@jCrDvBnCx@dAzBxCpMhQbAnAn@x@l@t@rCdDjDdDXV|AtAf@d@~DnDzDnDd@`@vArAhHpGhFtEtLjKf@b@TTxBjBhC`Cz@t@VTRRLHVVh@d@vKzJxApAhA|@bAz@`BjAhCbBzBlALDhCnAp@Z^P`DnAfF`B`AVjAXbDv@zLpCfCn@vD|@jEbAzKfCtEdAtFpAzA\\pInBzA\\tBd@RFnAXlCn@tGxAdFlAfAVj@NzAZlH`B|FtAjAX^HZN`Cf@dBZrCp@rCp@VF~M|CxDz@jCv@zEdB\\LdEtBvAp@rBhAlBnA~GhFhG~E~DbDfBvAhDpCxGtFHF`HvFzJfI`@\\tDzCPNhCjBbH|FTZtAdAtEtCfB`ArFhCdBn@b@NdHzB`Cj@jDn@|HhA~MfBzInApEf@bIr@fJv@zBNrGn@jBPvFf@~VvBt@FjR~Ar@Fr@FzGl@hUpBtGl@xFd@b@D`AHlE`@N@pHl@dBNfThB`K|@f@FXF`@F|ARzIxArAVbCj@bDz@fCr@tA^l@LfDn@hARfCf@nARxKhBzAThFz@tHtAJB|KpBdFt@fALbEd@`ALrDd@rDf@pARj@HfBV~AV`@HTDl@RlBt@TLfAn@d@\\ZTz@r@xAnAlCrB~@p@pBlApCfB"}, "startLocation": {"latLng": {"latitude": 12.5429478, "longitude": 78.202068}}, "endLocation": {"latLng": {"latitude": 12.0226358, "longitude": 78.0975713}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head south on NH 4/NH 44/NH 48 toward Service Road\nContinue to follow NH 44\nPass by Red Dhaba (on the left in 37.2 km)"}, "localizedValues": {"distance": {"text": "63.3 km"}, "staticDuration": {"text": "58 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 254, "staticDuration": "38s", "polyline": {"encodedPolyline": "odkhAylt{MINaF}CgC_B"}, "startLocation": {"latLng": {"latitude": 12.0226358, "longitude": 78.0975713}}, "endLocation": {"latLng": {"latitude": 12.0244986, "longitude": 78.0987602}}, "navigationInstruction": {"maneuver": "UTURN_RIGHT", "instructions": "Make a U-turn at Palayapudur..x.Road\nPass by GanashaTraders (on the right)"}, "localizedValues": {"distance": {"text": "0.3 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 28, "staticDuration": "10s", "polyline": {"encodedPolyline": "cpkhAgtt{MM^?N"}, "startLocation": {"latLng": {"latitude": 12.0244986, "longitude": 78.0987602}}, "endLocation": {"latLng": {"latitude": 12.0245661, "longitude": 78.0985226}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left at JOJO MOMOS - Dharmapuri NH"}, "localizedValues": {"distance": {"text": "28 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 48, "staticDuration": "14s", "polyline": {"encodedPolyline": "qpkhAwrt{Mk@hA"}, "startLocation": {"latLng": {"latitude": 12.0245661, "longitude": 78.0985226}}, "endLocation": {"latLng": {"latitude": 12.024792600000001, "longitude": 78.0981455}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_RIGHT", "instructions": "Slight right at the gas station\nDestination will be on the right"}, "localizedValues": {"distance": {"text": "48 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "63.6 km"}, "duration": {"text": "59 mins"}, "staticDuration": {"text": "59 mins"}}}, {"distanceMeters": 82548, "duration": "5399s", "staticDuration": "5399s", "polyline": {"encodedPolyline": "}qkhAmpt{Mj@iA?OL_@oCyBiCyB}@k@o@a@qBw@uBm@mOwBaFo@GD{@EgGm@mGaAeF_AyAYTiAjJ`B`JvAEZbCXtH~@rDf@B@lAPj@HfBV~AVv@Nl@RlBt@TLfAn@d@\\ZTtCbClCrB~@p@pBlApCfBfBhAfBvAv@dA`AvALRxFfKpBtEb@fA`A~Bv@zAp@bAd@h@j@d@bBjAxAv@NH~@f@ZR~AfAh@h@l@h@zAlB|AvBRV`DlEJJb@f@jCxCVXvAvAlAnAd@b@|@t@hBlAbD|AzAt@\\RfAp@\\VnBtAXRhDjCvAfAr@d@vIlEb@TpAl@nAp@bBfATRZVf@d@fAlAbDzErIhMdBrBdB`BdDrC`BhBvDjFLP`AdAzApAlBrAvHnFn@f@dBxAJHNJHHnExC|IvGv@h@zA|@|@`@~Ad@\\FtBb@xA\\TFbA^|@b@PJtFzDdA|@dApAfAfBrA~BpDjGdFtIx@lAz@`ApB`BtA`AxGtEf@\\xAz@jCfAbJ~CtChAnCfARH~@h@`@XpCnBfAf@v@Rb@HxARbCXt@JRBnBPr@HnBVvB`@z@Zr@f@|@dARXfA|Ar@p@HD^Np@Jv@@r@Mt@[b@]\\m@Tq@XwATq@NWhAgAj@[r@Qz@Ap@B|@Jv@\\hF~DhCnB|@`@x@Dt@GbEeAZEb@Ap@FF?f@Pf@`@V`@`@bApA`Cx@`AvBhBd@VNHvAl@hDz@VBdAHnACxAIpDUvEQbB?F?rBNRBP@hEd@rADx@ATAbAMr@KdAQdAQzF_AxBk@l@S`Bo@z@a@`@YhBiAtB{ApBsAh@_@PMj@]d@_@z@m@hJ}Gx@i@lBiAz@k@jAs@n@g@TY\\c@l@oAZaAHWBITiAD[T}BPwBL}ATqCN}AFq@RuCXwCT_BRcAXkAnBuFhAgCp@oANUx@_ApAoAxAcAhB}@vAg@l@Od@KhBYtAMt@CjDIlDO~DOzAIpCIhBBnAJzAXb@Nv@XnAj@xBfA|Az@~B`AhEdB|Q|GhC~@xBdAZPZPtEhCjB`AhB~@fCnAtAh@tA\\TDdBT`@Bz@B|@?z@C~@GlB[ZCjAOpFa@B?tBUvBa@rA_@x@[^OvFoCjB}@|CyA`Bm@nBg@fB[vHu@hCe@bAUfAYTGRGfC}@lB{@lCyAjBiAd@[tB}AzL{K`C{Bl@m@dBqB~FcHt@gAd@u@v@sAj@qAt@mAf@q@j@s@^a@\\_@dB{A`@]TKnEsCzAaA`@U|CgBPOFEd@i@PO?Az@u@xAwAdB}AtCsC`@a@|@gA|A}BfGkJ`AwAbAuAl@o@`BsAxA{@TIfBo@r@QNCdBUt@Ev@EjCCn@AN?v@?lFMpCStKwAx@Ql@QnEaB\\Kz@UTE`@Gd@ExAGfA@Z@dBPxAVdBb@v@Vh@RhAj@fBlAbDjCnIdHpExDnFxEbCxB~MvLVTbE~CrLbI|AfAtCpBPLf@\\NH`BfAbCfBHFvA~@tHdFdB~@XLlAd@rBl@dCb@nIlAlEv@hC`@nARbC^bC\\hDd@|@LfGx@h@FjCVfBJp@BN?pHC`CAJ?v@AxACpHJH?|@@l@B|DLR?pCA|EOnCGrAEV?lHPnADr@D`@D~BPbAHfBL`DThAHvCTpDd@zCn@nDdAxBt@~@ZjBn@nBl@bAV`FlAfHfBlAZlCr@jEfAbDp@j@JpGtAbBf@vAh@nC`AbA^v@Xp@TXJVHr@Rb@LlGdBvCz@`AR~A\\~Cj@`BXnEx@zAXzAVhB\\|AX~B^dDn@j@J|BVdG\\xEXnMv@bADbCApBEdPc@pEKtAEfKWzDIlBI`@A\\A`@?PAj@C|@CF?lAEl@EpGk@zC[dAGnB?r@DlAPzA^fA^`Cz@rDpAv@Xp@VfA`@fC|@xAf@^@RDhJrCnBj@LHB?RBl@JZJ|EdAx@Px@RvBd@nB^~AXND\\b@jLlC|Dt@vGjAdKbCxBh@rCn@vCv@zBh@xBd@pBTdB@b@CNA~@GN?zB]pLqBf@StEWt@?lA@vFPbENdEJ~BHbBDxAFpBFjGT`J\\lJ\\tABdBC~@I^M~@W`B{@\\SZUPO\\]X[bEsFn@cAXg@r@u@XYjB_Cb@i@|@}@TGxBcCvA{AtEyFpBkCpAwA`JwJl@m@V[p@s@hBkBrBoBbB_BnGgG~@{@l@o@rFqHlCqDrKcOz@mAvC}DtEmGl@y@bCaD\\c@pA_BLOzEaG~DaF~AqBt@gAl@gA`@aAJUfA{CxAeDjAyBhF_Kp@sABIz@mCTo@rAwClEmLdBwDvAqCh@cAFKrAyCVe@Vg@~D{HNWTc@fAuBd@}@`@y@Xk@Xk@\\m@hGiLtDoGXg@n@kAdC}E~DgIl@qApCkGXq@Xm@\\w@h@cAN]`@m@`AmAf@c@zByAbBy@\\QzC{AjCoAhGcD~C{A|CgB~@u@DCXWdAsAbAoBRk@BGf@sAd@mAHy@Rg@zByFd@kAFOTg@n@yAh@sA@CDUt@mB`@_A^{@LUj@y@bBwCtB_Eh@y@z@uADIlBiCr@Wb@]dCaBvA}@XUb@a@t@}@~BwDPYP[|AmCr@mA~AaCt@cAr@aAd@q@t@aAtC_EJKx@gAl@u@n@y@hBiBx@o@^WLIb@YFEz@c@|@c@zBw@z@Sd@M|B[^EbAGlAEhC@xENv@B|@DzDLtDL`BJvB^NBPDvBl@n@RrD`B`@V~@f@rBdAfAf@fBz@jFnCj@X`Bv@rIdEb@VbCnAZJb@^hBv@lBp@~A`@xBXz@D`A@n@Bx@BpDNP@N@R?fGXjMf@l@B|ETxLj@`CJrEPnAFjADbAD\\BnI\\tEPZ@R@~@BrADbBHtBNv@FtAH|AFNMh@@lDBfBEp@CVC`@CLC\\MZUR]Ha@@YGc@Sc@SQg@QWCM?o@Ja@TSTUf@Kn@I~AGhACh@El@?JAnAHr@Pj@T^`@`@\\RPFp@NL@v@Bh@?pB?lC@fMB\\?rI?nE?xEDnBFVBXBd@FzB\\bB^~Br@pAf@zC|Ax@h@r@h@rAhAt@r@\\`@rAdB|@vA|@`Bt@dBvGfQn@pAh@|@hAzATT~@|@z@l@bDdB~Al@tCv@d@LjQzE`AXnBh@rBf@`Bb@x@XvL`DhDbARHpAj@f@TTNTLb@Xr@f@dAz@dAdA`BxBdBhCx@|AdA|BTh@n@rAr@zAXd@JRh@v@\\b@h@n@tAlAvCrBx@j@fA`A`AhARVrBxCvAzBz@jAhFzHlBpC~@rAjAtA^b@hAjANLXV|B|BdAdAvCpChAfAl@n@vAfBbAzAlCbFLZNXd@hA`@jAV~@h@`Bn@tB^nA~@rDpArFDNzAxExArD`B|D`AvBTb@^t@l@~@^j@LR`BrB|@pAPVzBdE`A`CPb@Vp@HT~@`CVn@Rd@lB~EjAvCd@jAb@jAXv@ZdAp@vBlDlLdAhDh@dBTn@jBpFZbAzA|EfB~FL\\Tp@xCdJ~@pBf@~@HL\\l@pAjBdAlBlAhCf@nAV|@Pd@fC~HlA|CtB`FlBtE`CvF|B|FrC|HZh@\\l@RLZr@\\`AbBxETj@h@xAb@lAj@`Bf@vAbAlBz@vARj@Hd@\\l@hBrCJNh@`AnErHb@p@p@hAfD|FlAfBl@t@rFtGV\\hBxBbFlGhDbFtA~BtBpDNTrDnGxEvHnCpE`B|CVf@dB|DnAbD\\~@pDjJVj@f@vAl@bB`@tA~CtL\\hAJ`@XbAJb@J^XdAf@dBJb@l@`BHRBHL`@L^p@tBnAvDb@zAl@xBTt@b@`BZdAfAfDn@hBh@tAlBpEXn@|AtC^x@v@|AtAnClCbGd@`AVd@j@hALXLXbAnB^p@h@z@x@pA|FvJZl@~@~BFNDNhA`E|BbJ^|Av@|CRz@VnBFf@HbAH|@PdBb@dDJj@d@bBTr@|A|EzAhFb@jB^`BLh@\\|AjAxENn@r@xBz@~BvAdDj@pAz@xBTj@Nf@r@nBZ`ArBpGb@hA~@dCt@jBnCfHl@bBpCzHtA~Db@jAVp@`CxFDJDJv@fB~A|DbA`C`AzBfAfCx@|Bx@vCd@jC`@~DJdCBrB?l@?xA?h@?x@Ar@EvB@v@?J?HBz@LrATnAZz@v@|An@z@t@r@VRn@b@rAp@B@fCfATJ`DxAnBhAjAz@nAjA`@b@b@j@t@bADDjAxB`BnDjBvDj@jAf@~@lBbDj@dAjHpLb@x@~HtNh@dAz@dBVd@NVRXl@fAn@lAXh@dDfGR`@PXp@r@JV|@zCZlARn@~CjLh@fB~BbJv@vC|@fCz@rBtAlCXxANd@nEnJpAdCdAzBp@tAlBjDZf@`AvAFJv@Y"}, "startLocation": {"latLng": {"latitude": 12.024792600000001, "longitude": 78.0981455}}, "endLocation": {"latLng": {"latitude": 11.5144901, "longitude": 77.9260559}}, "steps": [{"distanceMeters": 48, "staticDuration": "13s", "polyline": {"encodedPolyline": "}qkhAmpt{Mj@iA"}, "startLocation": {"latLng": {"latitude": 12.024792600000001, "longitude": 78.0981455}}, "endLocation": {"latLng": {"latitude": 12.0245661, "longitude": 78.0985226}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head southeast"}, "localizedValues": {"distance": {"text": "48 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 28, "staticDuration": "11s", "polyline": {"encodedPolyline": "qpkhAwrt{M?OL_@"}, "startLocation": {"latLng": {"latitude": 12.0245661, "longitude": 78.0985226}}, "endLocation": {"latLng": {"latitude": 12.0244986, "longitude": 78.0987602}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_LEFT", "instructions": "Slight left at the gas station toward NH 44"}, "localizedValues": {"distance": {"text": "28 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 848, "staticDuration": "87s", "polyline": {"encodedPolyline": "cpkhAgtt{MoCyBiCyB}@k@o@a@qBw@uBm@mOwBaFo@"}, "startLocation": {"latLng": {"latitude": 12.0244986, "longitude": 78.0987602}}, "endLocation": {"latLng": {"latitude": 12.0313799, "longitude": 78.1017189}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left at JOJO MOMOS - Dharmapuri NH onto NH 44\nPass by Sri Kanaga Oil Mill (Cold Press oil) (on the left)"}, "localizedValues": {"distance": {"text": "0.8 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 527, "staticDuration": "43s", "polyline": {"encodedPolyline": "c{lhAwfu{MGD{@EgGm@mGaAeF_AyAY"}, "startLocation": {"latLng": {"latitude": 12.0313799, "longitude": 78.1017189}}, "endLocation": {"latLng": {"latitude": 12.035988699999999, "longitude": 78.1027319}}, "navigationInstruction": {"maneuver": "RAMP_LEFT", "instructions": "Take the exit\nPass by SURIYA HOUSE (on the left)"}, "localizedValues": {"distance": {"text": "0.5 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 41, "staticDuration": "18s", "polyline": {"encodedPolyline": "}wmhAamu{MTiA"}, "startLocation": {"latLng": {"latitude": 12.035988699999999, "longitude": 78.1027319}}, "endLocation": {"latLng": {"latitude": 12.0358793, "longitude": 78.10309670000001}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at ஸ்ரீ கிருஷ்ணா ஸ்வீட்ஸ் & பேக்கரி"}, "localizedValues": {"distance": {"text": "41 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 412, "staticDuration": "58s", "polyline": {"encodedPolyline": "gwmhAkou{MjJ`B`JvA"}, "startLocation": {"latLng": {"latitude": 12.0358793, "longitude": 78.10309670000001}}, "endLocation": {"latLng": {"latitude": 12.032287, "longitude": 78.1021671}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at யமுனா பாஸ்ட் புட்\nPass by PRAVEEN COMMUNICATIONS (on the left)"}, "localizedValues": {"distance": {"text": "0.4 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 16, "staticDuration": "2s", "polyline": {"encodedPolyline": "y`mhAqiu{MEZ"}, "startLocation": {"latLng": {"latitude": 12.032287, "longitude": 78.1021671}}, "endLocation": {"latLng": {"latitude": 12.032317299999999, "longitude": 78.10202749999999}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at Kaalmari Rice Godown onto Anjaneyar Koil Rd"}, "localizedValues": {"distance": {"text": "16 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 34162, "staticDuration": "2059s", "polyline": {"encodedPolyline": "_amhAuhu{MbCXtH~@rDf@B@lAPj@HfBV~AVv@Nl@RlBt@TLfAn@d@\\ZTtCbClCrB~@p@pBlApCfBfBhAfBvAv@dA`AvALRxFfKpBtEb@fA`A~Bv@zAp@bAd@h@j@d@bBjAxAv@NH~@f@ZR~AfAh@h@l@h@zAlB|AvBRV`DlEJJb@f@jCxCVXvAvAlAnAd@b@|@t@hBlAbD|AzAt@\\RfAp@\\VnBtAXRhDjCvAfAr@d@vIlEb@TpAl@nAp@bBfATRZVf@d@fAlAbDzErIhMdBrBdB`BdDrC`BhBvDjFLP`AdAzApAlBrAvHnFn@f@dBxAJHNJHHnExC|IvGv@h@zA|@|@`@~Ad@\\FtBb@xA\\TFbA^|@b@PJtFzDdA|@dApAfAfBrA~BpDjGdFtIx@lAz@`ApB`BtA`AxGtEf@\\xAz@jCfAbJ~CtChAnCfARH~@h@`@XpCnBfAf@v@Rb@HxARbCXt@JRBnBPr@HnBVvB`@z@Zr@f@|@dARXfA|Ar@p@HD^Np@Jv@@r@Mt@[b@]\\m@Tq@XwATq@NWhAgAj@[r@Qz@Ap@B|@Jv@\\hF~DhCnB|@`@x@Dt@GbEeAZEb@Ap@FF?f@Pf@`@V`@`@bApA`Cx@`AvBhBd@VNHvAl@hDz@VBdAHnACxAIpDUvEQbB?F?rBNRBP@hEd@rADx@ATAbAMr@KdAQdAQzF_AxBk@l@S`Bo@z@a@`@YhBiAtB{ApBsAh@_@PMj@]d@_@z@m@hJ}Gx@i@lBiAz@k@jAs@n@g@TY\\c@l@oAZaAHWBITiAD[T}BPwBL}ATqCN}AFq@RuCXwCT_BRcAXkAnBuFhAgCp@oANUx@_ApAoAxAcAhB}@vAg@l@Od@KhBYtAMt@CjDIlDO~DOzAIpCIhBBnAJzAXb@Nv@XnAj@xBfA|Az@~B`AhEdB|Q|GhC~@xBdAZPZPtEhCjB`AhB~@fCnAtAh@tA\\TDdBT`@Bz@B|@?z@C~@GlB[ZCjAOpFa@B?tBUvBa@rA_@x@[^OvFoCjB}@|CyA`Bm@nBg@fB[vHu@hCe@bAUfAYTGRGfC}@lB{@lCyAjBiAd@[tB}AzL{K`C{Bl@m@dBqB~FcHt@gAd@u@v@sAj@qAt@mAf@q@j@s@^a@\\_@dB{A`@]TKnEsCzAaA`@U|CgBPOFEd@i@PO?Az@u@xAwAdB}AtCsC`@a@|@gA|A}BfGkJ`AwAbAuAl@o@`BsAxA{@TIfBo@r@QNCdBUt@Ev@EjCCn@AN?v@?lFMpCStKwAx@Ql@QnEaB\\Kz@UTE`@Gd@ExAGfA@Z@dBPxAVdBb@v@Vh@RhAj@fBlAbDjCnIdHpExDnFxEbCxB~MvLVTbE~CrLbI|AfAtCpBPLf@\\NH`BfAbCfBHFvA~@tHdFdB~@XLlAd@rBl@dCb@nIlAlEv@hC`@nARbC^bC\\hDd@|@LfGx@h@FjCVfBJp@BN?pHC`CAJ?v@AxACpHJH?|@@l@B|DLR?pCA|EOnCGrAEV?lHPnADr@D`@D~BPbAHfBL`DThAHvCTpDd@zCn@nDdAxBt@~@ZjBn@nBl@bAV`FlAfHfBlAZlCr@jEfAbDp@j@JpGtAbBf@vAh@nC`AbA^v@Xp@TXJVHr@Rb@LlGdBvCz@`AR~A\\~Cj@`BXnEx@zAXzAVhB\\|AX~B^dDn@j@J|BVdG\\xEXnMv@bADbCApBEdPc@pEKtAEfKWzDIlBI`@A\\A`@?PAj@C|@CF?lAEl@EpGk@zC[dAGnB?r@DlAPzA^fA^`Cz@rDpAv@Xp@VfA`@fC|@xAf@"}, "startLocation": {"latLng": {"latitude": 12.032317299999999, "longitude": 78.10202749999999}}, "endLocation": {"latLng": {"latitude": 11.7753803, "longitude": 78.0559585}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left onto NH 44\nToll road\nPass by Kavi Kishore Tea Shop (on the left in 3.7 km)"}, "localizedValues": {"distance": {"text": "34.2 km"}, "staticDuration": {"text": "34 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 336, "staticDuration": "36s", "polyline": {"encodedPolyline": "c{zfAwhl{M^@RDhJrCnBj@LHB?RB"}, "startLocation": {"latLng": {"latitude": 11.7753803, "longitude": 78.0559585}}, "endLocation": {"latLng": {"latitude": 11.772558, "longitude": 78.05489349999999}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_LEFT", "instructions": "Slight left at Rajasthani Dhaba\nPass by S <PERSON>rill Labor Works (on the left)"}, "localizedValues": {"distance": {"text": "0.3 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 310, "staticDuration": "33s", "polyline": {"encodedPolyline": "oizfAabl{Ml@JZJ|EdAx@Px@RvBd@"}, "startLocation": {"latLng": {"latitude": 11.772558, "longitude": 78.05489349999999}}, "endLocation": {"latLng": {"latitude": 11.7699029, "longitude": 78.05404109999999}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Keep right"}, "localizedValues": {"distance": {"text": "0.3 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 129, "staticDuration": "14s", "polyline": {"encodedPolyline": "{xyfAw|k{MnB^~AXND"}, "startLocation": {"latLng": {"latitude": 11.7699029, "longitude": 78.05404109999999}}, "endLocation": {"latLng": {"latitude": 11.7687843, "longitude": 78.05372400000002}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "At Royal Looks Gents beauty parlour, continue onto NH 44 Service Road"}, "localizedValues": {"distance": {"text": "0.1 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 26, "staticDuration": "3s", "polyline": {"encodedPolyline": "{qyfAwzk{M\\b@"}, "startLocation": {"latLng": {"latitude": 11.7687843, "longitude": 78.05372400000002}}, "endLocation": {"latLng": {"latitude": 11.7686275, "longitude": 78.0535427}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_RIGHT", "instructions": "Slight right toward NH 44"}, "localizedValues": {"distance": {"text": "26 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 11603, "staticDuration": "815s", "polyline": {"encodedPolyline": "}pyfAsyk{MjLlC|Dt@vGjAdKbCxBh@rCn@vCv@zBh@xBd@pBTdB@b@CNA~@GN?zB]pLqBf@StEWt@?lA@vFPbENdEJ~BHbBDxAFpBFjGT`J\\lJ\\tABdBC~@I^M~@W`B{@\\SZUPO\\]X[bEsFn@cAXg@r@u@XYjB_Cb@i@|@}@TGxBcCvA{AtEyFpBkCpAwA`JwJl@m@V[p@s@hBkBrBoBbB_BnGgG~@{@l@o@rFqHlCqDrKcOz@mAvC}DtEmGl@y@bCaD\\c@pA_BLOzEaG~DaF~AqBt@gAl@gA`@aAJUfA{CxAeDjAyBhF_Kp@sABIz@mCTo@rAwClEmLdBwDvAqCh@cAFKrAyCVe@Vg@~D{HNWTc@fAuBd@}@`@y@Xk@Xk@\\m@hGiLtDoGXg@n@kAdC}E~DgIl@qApCkGXq@Xm@\\w@h@cAN]`@m@`AmAf@c@zByAbBy@\\QzC{AjCoAhGcD~C{A|CgB~@u@DCXWdAsAbAoBRk@BGf@sAd@mA"}, "startLocation": {"latLng": {"latitude": 11.7686275, "longitude": 78.0535427}}, "endLocation": {"latLng": {"latitude": 11.6938734, "longitude": 78.1100819}}, "navigationInstruction": {"maneuver": "MERGE", "instructions": "Merge onto NH 44\nToll road\nPass by Anvi Geospatial Pvt Ltd (on the left in 8 km)"}, "localizedValues": {"distance": {"text": "11.6 km"}, "staticDuration": {"text": "14 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 611, "staticDuration": "85s", "polyline": {"encodedPolyline": "u}jfA_{v{MHy@Rg@zByFd@kAFOTg@n@yAh@sA@CDUt@mB`@_A^{@LUj@y@"}, "startLocation": {"latLng": {"latitude": 11.6938734, "longitude": 78.1100819}}, "endLocation": {"latLng": {"latitude": 11.6913792, "longitude": 78.11505310000001}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_LEFT", "instructions": "Slight left\nPass by Tata Motors Commercial Vehicle Dealer - V S T Motors Pvt Ltd (on the left)"}, "localizedValues": {"distance": {"text": "0.6 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 422, "staticDuration": "56s", "polyline": {"encodedPolyline": "cnjfAazw{MbBwCtB_Eh@y@z@uADIlBiC"}, "startLocation": {"latLng": {"latitude": 11.6913792, "longitude": 78.11505310000001}}, "endLocation": {"latLng": {"latitude": 11.6892044, "longitude": 78.11822889999999}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Keep left\nPass by Balambiga Fancy Store (on the left)"}, "localizedValues": {"distance": {"text": "0.4 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 32, "staticDuration": "4s", "polyline": {"encodedPolyline": "o`jfA}mx{Mr@W"}, "startLocation": {"latLng": {"latitude": 11.6892044, "longitude": 78.11822889999999}}, "endLocation": {"latLng": {"latitude": 11.6889447, "longitude": 78.11835190000001}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_RIGHT", "instructions": "Slight right at KFC toward NH 44"}, "localizedValues": {"distance": {"text": "32 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 4442, "staticDuration": "332s", "polyline": {"encodedPolyline": "{~ifAunx{Mb@]dCaBvA}@XUb@a@t@}@~BwDPYP[|AmCr@mA~AaCt@cAr@aAd@q@t@aAtC_EJKx@gAl@u@n@y@hBiBx@o@^WLIb@YFEz@c@|@c@zBw@z@Sd@M|B[^EbAGlAEhC@xENv@B|@DzDLtDL`BJvB^NBPDvBl@n@RrD`B`@V~@f@rBdAfAf@fBz@jFnCj@X`Bv@rIdEb@VbCnAZJb@^hBv@lBp@~A`@xBXz@D`A@n@Bx@BpDNP@N@R?fGXjMf@l@B|ET"}, "startLocation": {"latLng": {"latitude": 11.6889447, "longitude": 78.11835190000001}}, "endLocation": {"latLng": {"latitude": 11.654597299999999, "longitude": 78.1213006}}, "navigationInstruction": {"maneuver": "MERGE", "instructions": "Merge onto NH 44\nPass by Salem By Pass (on the right in 1 km)"}, "localizedValues": {"distance": {"text": "4.4 km"}, "staticDuration": {"text": "6 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 1250, "staticDuration": "87s", "polyline": {"encodedPolyline": "ghcfAcay{MxLj@`CJrEPnAFjADbAD\\BnI\\tEPZ@R@~@BrADbBHtBNv@FtAH|AF"}, "startLocation": {"latLng": {"latitude": 11.654597299999999, "longitude": 78.1213006}}, "endLocation": {"latLng": {"latitude": 11.6434094, "longitude": 78.1202429}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Keep right to stay on NH 44\nPass by Sri Jyotirling Transport (on the right in 350m)"}, "localizedValues": {"distance": {"text": "1.2 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 27271, "staticDuration": "1634s", "polyline": {"encodedPolyline": "ibafAozx{MNMh@@lDBfBEp@CVC`@CLC\\MZUR]Ha@@YGc@Sc@SQg@QWCM?o@Ja@TSTUf@Kn@I~AGhACh@El@?JAn<PERSON>Hr@Pj@T^`@`@\\RPFp@NL@v@Bh@?pB?lC@fMB\\?rI?nE?xEDnBFVBXBd@FzB\\bB^~Br@pAf@zC|Ax@h@r@h@rAhAt@r@\\`@rAdB|@vA|@`Bt@dBvGfQn@pAh@|@hAzATT~@|@z@l@bDdB~Al@tCv@d@LjQzE`AXnBh@rBf@`Bb@x@XvL`DhDbARHpAj@f@TTNTLb@Xr@f@dAz@dAdA`BxBdBhCx@|AdA|BTh@n@rAr@zAXd@JRh@v@\\b@h@n@tAlAvCrBx@j@fA`A`AhARVrBxCvAzBz@jAhFzHlBpC~@rAjAtA^b@hAjANLXV|B|BdAdAvCpChAfAl@n@vAfBbAzAlCbFLZNXd@hA`@jAV~@h@`Bn@tB^nA~@rDpArFDNzAxExArD`B|D`AvBTb@^t@l@~@^j@LR`BrB|@pAPVzBdE`A`CPb@Vp@HT~@`CVn@Rd@lB~EjAvCd@jAb@jAXv@ZdAp@vBlDlLdAhDh@dBTn@jBpFZbAzA|EfB~FL\\Tp@xCdJ~@pBf@~@HL\\l@pAjBdAlBlAhCf@nAV|@Pd@fC~HlA|CtB`FlBtE`CvF|B|FrC|HZh@\\l@RLZr@\\`AbBxETj@h@xAb@lAj@`Bf@vAbAlBz@vARj@Hd@\\l@hBrCJNh@`AnErHb@p@p@hAfD|FlAfBl@t@rFtGV\\hBxBbFlGhDbFtA~BtBpDNTrDnGxEvHnCpE`B|CVf@dB|DnAbD\\~@pDjJVj@f@vAl@bB`@tA~CtL\\hAJ`@XbAJb@J^XdAf@dBJb@l@`BHRBHL`@L^p@tBnAvDb@zAl@xBTt@b@`BZdAfAfDn@hBh@tAlBpEXn@|AtC^x@v@|AtAnClCbGd@`AVd@j@hALXLXbAnB^p@h@z@x@pA|FvJZl@~@~BFNDNhA`E|BbJ^|Av@|CRz@VnBFf@HbAH|@PdBb@dDJj@d@bBTr@|A|EzAhFb@jB^`BLh@\\|AjAxENn@r@xBz@~BvAdDj@pAz@xBTj@Nf@r@nBZ`ArBpGb@hA~@dCt@jBnCfHl@bBpCzHtA~Db@jAVp@`CxFDJDJv@fB~A|DbA`C`AzBfAfCx@|Bx@vCd@jC`@~DJdCBrB?l@?xA?h@?x@Ar@EvB@v@?J?HBz@LrATnAZz@v@|An@z@t@r@VRn@b@rAp@B@fCfATJ`DxAnBhAjAz@nAjA`@b@b@j@t@bADDjAxB`BnDjBvDj@jAf@~@lBbDj@dAjHpLb@x@~HtNh@dAz@dBVd@NVRXl@fAn@lAXh@dDfGR`@PXp@r@JV|@zCZlARn@~CjLh@fB~BbJv@vC|@fCz@rBtAlCXxANd@nEnJpAdCdAzBp@tAlBjDZf@`AvAFJ"}, "startLocation": {"latLng": {"latitude": 11.6434094, "longitude": 78.1202429}}, "endLocation": {"latLng": {"latitude": 11.5147683, "longitude": 77.9259292}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_LEFT", "instructions": "Slight left at TVS FRIENDS ENGINEERING WORKS onto NH544\nPass by VIKAS TRANSPORT (on the left)"}, "localizedValues": {"distance": {"text": "27.3 km"}, "staticDuration": {"text": "27 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 34, "staticDuration": "9s", "polyline": {"encodedPolyline": "i~geAa|rzMv@Y"}, "startLocation": {"latLng": {"latitude": 11.5147683, "longitude": 77.9259292}}, "endLocation": {"latLng": {"latitude": 11.5144901, "longitude": 77.9260559}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left\nDestination will be on the right"}, "localizedValues": {"distance": {"text": "34 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "82.5 km"}, "duration": {"text": "1 hour 30 mins"}, "staticDuration": {"text": "1 hour 30 mins"}}}, {"distanceMeters": 114471, "duration": "7841s", "staticDuration": "7841s", "polyline": {"encodedPolyline": "q|geA{|rzMw@XbAvAfApAVZnA`BhA~A|@tAhCdEnArBTZNTzAtBxBzCxBnC~AtBrEvFpB`CjAlAv@t@nDxCDDZXpCrC`@`@fAhALLpIbJRT`@b@d@f@pArAh@h@t@l@|AnA|EvDVRpAdANJhErDhAjA\\b@|@nApAxBhCtEVd@n@nAdCvE`@t@nAzB~BlEVd@fAxBv@rBp@lBlAbDbA|CRr@ZlA`@|BT~BFtABt@?jAOlDEx@WvCSzCo@rKm@bIAPK`BKxAMdCQnDI|AQrBY`Eo@jJk@rIu@vKGn@GxAcAvPCl@AxBDxCf@~Hl@lI@F\\zD`@zELvAJxADf@HpAPdCHlAf@bGZjElAhQ\\xFDn@PbCNvBDv@L`B^bFXpCd@tCr@vCv@~BbA~B^t@fBhD@?\\f@HNdBfDfAjB^p@dBtCX^|BzChBvBr@v@xF`GpCjD~NrSt@bAlIhLf@r@tDbFVZnCdD|AdBv@x@xHxHRPxE|ErB|B~BzCfBdC\\j@fBxCbAtB|@hBpAzClC|FbB|Dn@jA`@f@p@~@h@n@fBfB`BpAdCxAf@Vz@ZpBp@vNnDtHfBVBNBvBd@pBj@~Bt@lBz@xChBZRXTvCxB|@p@nBzA`Az@jAtAdAzAVf@^t@Xp@`@bAVz@J`@DP`@`CPzBBfADpBJlBTlHBp@JjBFlBFnGBxFDhEBhEBvA?zB?~F?zL?XJtBNbBnAnITlC`@lK?~B?b@I|ACt@I~AGjB?nB@b@DlAPhC\\jFRnCJ|ABd@@x@F`GBhED|FDfGBbA@XBb@RjBZ`Bf@dBN`@N^h@jAd@x@|@nAbDxDzAvBfAjBv@|AZl@Zp@l@jB`@jBNvAFlA@`@AfAGnAARYnBe@jBm@zA]t@_ApBc@zASdAGZGv@Cd@Cj@@hAb@lHPzCBpA?bA?h@EpEA|FDbBFlATjB@LDTVrA^vAt@lBjAbCbApBx@`BN^v@nBX|@Lj@h@bDF`@x@tFF^Z|ALj@z@fDZhAv@~B\\bA\\pAXvATzB@RDpA@\\CtAAXCx@WxDEp@M`BU|CCPGr@APCd@EdACrBCzA@pA@xABxAHjELbCFn@JtABf@NzCXlFFpAJpEL~FTlIBfAJtA^zCJ`BT`CHdAHlAD|A@d@?fF@h@JxB@ZLnBHz@Fz@JxAVnDL~BDp@t@zKDxBGzG\\pAArAF|DPrF\\`DFf@H`@BJhArExAvDN^^f@fApBp@`AfAtArDpDxDrCt@j@LHVt@vC|Bf@`@RPpBpBj@v@b@l@PZz@bBb@`AVl@p@`Cz@jD\\`BR|@~@`E`@dBpBxIh@zBt@zC^|Ab@lBl@lCj@~BHZv@dD~A|GZpAHZb@bBBNdBlHdCpKNn@`@~B^rBPbBF`BItBIfAStDkAnSg@`KARGvASnDGj@Cn@GdC?p@Bx@NxABPVnAd@vALVx@pAj@p@z@x@NLn@b@NHRLr@\\bCn@x@Lp@H~@DhBC~@Gz@KtF{@fEq@lGaAjD[l@ElBKt@Cf@CnHa@zBKtB?\\?vADZBbADPBvBXfB\\n@NtA`@pAd@dBx@`B`AnAz@TRfB`B`BjBrAtBx@zA~@|BBHt@`Cn@tCb@xBnAjFvA|G`@fBJb@Vx@rArDfA|BdBjDp@zA|B~EnAjClB|DbG`M~@vADFJJPTFJPVNR`BjBxAtArAhAxAnAVR~DjD\\XjC|Bx@t@`BpAhCvB|AdAx@d@t@^nAf@b@L|Bt@|Bj@lARv@J@@|BPlCDhA?|@ArBKzBWjBYl@Or@QbBg@fBm@j@UlYeK`@M`Ba@rB_@tBUtBIhCAtBHVBx@Hp@Fz@Jr@N`@HtA\\n@R|@\\@?xAp@rDpBr@`@lB~@\\PpCzAvEbCfE`CNJXNnBdAhDlBvB|@hBh@zFrA~A`@~A`@z@PhKbChFjA|Bb@xARtBXvD\\nEVpFPnBPpB^nFlAdJrBxCh@TBzAXjBPzARb@Fp@JlEl@jALxAVzAXdB^vG|AvA^hEdA`AX`D|@fA`@v@^r@\\\\RhDvBr@d@jC`Cb@`@Z\\d@h@pAhBf@|@lAjCvAdEjAdEnD|LvAjFl@~Ah@dA\\n@`ApA~@dArAbAtA|@FDZNv@Zb@NdBf@VFrAT`BNfBHbIDp@@lLJ|GDnDDfCJXBbALTBdB\\fAZ`@LfBp@fB~@j@\\h@^h@`@|D`DdB`AzE~BnAp@bAh@lBrAtAlAlGxG\\^TZRVZb@|@xAJRd@|@bBpD`@v@fBxD|B|EJRt@tAtHbNj@dArEhI|@pAd@l@NP~@bArB|BpBzBpAvAbAjATVNN`BjBz@z@~E`FHHxAjBnAnBpEjJZl@dA`BrAbB|A~ARPrA`AhBhAzF~C`@Vx@d@DBjCxAxGrDfE~Bt@b@l@`@xBfBdBbBtA~ApDlFNRZf@Zb@vG|JlFbIjGpJz@|An@pABD^XPH?@z@zBz@hCx@nDVxAJv@Fd@JhALtB?V?@JP|AZb@Pp@V`AVnBXtBX@@hCj@fAT^B~@BjAFb@Bd@DH@jCVnCJdADt@Bh@@d@@l@?F?d@AVCVEDAHClA]dAYBAb@KfAUDAl@Ax@Fx@DVBl@Bx@FR@lBLx@Bv@Br@QXA@GFGNC@?NF@BBLAJv@nBr@dAr@r@tA`At@\\PHZL`ANB?zEb@`ADzAHZ@r@DFCHAHBDBDLAJCB\\l@Rb@^v@d@z@`AfBT`@lCxEBHFT@?F@DBBNABLN|@zA\\z@d@pBLn@FTjArEPn@|@xDb@`BZpA|A~FLb@h@hBPb@l@xAXl@Pb@fAbCf@dAr@fBJXb@jA|@`Cx@hCL^fBjFDHn@rBj@pBPx@Fb@Lv@z@hGPn@Lb@^pA`@nAlAfEbA~C~@vCjA|Cp@tAl@|@x@~@`A|@XX|@bA~O~Op@j@`AbA|@fANT@?H@BBHL?HCFA@rA`E|@rDBRV`AJf@FRJ^FN\\`A^v@RXpAnANNr@r@zBzCd@r@pAnCDHDDr@xAFxAfFnKdBvDf@xA\\dAFVb@vAPb@HTt@hB`DpFzAhCbAvBdAvCHV`@|ALh@d@fCl@xE`AnHBVTnBv@pFrAnHhAlGBFhD`S`BnJn@tFJt@PjAFTr@~BvA`Ed@`BvAbFbAjCb@hAP^Zl@zDzHpF`LJRtAdCxBdD|BvCVZjCzCb@`@z@z@xA|A~@jAdA`Bj@bAR`@z@tBp@zBd@pCZlCFlB@zBAf@MfCAXUlBQbAS~@Md@g@~AiAlCMVi@~@q@dAy@fAyArBw@zAEHy@vBc@bBKb@W~BIz@GtBD~BPbCNfAJl@FTf@lBr@jBz@~AfA~A`D~DbA~A~@hBt@rBr@dCf@rCJl@PdAp@xDj@~Bb@xAjD|Jz@~B~@dCv@xBpAxDPh@bB`GzAfGVhAJb@R`Ap@bDRnAd@zCtAzIh@bCl@hBPd@v@lBt@tAt@hAb@j@lBvBzEhEb@^hAbAf@j@VVx@dANVx@tAR\\P`@r@bB^jAt@jDjAxGJf@ZtAZdAL^N^p@|ATf@dAhBz@jAVZFHnArAtApA|BlBrAtA`@j@TZVd@R\\~CdGzEfJNZr@hB^nATz@\\bBZdCd@tGNnAh@vCf@fBFPt@jB`AhBzAzBDD`C~C|BnCzBjCh@j@pDrD~FtGnGxHnBhC|C~DhAzAlApBv@~APb@v@zBp@xCZzBJv@X|BX`BNv@x@xCt@xBTj@r@`BlAzB^r@rArBpBnCrGdJPTfEbGvArBn@dAnApChBlEfBjDN\\rCbGj@rAl@`Bx@`D\\hBRjBLlC@~AEdCOfFOdEQfFm@|T?VY~JGvAYzHI~ASxB]fBOn@c@rA{CpIM\\u@tBo@hBwH|SgBhFgB|Ek@nBi@`Ca@|AqAlGsAxGI\\I\\}A~HMj@[nBMfAc@tHAVMnBk@vKGnB[xQKdJCjAE|BGpFE|BElBMnFEv@In@ABWnA_@fAQ`@]n@gA~A[b@qFbH{ArBcC~DcFxIkCvEoA`CKPgBdDqAtBiBnDiAjCuA|DGTuAbFu@jDUnAIh@w@~FM|AI`AKlBEv@Cl@GrFBfDFfCLvBBb@\\nEZjDRjBZpB\\~Al@dBHTZr@b@v@tAlBtAtAzB~ANFJFlBx@r@TdAXtMtC|FpAx@TpAXb@H^JfAXfA\\`Ad@hAl@bAr@zAvA`@f@TZPVHJn@jAh@pA^rA\\`BPjAL`Al@zFr@vIr@zHh@tED`@v@|E~@jF`BrINv@jAfGlArGl@bDvArHz@zEn@jDbCvMN~@Fj@Hn@`BfUb@hHDl@Bn@DrBExCKvGUlLVd@C|@CfAElBAt@IrE@vA?p@DjAHbAL|Ap@dEx@dDb@tA|FvQTd@^j@N^HNUPuAd@ORGNCBMFOVE\\?j@Dp@B`@@dC@t@C~@EfAI`CCfAALGhCGlBKdEBdAFj@Hd@@DH^@D\\fARj@^z@h@tArAhDd@`AJTRd@n@dB@Bd@v@`@p@pAbBbAhA`BzA~@|@lAlABBt@n@h@b@VX`@b@`@d@DFvCjD~C~CBBxAlA`ChBhAz@z@p@n@f@HCD?LFBL@BADb@hANbA@FFn@@PHfB@b@D`ABhABrBAXIlAMdAAFa@bC[vBEXYzBGn@M|@c@zEMtBIfA]bCCJ?Da@`D]tA[lAyA|EcAtCk@tAc@~@g@nAiAzDSj@c@`AKLWZ[b@e@l@y@tAUp@WjAIbA?z@HfARfAXz@Zl@lAnAXVXVx@t@x@x@tB~Bl@z@`BlCFFnA`CXj@Tj@^t@rAjC@@BHKNA@{AdDy@bBGHWf@MVa@n@MNm@p@KH_BrAy@j@UNk@ZmAd@KBWHiCl@aAXa@LuBt@eBf@y@LW@s@EoAIOAi@?MAe@Cm@BaAF]@UBeD`@sC`@g@Fo@RWJURm@f@w@~@CDEHo@jCU~@kAvEg@rBc@lAYnAMd@CHWtA]zA}@`EOv@QjAEVWxBAPEzAEvCMx@q@pDkAnGGXIt@OpAEbEBbCJh@FN|@tAhA|A@F@NEj@If@a@zBKh@i@lCMl@UtACb@ClBBhCEp@G\\Or@}@tDYrB?@IdAAFIr@KbAEb@QtC?Z?bA?Z@vAArBKnDEv@ETY|@sAtCOXS`@Sd@sAvDq@nBs@pBSpAQtDA^QbBi@|FOv@O^_@v@eAzBUb@q@hAiAhBGLaAbB{BfDU`@U^[t@Sv@a@tC[nBEVi@fDOtBKbBi@vDWlBQrBc@zDKvAInBC^IdAKpBK|AARMnBAb@KpCKlBGdA]vGIpCAj@IzA?LStASfAOz@m@tCu@bEKt@O~A?RE~@Cv@IbCIvAG`AYzDQjBAZAXQvCShBMn@Id@_@v@Qb@Mf@El@@\\@d@D`BJnCC|@?r@C|AO~DO~B?FMlDATUvCSvBcAhHoB~J[xAeDbM_@fCALYhFMjAObBGjAI~@On@kA~Co@xBUp@aA~EAh@I`@Ut@_A`Dm@bC[tBI\\AJCZ?LEpAAf@GjAAJAj@?nCFhFG~AUbA?@kAvC[pCEj@KjBKhCEvAAr@G`ACh@ABOfAAFUdACb@AJC^An@?NAp@@PBdBEhBIr@e@`DQ~@i@nCI`@SbAIj@q@dHe@lDMr@SrAc@zCMz@o@nFW`CSbB_A~HSxBEx@Gv@E\\QzB{@|ICZ]bCIj@]lBo@|CgAbEW`BOv@Ib@]tACJKXWl@_@v@wAzCUd@M\\Qh@Or@I\\u@`FObCM~AG~@C\\Kt@CTEVCFe@pBMdAAFCTMnCAJEn@MhAO`AABYtA[~A_@nBGt@Qn@a@fAaAfBg@z@]d@YZGHo@z@cApBa@hASb@EHi@r@_AfBgAfBa@d@ERu@pAU`@u@nAm@dAOTMVw@tAOTsA~BcAdBOVKp@QtBGh@c@nBI`@SbAKn@o@xDYlB[xBCPYpCSzAMfAQtAM|@M~@WtBCJANIv@In@ARAJE`B?~@Dd@J~@@p@?ZIfA]lB]jBCB?JEd@E~@OzDAz@BVQTG^AxA?tABp@Dp@R~BRfBLdBDb@h@rGn@fICf@AVIZm@xBsArDaDlIeA`D_@bAENEXErBCnBS|Be@rDCRc@|CCPQjAG\\S|@UbAQ`AY|AIf@oAnHI\\[|@KX[r@}@nBy@|Au@`BWv@_AhD]`BSx@m@nB_AjCo@bC"}, "startLocation": {"latLng": {"latitude": 11.5144901, "longitude": 77.9260559}}, "endLocation": {"latLng": {"latitude": 11.2390528, "longitude": 77.0779834}}, "steps": [{"distanceMeters": 34, "staticDuration": "5s", "polyline": {"encodedPolyline": "q|geA{|rzMw@X"}, "startLocation": {"latLng": {"latitude": 11.5144901, "longitude": 77.9260559}}, "endLocation": {"latLng": {"latitude": 11.5147683, "longitude": 77.9259292}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head northwest toward NH544"}, "localizedValues": {"distance": {"text": "34 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 25370, "staticDuration": "1425s", "polyline": {"encodedPolyline": "i~geAa|rzMbAvAfApAVZnA`BhA~A|@tAhCdEnArBTZNTzAtBxBzCxBnC~AtBrEvFpB`CjAlAv@t@nDxCDDZXpCrC`@`@fAhALLpIbJRT`@b@d@f@pArAh@h@t@l@|AnA|EvDVRpAdANJhErDhAjA\\b@|@nApAxBhCtEVd@n@nAdCvE`@t@nAzB~BlEVd@fAxBv@rBp@lBlAbDbA|CRr@ZlA`@|BT~BFtABt@?jAOlDEx@WvCSzCo@rKm@bIAPK`BKxAMdCQnDI|AQrBY`Eo@jJk@rIu@vKGn@GxAcAvPCl@AxBDxCf@~Hl@lI@F\\zD`@zELvAJxADf@HpAPdCHlAf@bGZjElAhQ\\xFDn@PbCNvBDv@L`B^bFXpCd@tCr@vCv@~BbA~B^t@fBhD@?\\f@HNdBfDfAjB^p@dBtCX^|BzChBvBr@v@xF`GpCjD~NrSt@bAlIhLf@r@tDbFVZnCdD|AdBv@x@xHxHRPxE|ErB|B~BzCfBdC\\j@fBxCbAtB|@hBpAzClC|FbB|Dn@jA`@f@p@~@h@n@fBfB`BpAdCxAf@Vz@ZpBp@vNnDtHfBVBNBvBd@pBj@~Bt@lBz@xChBZRXTvCxB|@p@nBzA`Az@jAtAdAzAVf@^t@Xp@`@bAVz@J`@DP`@`CPzBBfADpBJlBTlHBp@JjBFlBFnGBxFDhEBhEBvA?zB?~F?zL?XJtBNbBnAnITlC`@lK?~B?b@I|ACt@I~AGjB?nB@b@DlAPhC\\jFRnCJ|ABd@@x@F`GBhED|FDfGBbA@XBb@RjBZ`Bf@dBN`@N^h@jAd@x@|@nAbDxDzAvBfAjBv@|AZl@Zp@l@jB`@jBNvAFlA@`@AfAGnAARYnBe@jBm@zA]t@_ApBc@zASdAGZGv@Cd@Cj@@hAb@lHPzCBpA?bA?h@EpEA|FDbBFlATjB@LDTVrA^vAt@lBjAbCbApBx@`BN^v@nBX|@Lj@h@bDF`@x@tFF^Z|ALj@z@fDZhAv@~B\\bA\\pAXvATzB@RDpA@\\CtAAXCx@WxDEp@M`BU|CCPGr@APCd@EdACrBCzA@pA@xABxAHjELbCFn@JtABf@NzCXlFFpAJpEL~FTlIBfAJtA^zCJ`BT`CHdAHlAD|A@d@?fF@h@JxB@ZLnBHz@Fz@JxAVnDL~BDp@t@zKDxBGzG"}, "startLocation": {"latLng": {"latitude": 11.5147683, "longitude": 77.9259292}}, "endLocation": {"latLng": {"latitude": 11.4446651, "longitude": 77.7209209}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left onto NH544\nToll road\nPass by Sri Neeviyin New Mass Cooldrinks (on the left in 15.3 km)"}, "localizedValues": {"distance": {"text": "25.4 km"}, "staticDuration": {"text": "24 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 48, "staticDuration": "7s", "polyline": {"encodedPolyline": "ehzdAwzjyM\\pA"}, "startLocation": {"latLng": {"latitude": 11.4446651, "longitude": 77.7209209}}, "endLocation": {"latLng": {"latitude": 11.444517099999999, "longitude": 77.72050999999999}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_LEFT", "instructions": "Slight left toward Service Road"}, "localizedValues": {"distance": {"text": "48 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 1202, "staticDuration": "140s", "polyline": {"encodedPolyline": "ggzdAexjyMArAF|DPrF\\`DFf@H`@BJhArExAvDN^^f@fApBp@`AfAtArDpDxDrCt@j@LH"}, "startLocation": {"latLng": {"latitude": 11.444517099999999, "longitude": 77.72050999999999}}, "endLocation": {"latLng": {"latitude": 11.4399436, "longitude": 77.7111121}}, "navigationInstruction": {"maneuver": "MERGE", "instructions": "Merge onto Service Road\nPass by Ag. Engineering Works (on the left)"}, "localizedValues": {"distance": {"text": "1.2 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 32, "staticDuration": "4s", "polyline": {"encodedPolyline": "sjydAm}hyMVt@"}, "startLocation": {"latLng": {"latitude": 11.4399436, "longitude": 77.7111121}}, "endLocation": {"latLng": {"latitude": 11.439819, "longitude": 77.7108447}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_RIGHT", "instructions": "Slight right toward NH544"}, "localizedValues": {"distance": {"text": "32 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 23820, "staticDuration": "1454s", "polyline": {"encodedPolyline": "{iydAw{hyMvC|Bf@`@RPpBpBj@v@b@l@PZz@bBb@`AV<PERSON>@p@`Cz@jD\\`BR|@~@`E`@dBpBxIh@zBt@zC^|Ab@lBl@lCj@~BHZv@dD~A|GZpAHZb@bBBNdBlHdCpKNn@`@~B^rBPbBF`BItBIfAStDkAnSg@`KARGvASnDGj@Cn@GdC?p@Bx@NxABPVnAd@vALVx@pAj@p@z@x@NLn@b@NHRLr@\\bCn@x@Lp@H~@DhBC~@Gz@KtF{@fEq@lGaAjD[l@ElBKt@Cf@CnHa@zBKtB?\\?vADZBbADPBvBXfB\\n@NtA`@pAd@dBx@`B`AnAz@TRfB`B`BjBrAtBx@zA~@|BBHt@`Cn@tCb@xBnAjFvA|G`@fBJb@Vx@rArDfA|BdBjDp@zA|B~EnAjClB|DbG`M~@vADFJJPTFJPVNR`BjBxAtArAhAxAnAVR~DjD\\XjC|Bx@t@`BpAhCvB|AdAx@d@t@^nAf@b@L|Bt@|Bj@lARv@J@@|BPlCDhA?|@ArBKzBWjBYl@Or@QbBg@fBm@j@UlYeK`@M`Ba@rB_@tBUtBIhCAtBHVBx@Hp@Fz@Jr@N`@HtA\\n@R|@\\@?xAp@rDpBr@`@lB~@\\PpCzAvEbCfE`CNJXNnBdAhDlBvB|@hBh@zFrA~A`@~A`@z@PhKbChFjA|Bb@xARtBXvD\\nEVpFPnBPpB^nFlAdJrBxCh@TBzAXjBPzARb@Fp@JlEl@jALxAVzAXdB^vG|AvA^hEdA`AX`D|@fA`@v@^r@\\\\RhDvBr@d@jC`Cb@`@Z\\d@h@pAhBf@|@lAjCvAdEjAdEnD|LvAjFl@~Ah@dA\\n@`ApA~@dArAbAtA|@FDZNv@Zb@NdBf@VFrAT`BNfBHbIDp@@lLJ|GDnDDfCJXBbALTBdB\\fAZ`@LfBp@fB~@j@\\h@^h@`@|D`DdB`AzE~BnAp@bAh@lBrAtAlAlGxG\\^TZRVZb@|@xAJRd@|@bBpD`@v@fBxD|B|EJRt@tAtHbNj@dArEhI|@pAd@l@NP~@bArB|BpBzBpAvAbAjATVNN`BjBz@z@~E`FHHxAjBnAnBpEjJZl@dA`BrAbB|A~ARPrA`AhBhAzF~C`@Vx@d@DBjCxAxGrDfE~Bt@b@l@`@xBfBdBbBtA~ApDlFNRZf@Zb@vG|JlFbIjGpJz@|An@pABD"}, "startLocation": {"latLng": {"latitude": 11.439819, "longitude": 77.7108447}}, "endLocation": {"latLng": {"latitude": 11.2931314, "longitude": 77.5904803}}, "navigationInstruction": {"maneuver": "MERGE", "instructions": "Merge onto NH544\nPass by Police Car Check - Pollution Certificate (on the left)"}, "localizedValues": {"distance": {"text": "23.8 km"}, "staticDuration": {"text": "24 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 529, "staticDuration": "71s", "polyline": {"encodedPolyline": "au|cAokqxM^XPH?@z@zBz@hCx@nDVxAJv@Fd@JhALtB?V?@JP"}, "startLocation": {"latLng": {"latitude": 11.2931314, "longitude": 77.5904803}}, "endLocation": {"latLng": {"latitude": 11.2915789, "longitude": 77.5859986}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_LEFT", "instructions": "Slight left\nPass by EAGLE FURNITIRE (on the left in 500m)"}, "localizedValues": {"distance": {"text": "0.5 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 1405, "staticDuration": "137s", "polyline": {"encodedPolyline": "kk|cAoopxM|AZb@Pp@V`AVnBXtBX@@hCj@fAT^B~@BjAFb@Bd@DH@jCVnCJdADt@Bh@@d@@l@?F?d@AVCVEDAHClA]dAYBAb@KfAUDAl@Ax@Fx@DVBl@Bx@FR@lBLx@Bv@Br@QXA"}, "startLocation": {"latLng": {"latitude": 11.2915789, "longitude": 77.5859986}}, "endLocation": {"latLng": {"latitude": 11.2792526, "longitude": 77.5849417}}, "navigationInstruction": {"maneuver": "MERGE", "instructions": "Merge onto Kavindapadi - Kanjikovil - Perundurai Rd/Perundurai - Kanjikovil Rd\nPass by Krishna Mess - <PERSON><PERSON><PERSON> (on the left)"}, "localizedValues": {"distance": {"text": "1.4 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 626, "staticDuration": "91s", "polyline": {"encodedPolyline": "i~ycA{hpxM@GFGNC@?NF@BBLAJv@nBr@dAr@r@tA`At@\\PHZL`ANB?zEb@`ADzAHZ@r@D"}, "startLocation": {"latLng": {"latitude": 11.2792526, "longitude": 77.5849417}}, "endLocation": {"latLng": {"latitude": 11.2746354, "longitude": 77.58270499999999}}, "navigationInstruction": {"maneuver": "ROUNDABOUT_LEFT", "instructions": "At the roundabout, take the 3rd exit onto Perundhurai - Bhavani Rd/Thiruvaluvar Street\nPass by Aroma Bakery Perundurai (on the left)"}, "localizedValues": {"distance": {"text": "0.6 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 4035, "staticDuration": "342s", "polyline": {"encodedPolyline": "oayc<PERSON>{zoxMFCHAHBDBDLAJCB\\l@Rb@^v@d@z@`AfBT`@lCxEBHFT@?F@DBBNABLN|@zA\\z@d@pBLn@FTjArEPn@|@xDb@`BZpA|A~FLb@h@hBPb@l@xAXl@Pb@fAbCf@dAr@fBJXb@jA|@`Cx@hCL^fBjFDHn@rBj@pBPx@Fb@Lv@z@hGPn@Lb@^pA`@nAlAfEbA~C~@vCjA|Cp@tAl@|@x@~@`A|@XX|@bA~O~Op@j@`AbA|@fANT@?H@BBHL?HCFA@rA`E|@rDBRV`AJf@"}, "startLocation": {"latLng": {"latitude": 11.2746354, "longitude": 77.58270499999999}}, "endLocation": {"latLng": {"latitude": 11.2579259, "longitude": 77.5506927}}, "navigationInstruction": {"maneuver": "ROUNDABOUT_LEFT", "instructions": "At Old Bustand Cir, take the 3rd exit onto Olapalayam - Modakurichi Rd/Perundurai-Coimbatore Rd\nPass by Drychem India Pvt Ltd. Perundurai (on the right)"}, "localizedValues": {"distance": {"text": "4.0 km"}, "staticDuration": {"text": "6 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 553, "staticDuration": "45s", "polyline": {"encodedPolyline": "ayucAyrixMFRJ^FN\\`A^v@RXpAnANNr@r@zBzCd@r@pAnCDHDDr@xA"}, "startLocation": {"latLng": {"latitude": 11.2579259, "longitude": 77.5506927}}, "endLocation": {"latLng": {"latitude": 11.2550897, "longitude": 77.5465773}}, "navigationInstruction": {"maneuver": "STRAIGHT", "instructions": "Continue straight onto Perundurai-Coimbatore Rd"}, "localizedValues": {"distance": {"text": "0.6 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 29327, "staticDuration": "1656s", "polyline": {"encodedPolyline": "igucAcyhxMFxAfFnKdBvDf@xA\\dAFVb@vAPb@HTt@hB`DpFzAhCbAvBdAvCHV`@|ALh@d@fCl@xE`AnHBVTnBv@pFrAnHhAlGBFhD`S`BnJn@tFJt@PjAFTr@~BvA`Ed@`BvAbFbAjCb@hAP^Zl@zDzHpF`LJRtAdCxBdD|BvCVZjCzCb@`@z@z@xA|A~@jAdA`Bj@bAR`@z@tBp@zBd@pCZlCFlB@zBAf@MfCAXUlBQbAS~@Md@g@~AiAlCMVi@~@q@dAy@fAyArBw@zAEHy@vBc@bBKb@W~BIz@GtBD~BPbCNfAJl@FTf@lBr@jBz@~AfA~A`D~DbA~A~@hBt@rBr@dCf@rCJl@PdAp@xDj@~Bb@xAjD|Jz@~B~@dCv@xBpAxDPh@bB`GzAfGVhAJb@R`Ap@bDRnAd@zCtAzIh@bCl@hBPd@v@lBt@tAt@hAb@j@lBvBzEhEb@^hAbAf@j@VVx@dANVx@tAR\\P`@r@bB^jAt@jDjAxGJf@ZtAZdAL^N^p@|ATf@dAhBz@jAVZFHnArAtApA|BlBrAtA`@j@TZVd@R\\~CdGzEfJNZr@hB^nATz@\\bBZdCd@tGNnAh@vCf@fBFPt@jB`AhBzAzBDD`C~C|BnCzBjCh@j@pDrD~FtGnGxHnBhC|C~DhAzAlApBv@~APb@v@zBp@xCZzBJv@X|BX`BNv@x@xCt@xBTj@r@`BlAzB^r@rArBpBnCrGdJPTfEbGvArBn@dAnApChBlEfBjDN\\rCbGj@rAl@`Bx@`D\\hBRjBLlC@~AEdCOfFOdEQfFm@|T?VY~JGvAYzHI~ASxB]fBOn@c@rA{CpIM\\u@tBo@hBwH|SgBhFgB|Ek@nBi@`Ca@|AqAlGsAxGI\\I\\}A~HMj@[nBMfAc@tHAVMnBk@vKGnB[xQKdJCjAE|BGpFE|BElBMnFEv@In@ABWnA_@fAQ`@]n@gA~A[b@qFbH{ArBcC~DcFxIkCvEoA`CKPgBdDqAtBiBnDiAjCuA|DGTuAbFu@jDUnAIh@w@~FM|AI`AKlBEv@Cl@GrFBfDFfCLvBBb@\\nEZjDRjBZpB\\~Al@dBHTZr@b@v@tAlBtAtAzB~ANFJFlBx@r@TdAXtMtC|FpAx@TpAXb@H^JfAXfA\\`Ad@hAl@bAr@zAvA`@f@TZPVHJn@jAh@pA^rA\\`BPjAL`Al@zFr@vIr@zHh@tED`@v@|E~@jF`BrINv@jAfGlArGl@bDvArHz@zEn@jDbCvMN~@Fj@Hn@`BfUb@hHDl@Bn@DrBExCKvGUlL"}, "startLocation": {"latLng": {"latitude": 11.2550897, "longitude": 77.5465773}}, "endLocation": {"latLng": {"latitude": 11.198523699999999, "longitude": 77.3073491}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_RIGHT", "instructions": "Slight right onto NH544\nToll road\nParts of this road may be closed at certain times or days\nPass by Denteck Equipment (on the right in 20.8 km)"}, "localizedValues": {"distance": {"text": "29.3 km"}, "staticDuration": {"text": "28 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 1198, "staticDuration": "104s", "polyline": {"encodedPolyline": "wejcA}azvMVd@C|@CfAElBAt@IrE@vA?p@DjAHbAL|Ap@dEx@dDb@tA|FvQTd@^j@N^"}, "startLocation": {"latLng": {"latitude": 11.198523699999999, "longitude": 77.3073491}}, "endLocation": {"latLng": {"latitude": 11.196029099999999, "longitude": 77.29691249999999}}, "navigationInstruction": {"maneuver": "RAMP_LEFT", "instructions": "Exit onto Devampalayam - Palangarai - Avinashi Rd\nPass by கணேசா (on the left)"}, "localizedValues": {"distance": {"text": "1.2 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 92, "staticDuration": "18s", "polyline": {"encodedPolyline": "evicAu`xvMHNUPuAd@OR"}, "startLocation": {"latLng": {"latitude": 11.196029099999999, "longitude": 77.29691249999999}}, "endLocation": {"latLng": {"latitude": 11.1966036, "longitude": 77.2964476}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right"}, "localizedValues": {"distance": {"text": "92 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 2255, "staticDuration": "195s", "polyline": {"encodedPolyline": "wyicAy}wvMGNCBMFOVE\\?j@Dp@B`@@dC@t@C~@EfAI`CCfAALGhCGlBKdEBdAFj@Hd@@DH^@D\\fARj@^z@h@tArAhDd@`AJTRd@n@dB@Bd@v@`@p@pAbBbAhA`BzA~@|@lAlABBt@n@h@b@VX`@b@`@d@DFvCjD~C~CBBxAlA`ChBhAz@z@p@n@f@"}, "startLocation": {"latLng": {"latitude": 11.1966036, "longitude": 77.2964476}}, "endLocation": {"latLng": {"latitude": 11.188333, "longitude": 77.2792812}}, "navigationInstruction": {"maneuver": "ROUNDABOUT_LEFT", "instructions": "At the roundabout, take the 1st exit\nPass by <PERSON><PERSON><PERSON><PERSON> (on the left in 450m)"}, "localizedValues": {"distance": {"text": "2.3 km"}, "staticDuration": {"text": "3 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 2925, "staticDuration": "309s", "polyline": {"encodedPolyline": "afhcAortvMHCD?LFBL@BADb@hANbA@FFn@@PHfB@b@D`ABhABrBAXIlAMdAAFa@bC[vBEXYzBGn@M|@c@zEMtBIfA]bCCJ?Da@`D]tA[lAyA|EcAtCk@tAc@~@g@nAiAzDSj@c@`AKLWZ[b@e@l@y@tAUp@WjAIbA?z@HfARfAXz@Zl@lAnAXVXVx@t@x@x@tB~Bl@z@`BlCFFnA`CXj@Tj@^t@rAjC@@"}, "startLocation": {"latLng": {"latitude": 11.188333, "longitude": 77.2792812}}, "endLocation": {"latLng": {"latitude": 11.1882524, "longitude": 77.2551462}}, "navigationInstruction": {"maneuver": "ROUNDABOUT_LEFT", "instructions": "At the roundabout, continue straight onto Annur - Avinashi Rd/Annur - Avinashi - Mettupalayam Rd/Devampalayam - Palangarai - Avinashi Rd\nContinue to follow Annur - Avinashi Rd/Annur - Avinashi - Mettupalayam Rd\nPass by the park (on the right)"}, "localizedValues": {"distance": {"text": "2.9 km"}, "staticDuration": {"text": "5 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 16168, "staticDuration": "1350s", "polyline": {"encodedPolyline": "qehcAu{ov<PERSON>HK<PERSON>@{AdDy@bBGHWf@MVa@n@MNm@p@KH_BrAy@j@UNk@ZmAd@KBWHiCl@aAXa@LuBt@eBf@y@LW@s@EoAIOAi@?MAe@Cm@BaAF]@UBeD`@sC`@g@Fo@RWJURm@f@w@~@CDEHo@jCU~@kAvEg@rBc@lAYnAMd@CHWtA]zA}@`EOv@QjAEVWxBAPEzAEvCMx@q@pDkAnGGXIt@OpAEbEBbCJh@FN|@tAhA|A@F@NEj@If@a@zBKh@i@lCMl@UtACb@ClBBhCEp@G\\Or@}@tDYrB?@IdAAFIr@KbAEb@QtC?Z?bA?Z@vAArBKnDEv@ETY|@sAtCOXS`@Sd@sAvDq@nBs@pBSpAQtDA^QbBi@|FOv@O^_@v@eAzBUb@q@hAiAhBGLaAbB{BfDU`@U^[t@Sv@a@tC[nBEVi@fDOtBKbBi@vDWlBQrBc@zDKvAInBC^IdAKpBK|AARMnBAb@KpCKlBGdA]vGIpCAj@IzA?LStASfAOz@m@tCu@bEKt@O~A?RE~@Cv@IbCIvAG`AYzDQjBAZAXQvCShBMn@Id@_@v@Qb@Mf@El@@\\@d@D`BJnCC|@?r@C|AO~DO~B?FMlDATUvCSvBcAhHoB~J[xAeDbM_@fCALYhFMjAObBGjAI~@On@kA~Co@xBUp@aA~EAh@I`@Ut@_A`Dm@bC[tBI\\AJCZ?LEpAAf@GjAAJAj@?nCFhFG~AUbA?@kAvC[pCEj@KjBKhCEvAAr@G`ACh@ABOfAAFUdACb@AJC^An@?NAp@@PBdBEhBIr@e@`DQ~@i@nCI`@SbAIj@q@dHe@lDMr@SrAc@zCMz@o@nFW`CSbB_A~HSxBEx@Gv@E\\QzB{@|ICZ]bCIj@]lBo@|CgAbEW`BOv@Ib@]tACJKXWl@_@v@wAzCUd@M\\Qh@Or@I\\u@`FObCM~AG~@C\\Kt@CTEVCFe@pBMdAAFCTMnCAJEn@MhAO`AABYtA[~A_@nBGt@Qn@a@fAaAfBg@z@]d@YZGHo@z@cApBa@hASb@EHi@r@_AfBgAfBa@d@ER"}, "startLocation": {"latLng": {"latitude": 11.1882524, "longitude": 77.2551462}}, "endLocation": {"latLng": {"latitude": 11.2287182, "longitude": 77.**************}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at Ramasamy Site to stay on Annur - Avinashi Rd/Annur - Avinashi - Mettupalayam Rd\nPass by Axis Bank ATM (on the left)"}, "localizedValues": {"distance": {"text": "16.2 km"}, "staticDuration": {"text": "23 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 1756, "staticDuration": "206s", "polyline": {"encodedPolyline": "obpcAq<PERSON>u<PERSON><PERSON>@pAU`@u@nAm@dAOTMVw@tAOTsA~BcAdBOVKp@QtBGh@c@nBI`@SbAKn@o@xDYlB[xBCPYpCSzAMfAQtAM|@M~@WtBCJANIv@In@ARAJE`B?~@Dd@J~@@p@?ZIfA]lB]jBCB"}, "startLocation": {"latLng": {"latitude": 11.2287182, "longitude": 77.**************}}, "endLocation": {"latLng": {"latitude": 11.2331602, "longitude": 77.**************}}, "navigationInstruction": {"maneuver": "STRAIGHT", "instructions": "Continue straight past VINAYAGA AUTO GARAGE onto Annur - Avinashi Rd/Annur - Avinashi - Mettupalayam Rd/Kamanaikenpalayam - Annur Rd\nPass by dream design Annur . (on the right in 1.3 km)"}, "localizedValues": {"distance": {"text": "1.8 km"}, "staticDuration": {"text": "3 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 211, "staticDuration": "37s", "polyline": {"encodedPolyline": "g~pcAcqru<PERSON>?JEd@E~@OzDAz@BV"}, "startLocation": {"latLng": {"latitude": 11.2331602, "longitude": 77.**************}}, "endLocation": {"latLng": {"latitude": 11.2332944, "longitude": 77.1030501}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_LEFT", "instructions": "Slight left at Sangeetha Gadgets - Annur onto NH 948\nPass by Best designer in annur (on the left)"}, "localizedValues": {"distance": {"text": "0.2 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 2885, "staticDuration": "245s", "polyline": {"encodedPolyline": "a_qcAaeruMQTG^AxA?tABp@Dp@R~BRfBLdBDb@h@rGn@fICf@AVIZm@xBsArDaDlIeA`D_@bAENEXErBCnBS|Be@rDCRc@|CCPQjAG\\S|@UbAQ`AY|AIf@oAnHI\\[|@KX[r@}@nBy@|Au@`BWv@_AhD]`BSx@m@nB_AjCo@bC"}, "startLocation": {"latLng": {"latitude": 11.2332944, "longitude": 77.1030501}}, "endLocation": {"latLng": {"latitude": 11.2390528, "longitude": 77.0779834}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at BEO office onto Annur - Avinashi - Mettupalayam Rd/Mettupalayam Rd/Mettupalayam - Annur Rd\nPass by Old Bus Stand (on the left)\nDestination will be on the right"}, "localizedValues": {"distance": {"text": "2.9 km"}, "staticDuration": {"text": "4 mins"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "114 km"}, "duration": {"text": "2 hours 11 mins"}, "staticDuration": {"text": "2 hours 11 mins"}}}, {"distanceMeters": 68652, "duration": "8252s", "staticDuration": "8252s", "polyline": {"encodedPolyline": "acrc<PERSON><PERSON>muMGVOj@I`@_@tB_@zAKZ}@jCOh@Kb@GVMj@QnAGZg@dCYz@e@lAADyC`I_@jACLe@hBY`AITo@vCUpAC<PERSON>p@o@xCC<PERSON>r@u@xBa@hAIRc@bAYn@MPy@fAwBzBwDjDsAlAyAjA]`@OPe@n@Ub@o@hAS\\gAfBSV]x@m@jCc@hDQf@MXYd@q@tA_AvBq@zAe@`AUt@aArDGTcBtEkCdHEJoBhFYv@]dAm@~AEJcAfDe@dBO`@MZiB`F}AbDEJc@vAOl@}@zBaC`GGN}AnE[bAGX?Tw@zAmAlB{@fBGPK^g@xCe@nDI~@G`A?JEdBGf@ENENg@tAWl@Sb@]fASj@CFe@~A[bAc@hA<PERSON><PERSON>`@sCnFsAxCIPSZSVq@t@wC|Bk@p@c@t@[p@aAvCk@tAOd@Kd@IvAE^e@lBw@bCa@rASt@e@xBU`AYdAuCrKs@vB{@rCw@rBUj@eBhE}BxGu@|BYdAQdBm@~DWbCKd@qAzFsChKUlAs@rFA@OlCG~AKzAStBYvB?BEj@AZ@jFShDGt@IbAGb@QdA[xA{@dDk@zB_@hBiBjIc@nBa@bBS~@Mh@UbAKb@_@|AMh@GRI^[nAeAhFy@dE_@bBGr@GxFOvBIp@Gf@cA|Fe@vCMz@En@MpCKvBKnAUnA[jAMTQVgFzFw@|@oA`Bs@fAeBdCQZiApB[h@[d@U\\k@fAsAnDaBbEy@rBUh@_@z@_@jASr@q@jBOb@]bA[|@a@hAkAbDi@zAg@~AwAdFSl@KVaApBo@vAABuBzGe@|Ae@hBq@bCGXa@`BGT]lAOj@y@zCGPITWf@eAbBaBtB}@lAORQVwAnBSXCF{@rAYd@i@~@Ud@w@|AGLKR_@r@ELg@fAOXeAxBc@|@OXc@|@KTaApBCF{@fBSb@a@|@mAdCe@bAgAtBYj@Yl@MVe@`ASd@Yh@a@v@KPGLGJO`@YdA}A~EAF]dAQn@GNg@dBKPeB~CWb@QXeAhBKNGN_@l@[j@q@fAINg@z@m@bA]j@U^MTm@`A}@zAaA|AqAzBW`@qA|Be@x@[j@_@n@m@dAw@tAm@dAO`@]zAI`@S~@K^O^k@|@CBQRg@d@c@ZSTk@f@_A~@CBQPyAzBc@v@GLS\\k@z@gBbDYd@GPKRy@nA[j@MT]p@{@r@oAfAC@_ErDiC`C{AtAYVURs@p@i@^OHuBzA{BbBSLgBrA_Ah@i@Rw@Xg@JmAV_Fx@q@PYDYH[P[Ra@r@a@t@AJu@zASVuCzD}ArBoBjCgBdDWd@GL]r@KToBbE}AfDi@fAeBxD_B~Cg@dAg@z@y@nAw@nA{A~Ba@j@}@xAa@j@yBnDq@jA]j@mBfDg@~@S`@e@~@uAjDoBbGsArDQ`@y@`Bq@tAiAvCi@rA{BpDOTwBbDgBrCoArCs@tAm@z@cBhByApAiAbAY\\WTOVYp@Ov@w@xDqAlGo@bCWn@a@xA_@vAc@f@IJMt@@RDp@AVIn@_@~Ba@hCMvAEnCFlCJvAf@tCT|A?@LvAG`DM|AIh@sBrGUd@k@p@m@j@oAlAaAtA}ErGcAbBaAdB[n@Oj@Gj@QbCa@hDKrBIZQb@U\\o@v@q@j@q@j@w@`Ay@n@kAl@SRKNGPKr@MdACVOjA]lC?DOv@CJo@fCUfBGpAElAFnCJ`FB\\Pp@JTZhAB\\^dITpDVdDBP\\nCHb@b@jC?@?b@GHA?Y@ECGCW]eA}Ae@e@a@_AQMqAk@OOM[i@q@US}@gAUGqAGUIEMAgAIWWSKA]?mAKa@K_Ag@m@OeAQg@K]MgAa@gA[g@_@sBu@IEiAi@i@O}@IEAmAXSJ?H@DF@TKVKJAr@F`@Jh@Zd@\\nEpEj@\\f@Nv@r@Vh@dAfAHZAd@C`@@b@@@LLNBr@AF?XDd@Pf@\\RFb@Fp@BNDLTDTDlAAXGdABf@H\\NRl@ZJP@H@Z?Bi@z@I\\DJh@d@BPALm@fB@HJLPBl@EVNb@XBFDXDvAFzABpAATC^Q~@WV_Br@yBx@AD?DBLB@~@t@DNADELk@^_@NA?c@b@a@d@k@bAET@L?LDNFJNFXEL?L@THZVLVDZ@TFTJPNL^D^?D?PBb@Rl@Lr@@~@IPDBDBbADVf@fAPFP@PJDJj@z@NRDVAHc@v@GVDNVVDLBZIf@@dACTELEJ{@b@aAj@KLQ^WPWHOBc@CWAOBm@d@mAf@s@AWAo@WGAQ@GDaAfAs@`AMZC^?V@HHpARbALRNJZH`@Jn@H\\@j@CVDNDT^FVJFPBbBLXNFFFR?X?BEPQZ[XOHsBf@GPLv@APIRQJ_A\\S\\?NLLTD^E^BZNJPJXBV?PMl@Eh@@N@Lb@TVFVNDJFz@@b@R|A@R?^GRWTWJc@Fa@Ac@QCA]UIAS?K?[H]BiAOmCYoAQoBAQBu@Fs@V{@JmATEBsAl@IFKJKN?b@?f@XJR@@@f@LPBvBVr@Ll@ZJNFVSHK?CAqAe@m@Ec@@YAU@EFDHp@NXTDJ@DCPA\\Ff@JTTL`@FRHNTDj@?l@DVFNTJR@^KBEXWVBJHFL?B@PA\\Er@TZHFFL@\\Ah@Fb@J`@PPB@NFd@CLCj@WPMHGb@OLEh@?TDZ@Z@LHDLE^S^C\\@ZGjADLJFf@LLHd@h@b@z@HHFBnARd@ZRVJ^@TIZg@vAKJUFk@JOFgB`@EF?XJPLNr@f@VV@RITCBe@v@i@v@YZQ^@LJDL@xAGb@EL?`@@`@FfAXRJVVDF|@pAFXCROXK|@XfBLR^L\\FRPDPCf@Wx@Sd@gB~A}@b@{BbAQR@ZHNv@h@FJXbATT^Nz@F~BB`@Hl@`@NRBTCf@@b@Nb@X^DLBh@EXK`@KZ_CpCeA|AQ\\EZEhAUd@g@j@q@^o@n@ANNZLFTAPHRXFLXXXHXCh@a@JE^A~BDjADbBTVLDFr@z@|AbBt@f@vA`Ad@\\PJ\\RPRd@r@J`@?\\GLY^CPFd@d@v@JXPvA?N@DEzAKXMNQACKNu@@k@E]KSEImA{Aa@WMGMSKa@CiACIKU_Ay@QM_@M[Bi@VYPQ?MIWWqAwAOIOGi@QoAUq@@YHIBIHGLMLe@Vo@BGHBFLB\\Cf@QROZ_@HE^C^NNLdB~APPJTB\\NV`@FnACN?XDNJDPAz@Ix@GVOl@Sn@IZBf@DNTVTTLf@?JGh@W^}@n@OPQJGEBMTWXQLIR[DW?Ye@]}@a@SMKMCI?EDOV[BYCi@M_@G_@Ia@IGQCI@]@c@CQIWa@GYQYSQYKWBQPa@x@c@TMHAFHFR?BAVIPOH[NSNCNFRZJd@JNdAn@NVBHDb@Aj@GL_@RYZCJAP@NTt@D\\WhACHg@|@UTIZ@ZFRHPd@b@HJBrAMfBOf@@NDHFLd@b@j@`Ad@t@PTr@v@LXNr@Vp@PRTJT@PE`@[t@aAJAJFLh@BJHLp@RHJF~@Kv@Kh@@l@Ix@EhABh@HNXHFAr@MRBLHTj@JZNz@@RTn@Zz@ALG@ICSi@m@cBUm@SSOE_@Ba@Di@NODM?OI]a@]UESFWVk@@KEYCSBYHc@AUI]GIU_@ECE?GHJvA@H@LKZARFn@ETEFY\\CNDNZd@Tl@T^JP^VNBf@I\\BJFVh@L\\Rd@BL@N?pAYlD?b@DTPf@?n@Mh@Df@\\p@l@h@HBn@LNLt@rAHJ`A`AFHDL?TOJGAEYCUMOGGaAk@SSg@s@WOK?W@Q@M?e@Ug@a@U_@Mg@Ug@C_@Ao@]}@[q@Iu@KGIBAJDj@d@rAF\\?f@Ep@Tn@E\\K`@@V@DJP^PXHb@XXPl@F`@DJNRt@Dj@@p@L`@JPJLb@`@`@t@T`@BH@T@|@Jr@LPb@TH@h@@X@LFR\\BL?NAZWfAADDVLLDBTDXEp@KVD^N~AhA\\Lr@Ld@Vn@l@h@p@XNd@Df@Ah@Cd@HJJDp@Cx@M`A@^`@x@Fl@CRMPeA`@EFEF?R?BZrABd@CXQz@Et@Ml@y@bB@XPp@Fp@@n@?HGf@Sf@WXc@Hk@Fs@TiAZ]BoAEc@FOBq@CSDUXKVC^BNJRVTXPRRTj@Lv@Vv@`@h@VDb@If@AZDRFNPLn@F~@z@pAf@~BA\\MVi@l@Un@Kp@A\\JjAEPMHKBgACMBKJCRD~@L|@Tz@E`@IPi@j@y@l@e@X]JUAICa@MUM]EQ@SJSXEX@|@Fp@Gd@KT[LY@s@KOAWFKJCPFbACR]T{@XYL]^IFi@Ti@NMNGb@@PXl@Tr@PX\\H~@Nb@PJNBFDZDhA?zB?ZFr@CHEFIDKOGi@GaBM}@Qc@u@{@QCiBQq@QKEKKe@q@CCUQ[Ce@AMIGOAUHuAAm@@QL{@Pa@PQXI^Gf@QRSPWJ]?QE[]c@KW@YFa@Fi@Hk@BOFIb@i@x@cB^e@Hc@Bq@RcBTaARc@x@u@HMJUJy@@UGg@MU_@WSm@KUQKUE]@_@CSEc@MmAmAQGYCYFMHg@b@s@`@c@NaARoDP_@CUGi@c@UAOBWJQJURc@n@M\\Mt@KVYJc@@M@c@LOB_@MkAq@c@Mg@?o@J_@VWNqAj@k@\\IJQ\\IXKfAFz@GnAIt@i@`BY^qAz@s@`@aAl@a@f@Ql@C`@Cx@Hv@@XEZOt@IZADSl@CPG~@J|@E^Wd@GHKPAN@J?FE\\OVQH}ARw@Ps@Lo@LKHERAT@d@@`@GRGHQJ_@HMJY`@WLq@Dc@H{@X_@Fo@DOJKVc@v@WLk@D_@Ng@V[H]BgABWJ]XSVQ^cA|@IVJZd@j@Xr@@VGf@]bAKTSNQ@a@Eg@Gg@Ms@KcBYc@Mc@McAO]BMBw@p@e@r@_@n@[VEBSDA?MACCYU_A}@OIKEe@CSFWPc@d@UPiAt@a@NQFg@Va@TcAv@o@Vo@VQ@M?UCk@Us@_@MEg@CuCH_AFy@HaBd@OJORCf@Dj@H^Xb@VZZbA`@r@DV?d@ABGHMHOD_BZ}@XaAFqAAa@JQRg@dBU`@MLUTEd@@^Lv@P^@TEJA@KDI?K@sBI[?_@Dg@Le@Rq@p@[p@Qv@Gp@?`@@f@TfAb@vAPZ^`@RHZDTLJTHv@V`@BHD^APBd@Lp@BdACr@GVGDQ@a@MQS[MYES?g@Fq@NSB}AYe@QSKa@e@e@[YMi@YgAYg@CQ@S@]L_@\\QTo@`B[|@[bBKhCa@rFE~A?f@@d@LpACb@I^sAdEs@pCAT?TZlAB\\A\\W`B@Lb@vA@H?FAVKn@Kl@Qn@]x@[l@[^SRa@\\m@Vu@TUNEDITGd@Yj@QPsBnAcAt@[VUXQXk@nA}@lCMRs@d@MD_@N}@Xa@Xu@j@oAx@KNAPHd@b@lBp@x@DHFRFn@Cn@On@]n@YRYFK?q@CMAo@QMEi@GI@yA`@YRi@Na@BUGUSY]OGQ@SJINCRWnCG\\i@vAEX?z@Dh@ChBFNJNh@^t@Z`@Tb@v@Rt@Db@Ab@Ur@a@hAOVWJ_@BkBJg@CKCSEk@US?OLWl@]j@Od@E`BMl@SZUNs@XKHIZHx@Lj@DXLVTPf@b@l@l@n@f@DRC^UdAE\\QlAMf@Qd@WTOFW?EAu@SSAI?UFSHeA`BmBfC_@\\eAp@U^EZ?r@AJGP]d@KPO`@Cb@@LJL~@N\\^vA|B`@`ABF@T@l@E\\SfBOt@c@~@UZq@r@A?]Fa@GWOMIg@?CBCPFj@C\\qAvAO^M^UVGBYFMJKVIv@@b@Jp@JVR`@BTEP[V_@Nw@ZWFu@?u@IyAScA?[FeAl@YZO^ERPlA?HG`@IfAk@bBCL?VDZZpAj@nBb@nA\\|@Dr@?LEv@I\\OPiAZUBm@CWEc@?u@JMHSZYNu@FQCc@Wc@g@]Ss@KcAKi@KgCwAQA]ZGRCRGj@A@[fAOR]P}@JsCNa@DKBCBERBNV^~@z@`A`BXt@T~@TdBJx@Fz@AfAI`@IROPSLSHY@WAGAs@QQ?MJKTAFAn@Of@?NPn@@RGR]\\SLIV@PPNZNFJ?PUrA?`@RhAJl@@VADKv@A`@Ff@f@|AAt@Mz@D`@@NOzA?N@f@VjABJv@rBBj@C`@UdAENM^a@l@w@bAUn@y@vCGJYXg@`@W`@S~@?@KbAFb@VbA?f@E`@IVOXMP[T{@Vi@JsA@eANgD`@kAf@YVi@r@_@NODa@A[KW[_@eAKa@Iq@IIUCiCAW@YFk@b@i@RmBh@S@iAW}Ac@a@EyD^k@AaA?o@H_@JSJcAx@q@l@}@r@qC~ASJyBhAsAt@QH{Ap@cA^qBj@g@JWBI@_A?]Ae@AiCWy@EoAEqARM@gB?WFIDu@hAURUNm@Zu@l@k@h@{Bv@A@e@j@CB{BtDw@zAYb@STuBfBGF_AnAa@|@GZLj@Nb@]LG@c@LMVMb@e@xAw@~@KJo@Tc@LYT[d@_@fAAb@AjAHlCF^J\\FNnB~Ea@`A}CnHc@l@SEwAKIAA?gBC}@Ce@G_@G_@SIIG[?QAgBMv@IvAA^EhAOfAUz@s@nAEZBVd@fBTtAIx@K`@UPMByCFc@Bc@Fs@\\YTk@p@a@z@GJuA~CAf@?h@BXDV\\d@V`BHn@QQOm@Gs@MSa@QiA]UQKWH{@?MSO]QOYG[?UBMJ[lAqBRcABg@BQJWT[?EEGg@@g@Eu@SY?UDQFEb@CDO@o@C]HMJOh@CL"}, "startLocation": {"latLng": {"latitude": 11.2390528, "longitude": 77.0779834}}, "endLocation": {"latLng": {"latitude": 11.4205247, "longitude": 76.6994114}}, "steps": [{"distanceMeters": 17045, "staticDuration": "1280s", "polyline": {"encodedPolyline": "acrc<PERSON><PERSON>muMGVOj@I`@_@tB_@zAKZ}@jCOh@Kb@GVMj@QnAGZg@dCYz@e@lAADyC`I_@jACLe@hBY`AITo@vCUpAC<PERSON>p@o@xCC<PERSON>r@u@xBa@hAIRc@bAYn@MPy@fAwBzBwDjDsAlAyAjA]`@OPe@n@Ub@o@hAS\\gAfBSV]x@m@jCc@hDQf@MXYd@q@tA_AvBq@zAe@`AUt@aArDGTcBtEkCdHEJoBhFYv@]dAm@~AEJcAfDe@dBO`@MZiB`F}AbDEJc@vAOl@}@zBaC`GGN}AnE[bAGX?Tw@zAmAlB{@fBGPK^g@xCe@nDI~@G`A?JEdBGf@ENENg@tAWl@Sb@]fASj@CFe@~A[bAc@hA<PERSON><PERSON>`@sCnFsAxCIPSZSVq@t@wC|Bk@p@c@t@[p@aAvCk@tAOd@Kd@IvAE^e@lBw@bCa@rASt@e@xBU`AYdAuCrKs@vB{@rCw@rBUj@eBhE}BxGu@|BYdAQdBm@~DWbCKd@qAzFsChKUlAs@rFA@OlCG~AKzAStBYvB?BEj@AZ@jFShDGt@IbAGb@QdA[xA{@dDk@zB_@hBiBjIc@nBa@bBS~@Mh@UbAKb@_@|AMh@GRI^[nAeAhFy@dE_@bBGr@GxFOvBIp@Gf@cA|Fe@vCMz@En@MpCKvBKnAUnA[jAMTQVgFzFw@|@oA`Bs@fAeBdCQZiApB[h@[d@U\\k@fAsAnDaBbEy@rBUh@_@z@_@jASr@q@jBOb@]bA[|@a@hAkAbDi@zAg@~AwAdFSl@KVaApBo@vAABuBzGe@|Ae@hBq@bCGXa@`BGT]lAOj@y@zCGPITWf@eAbBaBtB}@lAORQVwAnBSXCF{@rAYd@i@~@Ud@w@|AGLKR_@r@ELg@fAOXeAxBc@|@OXc@|@KTaApBCF{@fBSb@a@|@mAdCe@bAgAtBYj@Yl@MVe@`ASd@Yh@a@v@KPGLGJO`@YdA}A~EAF]dAQn@GNg@dBKPeB~CWb@QXeAhBKNGN_@l@[j@q@fAINg@z@m@bA]j@U^MTm@`A}@zAaA|AqAzBW`@qA|Be@x@[j@_@n@m@dAw@tAm@dAO`@]zAI`@S~@K^O^k@|@CBQRg@d@c@ZSTk@f@_A~@CBQPyAzBc@v@GLS\\k@z@gBbD"}, "startLocation": {"latLng": {"latitude": 11.2390528, "longitude": 77.0779834}}, "endLocation": {"latLng": {"latitude": 11.3027312, "longitude": 76.9383294}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head west on Annur - Avinashi - Mettupalayam Rd/Mettupalayam Rd/Mettupalayam - Annur Rd\nPass by THENDRAL TRAVELS (on the right)"}, "localizedValues": {"distance": {"text": "17.0 km"}, "staticDuration": {"text": "21 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 29491, "staticDuration": "3806s", "polyline": {"encodedPolyline": "aq~cAq_rtMYd@GPKRy@nA[j@MT]p@{@r@oAfAC@_ErDiC`C{AtAYVURs@p@i@^OHuBzA{BbBSLgBrA_Ah@i@Rw@Xg@JmAV_Fx@q@PYDYH[P[Ra@r@a@t@AJu@zASVuCzD}ArBoBjCgBdDWd@GL]r@KToBbE}AfDi@fAeBxD_B~Cg@dAg@z@y@nAw@nA{A~Ba@j@}@xAa@j@yBnDq@jA]j@mBfDg@~@S`@e@~@uAjDoBbGsArDQ`@y@`Bq@tAiAvCi@rA{BpDOTwBbDgBrCoArCs@tAm@z@cBhByApAiAbAY\\WTOVYp@Ov@w@xDqAlGo@bCWn@a@xA_@vAc@f@IJMt@@RDp@AVIn@_@~Ba@hCMvAEnCFlCJvAf@tCT|A?@LvAG`DM|AIh@sBrGUd@k@p@m@j@oAlAaAtA}ErGcAbBaAdB[n@Oj@Gj@QbCa@hDKrBIZQb@U\\o@v@q@j@q@j@w@`Ay@n@kAl@SRKNGPKr@MdACVOjA]lC?DOv@CJo@fCUfBGpAElAFnCJ`FB\\Pp@JTZhAB\\^dITpDVdDBP\\nCHb@b@jC?@?b@GHA?Y@ECGCW]eA}Ae@e@a@_AQMqAk@OOM[i@q@US}@gAUGqAGUIEMAgAIWWSKA]?mAKa@K_Ag@m@OeAQg@K]MgAa@gA[g@_@sBu@IEiAi@i@O}@IEAmAXSJ?H@DF@TKVKJAr@F`@Jh@Zd@\\nEpEj@\\f@Nv@r@Vh@dAfAHZAd@C`@@b@@@LLNBr@AF?XDd@Pf@\\RFb@Fp@BNDLTDTDlAAXGdABf@H\\NRl@ZJP@H@Z?Bi@z@I\\DJh@d@BPALm@fB@HJLPBl@EVNb@XBFDXDvAFzABpAATC^Q~@WV_Br@yBx@AD?DBLB@~@t@DNADELk@^_@NA?c@b@a@d@k@bAET@L?LDNFJNFXEL?L@THZVLVDZ@TFTJPNL^D^?D?PBb@Rl@Lr@@~@IPDBDBbADVf@fAPFP@PJDJj@z@NRDVAHc@v@GVDNVVDLBZIf@@dACTELEJ{@b@aAj@KLQ^WPWHOBc@CWAOBm@d@mAf@s@AWAo@WGAQ@GDaAfAs@`AMZC^?V@HHpARbALRNJZH`@Jn@H\\@j@CVDNDT^FVJFPBbBLXNFFFR?X?BEPQZ[XOHsBf@GPLv@APIRQJ_A\\S\\?NLLTD^E^BZNJPJXBV?PMl@Eh@@N@Lb@TVFVNDJFz@@b@R|A@R?^GRWTWJc@Fa@Ac@QCA]UIAS?K?[H]BiAOmCYoAQoBAQBu@Fs@V{@JmATEBsAl@IFKJKN?b@?f@XJR@@@f@LPBvBVr@Ll@ZJNFVSHK?CAqAe@m@Ec@@YAU@EFDHp@NXTDJ@DCPA\\Ff@JTTL`@FRHNTDj@?l@DVFNTJR@^KBEXWVBJHFL?B@PA\\Er@TZHFFL@\\Ah@Fb@J`@PPB@NFd@CLCj@WPMHGb@OLEh@?TDZ@Z@LHDLE^S^C\\@ZGjADLJFf@LLHd@h@b@z@HHFBnARd@ZRVJ^@TIZg@vAKJUFk@JOFgB`@EF?XJPLNr@f@VV@RITCBe@v@i@v@YZQ^@LJDL@xAGb@EL?`@@`@FfAXRJVVDF|@pAFXCROXK|@XfBLR^L\\FRPDPCf@Wx@Sd@gB~A}@b@{BbAQR@ZHNv@h@FJXbATT^Nz@F~BB`@Hl@`@NRBTCf@@b@Nb@X^DLBh@EXK`@KZ_CpCeA|AQ\\EZEhAUd@g@j@q@^o@n@ANNZLFTAPHRXFLXXXHXCh@a@JE^A~BDjADbBTVLDFr@z@|AbBt@f@vA`Ad@\\PJ\\RPRd@r@J`@?\\GLY^CPFd@d@v@JXPvA?N@DEzAKXMNQACKNu@@k@E]KSEImA{Aa@WMGMSKa@CiACIKU_Ay@QM_@M[Bi@VYPQ?MIWWqAwAOIOGi@QoAUq@@YHIBIHGLMLe@Vo@BGHBFLB\\Cf@QROZ_@HE^C^NNLdB~APPJTB\\NV`@FnACN?XDNJDPAz@Ix@GVOl@Sn@IZBf@DNTVTTLf@?JGh@W^}@n@OPQJGEBMTWXQLIR[DW?Ye@]}@a@SMKMCI?EDOV[BYCi@M_@G_@Ia@IGQCI@]@c@CQIWa@GYQYSQYKWBQPa@x@c@TMHAFHFR?BAVIPOH[NSNCNFRZJd@JNdAn@NVBHDb@Aj@GL_@RYZCJAP@NTt@D\\WhACHg@|@UTIZ@ZFRHPd@b@HJBrAMfBOf@@NDHFLd@b@j@`Ad@t@PTr@v@LXNr@Vp@PRTJT@PE`@[t@aAJAJFLh@BJHLp@RHJF~@Kv@Kh@@l@Ix@EhABh@HNXHFAr@MRBLHTj@JZNz@@RTn@Zz@ALG@ICSi@m@cBUm@SSOE_@Ba@Di@NODM?OI]a@]UESFWVk@@KEYCSBYHc@AUI]GIU_@ECE?GHJvA@H@LKZARFn@ETEFY\\CNDNZd@Tl@T^JP^VNBf@I\\BJFVh@L\\Rd@BL@N?pAYlD?b@DTPf@?n@Mh@Df@\\p@l@h@HBn@LNLt@rAHJ`A`AFHDL?TOJGAEYCUMOGGaAk@SSg@s@WOK?W@Q@M?e@Ug@a@U_@Mg@Ug@C_@Ao@]}@[q@Iu@KGIBAJDj@d@rAF\\?f@Ep@Tn@E\\K`@@V@DJP^PXHb@XXPl@F`@DJNRt@Dj@@p@L`@JPJLb@`@`@t@T`@BH@T@|@Jr@LPb@TH@h@@X@LFR\\BL?NAZWfAADDVLLDBTDXEp@KVD^N~AhA\\Lr@Ld@Vn@l@h@p@XNd@Df@Ah@Cd@HJJDp@Cx@M`A@^`@x@Fl@CRMPeA`@EFEF?R?BZrABd@CXQz@Et@Ml@y@bB@XPp@Fp@@n@?HGf@Sf@WXc@Hk@Fs@TiAZ]BoAEc@FOBq@CSDUXKVC^BNJRVTXPRRTj@Lv@Vv@`@h@VDb@If@AZDRFNPLn@F~@z@pAf@~BA\\MVi@l@Un@Kp@A\\JjAEPMHKBgACMBKJCRD~@L|@Tz@E`@IPi@j@y@l@e@X]JUAICa@MUM]EQ@SJSXEX@|@Fp@Gd@KT[LY@s@KOAWFKJCPFbACR]T{@XYL]^IFi@Ti@NMNGb@@PXl@Tr@PX\\H~@Nb@PJNBFDZDhA?zB?ZFr@CHEFID"}, "startLocation": {"latLng": {"latitude": 11.3027312, "longitude": 76.9383294}}, "endLocation": {"latLng": {"latitude": 11.3338571, "longitude": 76.78875459999999}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "At Jr batteries and accessories, continue onto NH 181\nPass by PARADISE BAKERY (on the left)"}, "localizedValues": {"distance": {"text": "29.5 km"}, "staticDuration": {"text": "1 hour 3 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 19126, "staticDuration": "2618s", "polyline": {"encodedPolyline": "ssddAuxtsMKOGi@GaBM}@Qc@u@{@QCiBQq@QKEKKe@q@CCUQ[Ce@AMIGOAUHuAAm@@QL{@Pa@PQXI^Gf@QRSPWJ]?QE[]c@KW@YFa@Fi@Hk@BOFIb@i@x@cB^e@Hc@Bq@RcBTaARc@x@u@HMJUJy@@UGg@MU_@WSm@KUQKUE]@_@CSEc@MmAmAQGYCYFMHg@b@s@`@c@NaARoDP_@CUGi@c@UAOBWJQJURc@n@M\\Mt@KVYJc@@M@c@LOB_@MkAq@c@Mg@?o@J_@VWNqAj@k@\\IJQ\\IXKfAFz@GnAIt@i@`BY^qAz@s@`@aAl@a@f@Ql@C`@Cx@Hv@@XEZOt@IZADSl@CPG~@J|@E^Wd@GHKPAN@J?FE\\OVQH}ARw@Ps@Lo@LKHERAT@d@@`@GRGHQJ_@HMJY`@WLq@Dc@H{@X_@Fo@DOJKVc@v@WLk@D_@Ng@V[H]BgABWJ]XSVQ^cA|@IVJZd@j@Xr@@VGf@]bAKTSNQ@a@Eg@Gg@Ms@KcBYc@Mc@McAO]BMBw@p@e@r@_@n@[VEBSDA?MACCYU_A}@OIKEe@CSFWPc@d@UPiAt@a@NQFg@Va@TcAv@o@Vo@VQ@M?UCk@Us@_@MEg@CuCH_AFy@HaBd@OJORCf@Dj@H^Xb@VZZbA`@r@DV?d@ABGHMHOD_BZ}@XaAFqAAa@JQRg@dBU`@MLUTEd@@^Lv@P^@TEJA@KDI?K@sBI[?_@Dg@Le@Rq@p@[p@Qv@Gp@?`@@f@TfAb@vAPZ^`@RHZDTLJTHv@V`@BHD^APBd@Lp@BdACr@GVGDQ@a@MQS[MYES?g@Fq@NSB}AYe@QSKa@e@e@[YMi@YgAYg@CQ@S@]L_@\\QTo@`B[|@[bBKhCa@rFE~A?f@@d@LpACb@I^sAdEs@pCAT?TZlAB\\A\\W`B@Lb@vA@H?FAVKn@Kl@Qn@]x@[l@[^SRa@\\m@Vu@TUNEDITGd@Yj@QPsBnAcAt@[VUXQXk@nA}@lCMRs@d@MD_@N}@Xa@Xu@j@oAx@KNAPHd@b@lBp@x@DHFRFn@Cn@On@]n@YRYFK?q@CMAo@QMEi@GI@yA`@YRi@Na@BUGUSY]OGQ@SJINCRWnCG\\i@vAEX?z@Dh@ChBFNJNh@^t@Z`@Tb@v@Rt@Db@Ab@Ur@a@hAOVWJ_@BkBJg@CKCSEk@US?OLWl@]j@Od@E`BMl@SZUNs@XKHIZHx@Lj@DXLVTPf@b@l@l@n@f@DRC^UdAE\\QlAMf@Qd@WTOFW?EAu@SSAI?UFSHeA`BmBfC_@\\eAp@U^EZ?r@AJGP]d@KPO`@Cb@@LJL~@N\\^vA|B`@`ABF@T@l@E\\SfBOt@c@~@UZq@r@A?]Fa@GWOMIg@?CBCPFj@C\\qAvAO^M^UVGBYFMJKVIv@@b@Jp@JVR`@BTEP[V_@Nw@ZWFu@?u@IyAScA?[FeAl@YZO^ERPlA?HG`@IfAk@bBCL?VDZZpAj@nBb@nA\\|@Dr@?LEv@I\\OPiAZUBm@CWEc@?u@JMHSZYNu@FQCc@Wc@g@]Ss@KcAKi@KgCwAQA]ZGRCRGj@A@[fAOR]P}@JsCNa@DKBCBERBNV^~@z@`A`BXt@T~@TdBJx@Fz@AfAI`@IROPSLSHY@WAGAs@QQ?MJKTAFAn@Of@?NPn@@RGR]\\SLIV@PPNZNFJ?PUrA?`@RhAJl@@VADKv@A`@Ff@f@|AAt@Mz@D`@@NOzA?N@f@VjABJv@rBBj@C`@UdAENM^a@l@w@bAUn@y@vCGJYXg@`@W`@S~@?@KbAFb@VbA?f@E`@IVOXMP[T{@Vi@JsA@eANgD`@kAf@YVi@r@_@NODa@A[KW[_@eAKa@Iq@IIUCiCAW@YFk@b@i@RmBh@S@iAW}Ac@a@EyD^k@AaA?o@H_@JSJcAx@q@l@}@r@qC~ASJyBhAsAt@QH{Ap@cA^qBj@g@JWBI@_A?]Ae@AiCWy@EoAEqARM@gB?WFIDu@hAURUNm@Zu@l@k@h@{Bv@A@e@j@CB{BtDw@zAYb@STuBfBGF_AnAa@|@GZLj@Nb@"}, "startLocation": {"latLng": {"latitude": 11.3338571, "longitude": 76.78875459999999}}, "endLocation": {"latLng": {"latitude": 11.4112981, "longitude": 76.709456}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at J.S.S. TEA CORNER onto Nagapattinam - Coimbatore - Gundlupet Hwy/NH 181\nContinue to follow Nagapattinam - Coimbatore - Gundlupet Hwy\nPass by Arvind&Asha Site (on the right)"}, "localizedValues": {"distance": {"text": "19.1 km"}, "staticDuration": {"text": "44 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 911, "staticDuration": "188s", "polyline": {"encodedPolyline": "swsdAciesM]LG@c@LMVMb@e@xAw@~@KJo@Tc@LYT[d@_@fAAb@AjAHlCF^J\\FNnB~Ea@`A}CnHc@l@"}, "startLocation": {"latLng": {"latitude": 11.4112981, "longitude": 76.709456}}, "endLocation": {"latLng": {"latitude": 11.4136034, "longitude": 76.7024833}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at Ooty Upper Bazar onto Ettines Rd/Nagapattinam - Coimbatore - Gundlupet Hwy\nContinue to follow Nagapattinam - Coimbatore - Gundlupet Hwy\nPass by Toda Bank (on the right)"}, "localizedValues": {"distance": {"text": "0.9 km"}, "staticDuration": {"text": "3 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 310, "staticDuration": "58s", "polyline": {"encodedPolyline": "_ftdAo}csMSEwAKIAA?gBC}@Ce@G_@G_@SIIG[?QAgB"}, "startLocation": {"latLng": {"latitude": 11.4136034, "longitude": 76.7024833}}, "endLocation": {"latLng": {"latitude": 11.4156357, "longitude": 76.**************}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at Biki Dash\nPass by Parking lot (on the left)"}, "localizedValues": {"distance": {"text": "0.3 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 404, "staticDuration": "70s", "polyline": {"encodedPolyline": "wrtdAoddsMMv@IvAA^EhAOfAUz@s@nAEZBVd@fBTtA"}, "startLocation": {"latLng": {"latitude": 11.4156357, "longitude": 76.**************}}, "endLocation": {"latLng": {"latitude": 11.4159563, "longitude": 76.**************}}, "navigationInstruction": {"maneuver": "TURN_SHARP_LEFT", "instructions": "<PERSON> left"}, "localizedValues": {"distance": {"text": "0.4 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 76, "staticDuration": "11s", "polyline": {"encodedPolyline": "wttdAoncsMIx@K`@UPMB"}, "startLocation": {"latLng": {"latitude": 11.4159563, "longitude": 76.**************}}, "endLocation": {"latLng": {"latitude": 11.4162482, "longitude": 76.6995062}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Keep right"}, "localizedValues": {"distance": {"text": "76 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 215, "staticDuration": "32s", "polyline": {"encodedPolyline": "qvtdA}jcsMyCFc@Bc@Fs@\\YTk@p@"}, "startLocation": {"latLng": {"latitude": 11.4162482, "longitude": 76.6995062}}, "endLocation": {"latLng": {"latitude": 11.417986599999999, "longitude": 76.6989009}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Keep left"}, "localizedValues": {"distance": {"text": "0.2 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 327, "staticDuration": "41s", "polyline": {"encodedPolyline": "maudAcgcsMa@z@GJuA~CAf@?h@BXDV\\d@V`BHn@"}, "startLocation": {"latLng": {"latitude": 11.417986599999999, "longitude": 76.6989009}}, "endLocation": {"latLng": {"latitude": 11.4182696, "longitude": 76.69615639999999}}, "navigationInstruction": {"maneuver": "STRAIGHT", "instructions": "Continue straight"}, "localizedValues": {"distance": {"text": "0.3 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 507, "staticDuration": "99s", "polyline": {"encodedPolyline": "ecudA_vbsMQQOm@Gs@MSa@QiA]UQKWH{@?MSO]QOYG[?UBMJ[lAqBRcABg@BQJWT[?E"}, "startLocation": {"latLng": {"latitude": 11.4182696, "longitude": 76.69615639999999}}, "endLocation": {"latLng": {"latitude": 11.418798599999999, "longitude": 76.6999087}}, "navigationInstruction": {"maneuver": "TURN_SHARP_RIGHT", "instructions": "Sharp right onto Havelock Rd\nPass by <PERSON><PERSON> (on the right)"}, "localizedValues": {"distance": {"text": "0.5 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 120, "staticDuration": "26s", "polyline": {"encodedPolyline": "ofudAmmcsMEGg@@g@Eu@SY?UDQF"}, "startLocation": {"latLng": {"latitude": 11.418798599999999, "longitude": 76.6999087}}, "endLocation": {"latLng": {"latitude": 11.4198253, "longitude": 76.69999670000001}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left"}, "localizedValues": {"distance": {"text": "0.1 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 120, "staticDuration": "23s", "polyline": {"encodedPolyline": "}ludA_ncsMEb@CDO@o@C]HMJOh@CL"}, "startLocation": {"latLng": {"latitude": 11.4198253, "longitude": 76.69999670000001}}, "endLocation": {"latLng": {"latitude": 11.4205247, "longitude": 76.6994114}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left\nDestination will be on the left"}, "localizedValues": {"distance": {"text": "0.1 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "68.7 km"}, "duration": {"text": "2 hours 18 mins"}, "staticDuration": {"text": "2 hours 18 mins"}}}], "distanceMeters": 405627, "duration": "30858s", "staticDuration": "30858s", "polyline": {"encodedPolyline": "sx|mAe`oyMKPb@Zn@d@l@`@LHp@h@d@Z|@j@h@`@h@\\fAx@B@LHf@\\PLn@b@VPRN`@Xl@`@PLDBp@d@XRh@^RNf@\\^V\\V~@l@PNJHPJjAn@FB\\NXLd@Tz@`@n@XRJFBLFn@\\ZNx@\\TJPDt@RNBn@DX@V@V?bDDT?rCBL?p@@pA?j@?h@Ar@AfAAtAEd@G|AQz@SDAnB}@zBeAdCmAbFaCnAm@zE}B|Aq@|BiAHE^[PQf@o@fAuA\\a@dB{Bj@u@`@i@t@_At@aAfDoEl@y@n@{@j@y@zB}C|@iAf@c@n@SxDu@lB]nAUr@OPCnCi@|AYl@MjB_@|A[fAQjDg@`BSd@ED?x@G`Ge@`@ETA`@CNANC|Ec@z@IbAKbCUn@IfD_@fAOzEq@rEo@d@KHAbDe@|@IrFq@LAlBSTAv@I\\GHAREpBa@j@MHCVIx@WvAe@r@UhAc@`@MDANEHEv@[\\Mr@U|Ai@TIz@UHCtA_@x@SbBc@H]TeATaAT{@DQJa@F[Jg@BIT_ADOPu@FUR_Ap@cDRcATiAd@gBLa@Rk@^y@JULYVk@nAqCdA_CVi@xAiDVk@v@gBb@aALYjAeCtBcFh@oAPa@N]Te@\\o@DE\\WZMf@KRGFALClBW`BYn@KFApAOdAOb@ILAxBa@zCe@bAO\\Ev@M|AW^EdAGf@Cb@ArAA^AV?dAA`@?t@?dA?X?^?F?~@I@?j@MPGPGPKbAu@t@m@~@w@DElAeAzBiBhA}@XY\\WTSvBeBXWNM^[`Ay@VU|AmAb@_@^YNMzCmC\\WLQVs@Hg@@KVyBVaCBSXiCTqBDa@Fg@@KJqAH_ALgCBc@?ILcCJuAF_A@WFwA?k@?q@Ae@IyA?c@AmA?_@A_@CoAEaB?QAYCcAAa@GkA?YCIBCDAB?f@@bI~@l@HVD`@Fz@Hl@Hp@Jv@JVBF?vEf@j@FnDVtBFt@?nBB`@?`EDj@?`BBhDD~@@`BB^?nGHvCAJA`@E|Eo@`Ca@fG}@|Ba@bLqB`Cc@|A[xAY\\GfB_@|KoBtBYh@INA^E~AKJAjE?v@@fBKnFa@hGe@h@Gx@G~D]j@EtBUvBKzBKlEUfAIj@GrCErA?`BNTB~@JlAPvARdAN~ATj@HvBTj@HfBF`@@zFPzAD|GRB?|@B~DJ`EN~AFJ@vFTjBJfDVnAJxGn@fANtHfAvIlA|Df@dAJ|BR\\BnCTZBb@BhL|@XBnBZRHfAPPDd@NRHl@\\\\V~@hA@@t@jAlAhAj@^zAf@jB~@v@VfBJl@Jz@NvAJfAVzBt@dA`@B?VHRHTP`@Zn@n@JJ`C`C~EnEJHbBzALJhAv@FBdG~BhBr@jE`BNF~ElBfBp@zCjAfBp@ND~An@tChAnFpBp@T`Ab@VJj@XTHt@\\p@ZfAd@vB`A|@b@|@^XDl@IPIJOd@yDD_@j@iFFm@L_AJw@TcBR{@FUJWNGJ]p@mB@AXcAj@eBDQx@qBd@cANWFIFKt@aBxC_FPWt@yAx@{AHObCqEpD{HzAqDtAoCf@{@Ra@HQxAaClBiCLOb@g@RUlAkAhA{@XUx@i@h@Yr@]`Bo@fBaApGqDhAu@\\[^Uv@m@f@_@dAeATW|ByC?[b@m@`@i@TYX_@NSbHwIbB{BnH}IfEoElAkA^?hCkCNOzAeBnAgA|AeAtBmAfAo@jBgAtA_AZWv@q@|AqA`@]nDwCpCaCRQnBsAfEqDfAmAl@u@z@qA|BaEh@aAp@_Bz@iCnB{H~EmRV_A\\qAVw@j@uAhBgDh@iAn@gAp@y@nAoAt@q@VUxAkAxJcHnA{@zAgA\\UROp@g@hAw@n@e@\\Wb@_@TSb@a@RSpA_B~BcDfA{AVa@Zi@\\o@f@mAXo@ZeAf@uBX_BJg@fAyFJe@nAwGH]ZqAb@eBjCyI~@cEJe@VkAR{@R{@Ry@z@uCx@uCZ_A\\aA^{@P_@v@wAPYPYn@aAx@gAHKJO`FsGhBiCj@_AZm@v@kBZ{@Vu@ZkARaAH[Hk@PkARqBFcADeADaAHwARiBt@mDxAoHf@_DDa@R{Bv@}PF_AP}ATqAfAaGFYXsBNgAJeAHkA`@gEVuCD_@Dc@D_@PsBLyAFs@D_@L{AHo@^_DD_@Jg@XmAx@qCx@qBn@oArAwBzA{BbBgCdBeCvFqIT]f@y@|@uAf@w@b@s@j@aALWh@mAJUXs@Lc@x@{CJg@RiAxAkIFYLq@xBuLNw@|CiQNw@n@eDFYH]VkAd@_B\\}@Ti@bAoB|BcE`@q@`@q@r@aAb@e@l@k@bB_Bn@k@pA_Bl@y@l@sA`@}ADWFc@Hu@DeABy@E_ACUKcASeAu@uDSiBAsBJmBz@kFv@eDb@}Aj@eBt@sBnAuC~@eBrBeDdAyA`ByB~AeBXWZW|F_GrAuAbEcEjB{B|AqBjA}AvAeB~CeET[z@qAZq@rBiDh@o@f@k@|B{BbAmAv@kAf@cAtAsCZi@v@kAhCmDhB{Bn@}@zBaC~CaCdF}C~@m@pA{@bBqAn@o@z@{@vBqCbB}BzB}Cj@u@dF_HVc@t@mA^u@Z}@b@_Bt@eDhAoEJa@|AiGdAeDj@}BbBgFt@sC~AgGdA{Db@kBpAuGh@}Cb@}BDQ~@gEb@iBPo@d@oBf@mBLc@b@eBbC}Iz@{C|@wCr@aCp@mCt@{Cj@eCt@yCTcA`@_CBWDk@?oAC}@g@wGCmBH_CRkCT{CLkBD_@HyA?MJyEr@iFPqAPiABQXkBpBcNfAyHDYFk@PoABKPkDNuCN}BPwAZ_B`@eBz@sBd@eAxA_C~@eAhCyBz@u@j@i@hAw@`BoAnAkAz@gA`AgBxA}CtAaD|AwD`@cAZo@r@sBLi@PwABgCGkIEeCQuKCmBEaE@_@?SB}@L{AF[RaAZsAdAqEnAsFnAqFLg@|AuF`@_BjBcHNe@d@iBdCqIVgAn@}DPeBDa@b@eEh@_GDc@Fc@D[^aDj@qF\\cDr@aHHw@^kCZuAv@mC\\_AtAkEV{@jAmD|@sChBaGFURi@|AkE|AsEdAqDV{BNqCVqCLgA^uBPm@dAaC|@oBz@}ANYRc@d@{@b@cAzCqF`D}GpAoCp@gAf@q@z@_ApEcFr@iAhAuBlD_H~A}CdBgDzA{Cf@wA~AoFbAyDj@mBdD_MNk@t@iCh@gB|@_DT{@tAcFPi@|@iDt@mCXcAh@yCt@uCNi@Vu@bAqBhB_Dp@gBXgAbAwDlAoHp@qELw@^yBb@mCV}AlAuHv@sFd@kCnAyHTuA\\wAJa@b@oAN]bAyBhEgJx@iBzAyCjC{Fb@yANUVm@|BqFHOl@uAzBcFn@aBf@uBZwBp@sGLwA`AuJ@ILaAJ]Li@^gAx@aBDGbBmB`@a@b@WfAm@xCmAjFiBpDgBtB{AvAkA`DqC\\[fB_BXWlEcEhAkAvA_BfEeF|@{@~AoAxN_JdAo@fAo@hBy@\\MpBo@~NoD~Ag@RIl@_@|@u@rAsA`CqC~@mAv@cAd@k@T[d@_@hCaDfAwANSrCeDx@eAhH{IvD{E|EuFj@y@^q@Vk@\\{@Ri@Z{@Pc@zAwDJW~AyEdC{GhAoC`B}Dl@}AL]t@{B\\qARiATiBTsB^{EDy@RqC?c@?aBAe@MkBMsD@yA?CLoF?_@EaBWaCs@_EQyAGq@MmBAmC@y@@kC@i@LaBVmBd@oCJs@^_CTmAnBcLn@gC\\aAVm@FKf@y@tCoDpA}Ad@k@PUZi@j@iAFSbDgIRi@J]r@iB^cBN{@DUPiAr@oHToBLcAJs@TaAfBgFr@kBNa@jAmDJKL]v@uBPg@p@cB|@iCp@}AbAcBhBeB`@YbAk@zAs@hKkDhCeAtA}@rAoAf@k@R[pE_G^g@xBqCvAiBp@_Av@aA^c@l@}@`@w@L[l@sB\\_Bf@iDDW@k@D_@Fa@H]Rs@t@kBb@o@fAsAVYh@i@n@q@bEkEh@s@\\k@Vk@Xu@\\oAf@gDNy@`@_B\\yAj@iBr@kCL]Ps@dBsFr@aChB}FdBsFDKL[N_@Vc@fCqDfDiEX_@n@w@fCaDlCqDz@kAx@wARe@hDyGn@uA~A_DFOXm@^q@Va@r@gAFI~@cApAiA~DeDz@e@t@o@`GsELKv@u@HI|@mApCaFh@s@xA_Br@g@dDgCpCcB`DuBJIb@Yf@_@hAq@z@q@hA}@f@g@VYlAuA@?tBmCj@u@rA}A\\a@hAmAhAaAz@w@dC{BtAwAT[LM|H}IhBuBhEaFh@k@tAqADCfAw@nEwC~@g@dA}@p@k@z@{@|DyDtDuD`AmAn@_Al@iAZq@j@wARq@lByFxBiGH]xAeEXy@t@yAx@sA~BsCLMzAiBlB{BxBmC~@kAlAqAzBiBd@UnAw@ZOb@UnAo@vAi@JEnA]bASt@MzBa@b@Gp[yFt@SjQ{C`B]tA[tA]rDcA`HyBpCeAr@Y~BaAtFgCr@_@pBcAhHeEpHaFxCoBzG_F`Ao@h@a@tDkCZUtKsHtGsEfEsCBCbJkGZU|HqFxAcAvEcDtOwKfAu@r@c@h@W`Ae@`Cy@x@UhCc@t@KvAMtCKt@?z@@fEPL@J@|@Ll@Ln@N|A^n@NTFvDbALDB?vBp@rEbBh@\\`FvBvB`A^P`IjDfAb@zBz@zCx@~@NTDjEb@r@FRBp@Hr@FzALjBPF@zEb@dCTn@Hb@DdAJrAPhAPpB^tCt@zEjBnBx@pC~@|Bh@`AL|Hv@|ANpCTfA@xZ|C~@HzD`@|CZdEXJ@dENfBD\\@bA?xA@jAAlLW~KWRA`AGxASx@OpEmAfBa@hBKrEi@pBSpAO^OxDc@zAUbCe@rCu@~CiA`EgB|CsA`Ac@RGbBk@f@Kl@KlAMnBGrCHN@v@FnKl@D?pCBfCGrBOvBWxCm@jCy@rCiAdDgBFEd@WzCaBvHeE|Au@nCiAbGwBrBo@rNmEZKjGmBfJoChK}CnEuAhD}@x@KtBMnB@nAHv@Jb@Fz@JZDbJrAd@FtB\\lRnCRBzB\\jL`Bv@JvCZdSrAjN~@zARfAVr@TLDrAj@|A|@n@h@dA~@zG|GLLr@r@tCxCJTfAjAnCdCf@`@r@b@p@XNFl@Rx@Pl@J|@FbABhACvAOHClD[|@Q`LkAfCOrD_@HAlDSjEUdDUzEk@nDe@bAK\\Al@AvAAv@DvANnBZ~B`@vB^lJpApCZdD^zARhALrLr@b@BjKn@fBNpIbBdCf@lE|@fDt@lCj@rG`B|Bd@|AThCRxCNzDR~CNjBJjAHfE^xALhPnAZDdE\\pBLn@@zB@`BC|CG`Mu@|EU`Dc@pASnEq@~GcA^E`CUxCIzIOjM]hEEzCCnDCdDDnDPfBLf@Db@DrFn@lGx@tAXhHvApFnAbAZbAf@fAl@hHlEr@b@jClAlNhE|E|AzBz@bDtA|IxDxErBp@VnBl@j@LhANlCLjB?pGGpDGtCEfAClDQ`Ko@z@E|Hi@`Im@xEi@lF_@J?`CKrB?t@BvAJ|ATZFZFdFfAxGzAdDr@zBh@rA^p@Xl@ZRJ`Ar@zBfBhA`ApCvBbG~Fh@f@fB`B`Az@fAbArD|DnEpF`@l@FFVR|@f@b@ZVNb@PtA`@VFvATbBLf@BbP|@t@DvCVbDXtALtDj@`C`@hBXjC\\vCTrLz@|Ij@hJp@tBR`DVrFd@lEPvBBV@n@?^ApDCnJEdACnAG|AKpMqAlCWpHw@dNyApAOl@KPC`ASnBk@zAk@v@]fAo@~@k@lFiD|@e@~@_@v@UZIxB]pAKrAA~ABjBPpATbA\\tB|@TJv@d@nAbAp@r@b@h@xDzFnAlBvBpCPTt@t@FFv@f@LHnAj@hAZbAND@`@B@?jADtAAbJY`@AfDGn@?jD@rGFhEFzDAd@@rAB~B@bBB`@?jBBb@?|KOv@?`EN|@J~ARrCl@D?rA^jA`@r@Xb@F~CdBjBrAbDnCnAdAfAr@d@XvAj@r@RZF`@Hr@HX@nADlACfG[tHc@rAAnBBxBPrAPtGpBvAf@dJjCrAZdF~@|Dj@hGt@vAXrA\\|@`@pB|@`EjB|@b@r@NtCfAz@VbAZnAd@h@Tt@^zAbAxAnApEzDHHr@j@|BbBlFrDj@`@VNxBnA|At@tBv@fBf@lAXPFvAVf@DxAPL@~CR`C@d@?hBAdENdBRfBd@f@RlB|@nDlBd@XjAl@xDlBhI|D^NfDrA~Bt@nI`CnBp@fAh@dAj@x@j@jAz@`ExCnEnDd@\\zDlCp@`@vAx@v@d@zIhEvDvA~Ah@`JhChEjALBfGhBh@Rn@Zj@Zj@Xb@T`At@d@d@t@x@bPjTd@n@jCrDvBnCx@dAzBxCpMhQbAnAn@x@l@t@rCdDjDdDXV|AtAf@d@~DnDzDnDd@`@vArAhHpGhFtEtLjKf@b@TTxBjBhC`Cz@t@VTRRLHVVh@d@vKzJxApAhA|@bAz@`BjAhCbBzBlALDhCnAp@Z^P`DnAfF`B`AVjAXbDv@zLpCfCn@vD|@jEbAzKfCtEdAtFpAzA\\pInBzA\\tBd@RFnAXlCn@tGxAdFlAfAVj@NzAZlH`B|FtAjAX^HZN`Cf@dBZrCp@rCp@VF~M|CxDz@jCv@zEdB\\LdEtBvAp@rBhAlBnA~GhFhG~E~DbDfBvAhDpCxGtFHF`HvFzJfI`@\\tDzCPNhCjBbH|FTZtAdAtEtCfB`ArFhCdBn@b@NdHzB`Cj@jDn@|HhA~MfBzInApEf@bIr@fJv@zBNrGn@jBPvFf@~VvBt@FjR~Ar@Fr@FzGl@hUpBtGl@xFd@b@D`AHlE`@N@pHl@dBNfThB`K|@f@FXF`@F|ARzIxArAVbCj@bDz@fCr@tA^l@LfDn@hARfCf@nARxKhBzAThFz@tHtAJB|KpBdFt@fALbEd@`ALrDd@rDf@pARj@HfBV~AV`@HTDl@RlBt@TLfAn@d@\\ZTz@r@xAnAlCrB~@p@pBlApCfBINaF}CgC_BM^?Nk@hAj@iA?OL_@oCyBiCyB}@k@o@a@qBw@uBm@mOwBaFo@GD{@EgGm@mGaAeF_AyAYTiAjJ`B`JvAEZbCXtH~@rDf@B@lAPj@HfBV~AVv@Nl@RlBt@TLfAn@d@\\ZTtCbClCrB~@p@pBlApCfBfBhAfBvAv@dA`AvALRxFfKpBtEb@fA`A~Bv@zAp@bAd@h@j@d@bBjAxAv@NH~@f@ZR~AfAh@h@l@h@zAlB|AvBRV`DlEJJb@f@jCxCVXvAvAlAnAd@b@|@t@hBlAbD|AzAt@\\RfAp@\\VnBtAXRhDjCvAfAr@d@vIlEb@TpAl@nAp@bBfATRZVf@d@fAlAbDzErIhMdBrBdB`BdDrC`BhBvDjFLP`AdAzApAlBrAvHnFn@f@dBxAJHNJHHnExC|IvGv@h@zA|@|@`@~Ad@\\FtBb@xA\\TFbA^|@b@PJtFzDdA|@dApAfAfBrA~BpDjGdFtIx@lAz@`ApB`BtA`AxGtEf@\\xAz@jCfAbJ~CtChAnCfARH~@h@`@XpCnBfAf@v@Rb@HxARbCXt@JRBnBPr@HnBVvB`@z@Zr@f@|@dARXfA|Ar@p@HD^Np@Jv@@r@Mt@[b@]\\m@Tq@XwATq@NWhAgAj@[r@Qz@Ap@B|@Jv@\\hF~DhCnB|@`@x@Dt@GbEeAZEb@Ap@FF?f@Pf@`@V`@`@bApA`Cx@`AvBhBd@VNHvAl@hDz@VBdAHnACxAIpDUvEQbB?F?rBNRBP@hEd@rADx@ATAbAMr@KdAQdAQzF_AxBk@l@S`Bo@z@a@`@YhBiAtB{ApBsAh@_@PMj@]d@_@z@m@hJ}Gx@i@lBiAz@k@jAs@n@g@TY\\c@l@oAZaAHWBITiAD[T}BPwBL}ATqCN}AFq@RuCXwCT_BRcAXkAnBuFhAgCp@oANUx@_ApAoAxAcAhB}@vAg@l@Od@KhBYtAMt@CjDIlDO~DOzAIpCIhBBnAJzAXb@Nv@XnAj@xBfA|Az@~B`AhEdB|Q|GhC~@xBdAZPZPtEhCjB`AhB~@fCnAtAh@tA\\TDdBT`@Bz@B|@?z@C~@GlB[ZCjAOpFa@B?tBUvBa@rA_@x@[^OvFoCjB}@|CyA`Bm@nBg@fB[vHu@hCe@bAUfAYTGRGfC}@lB{@lCyAjBiAd@[tB}AzL{K`C{Bl@m@dBqB~FcHt@gAd@u@v@sAj@qAt@mAf@q@j@s@^a@\\_@dB{A`@]TKnEsCzAaA`@U|CgBPOFEd@i@PO?Az@u@xAwAdB}AtCsC`@a@|@gA|A}BfGkJ`AwAbAuAl@o@`BsAxA{@TIfBo@r@QNCdBUt@Ev@EjCCn@AN?v@?lFMpCStKwAx@Ql@QnEaB\\Kz@UTE`@Gd@ExAGfA@Z@dBPxAVdBb@v@Vh@RhAj@fBlAbDjCnIdHpExDnFxEbCxB~MvLVTbE~CrLbI|AfAtCpBPLf@\\NH`BfAbCfBHFvA~@tHdFdB~@XLlAd@rBl@dCb@nIlAlEv@hC`@nARbC^bC\\hDd@|@LfGx@h@FjCVfBJp@BN?pHC`CAJ?v@AxACpHJH?|@@l@B|DLR?pCA|EOnCGrAEV?lHPnADr@D`@D~BPbAHfBL`DThAHvCTpDd@zCn@nDdAxBt@~@ZjBn@nBl@bAV`FlAfHfBlAZlCr@jEfAbDp@j@JpGtAbBf@vAh@nC`AbA^v@Xp@TXJVHr@Rb@LlGdBvCz@`AR~A\\~Cj@`BXnEx@zAXzAVhB\\|AX~B^dDn@j@J|BVdG\\xEXnMv@bADbCApBEdPc@pEKtAEfKWzDIlBI`@A\\A`@?PAj@C|@CF?lAEl@EpGk@zC[dAGnB?r@DlAPzA^fA^`Cz@rDpAv@Xp@VfA`@fC|@xAf@^@RDhJrCnBj@LHB?RBl@JZJ|EdAx@Px@RvBd@nB^~AXND\\b@jLlC|Dt@vGjAdKbCxBh@rCn@vCv@zBh@xBd@pBTdB@b@CNA~@GN?zB]pLqBf@StEWt@?lA@vFPbENdEJ~BHbBDxAFpBFjGT`J\\lJ\\tABdBC~@I^M~@W`B{@\\SZUPO\\]X[bEsFn@cAXg@r@u@XYjB_Cb@i@|@}@TGxBcCvA{AtEyFpBkCpAwA`JwJl@m@V[p@s@hBkBrBoBbB_BnGgG~@{@l@o@rFqHlCqDrKcOz@mAvC}DtEmGl@y@bCaD\\c@pA_BLOzEaG~DaF~AqBt@gAl@gA`@aAJUfA{CxAeDjAyBhF_Kp@sABIz@mCTo@rAwClEmLdBwDvAqCh@cAFKrAyCVe@Vg@~D{HNWTc@fAuBd@}@`@y@Xk@Xk@\\m@hGiLtDoGXg@n@kAdC}E~DgIl@qApCkGXq@Xm@\\w@h@cAN]`@m@`AmAf@c@zByAbBy@\\QzC{AjCoAhGcD~C{A|CgB~@u@DCXWdAsAbAoBRk@BGf@sAd@mAHy@Rg@zByFd@kAFOTg@n@yAh@sA@CDUt@mB`@_A^{@LUj@y@bBwCtB_Eh@y@z@uADIlBiCr@Wb@]dCaBvA}@XUb@a@t@}@~BwDPYP[|AmCr@mA~AaCt@cAr@aAd@q@t@aAtC_EJKx@gAl@u@n@y@hBiBx@o@^WLIb@YFEz@c@|@c@zBw@z@Sd@M|B[^EbAGlAEhC@xENv@B|@DzDLtDL`BJvB^NBPDvBl@n@RrD`B`@V~@f@rBdAfAf@fBz@jFnCj@X`Bv@rIdEb@VbCnAZJb@^hBv@lBp@~A`@xBXz@D`A@n@Bx@BpDNP@N@R?fGXjMf@l@B|ETxLj@`CJrEPnAFjADbAD\\BnI\\tEPZ@R@~@BrADbBHtBNv@FtAH|AFNMh@@lDBfBEp@CVC`@CLC\\MZUR]Ha@@YGc@Sc@SQg@QWCM?o@Ja@TSTUf@Kn@I~AGhACh@El@?JAnAHr@Pj@T^`@`@\\RPFp@NL@v@Bh@?pB?lC@fMB\\?rI?nE?xEDnBFVBXBd@FzB\\bB^~Br@pAf@zC|Ax@h@r@h@rAhAt@r@\\`@rAdB|@vA|@`Bt@dBvGfQn@pAh@|@hAzATT~@|@z@l@bDdB~Al@tCv@d@LjQzE`AXnBh@rBf@`Bb@x@XvL`DhDbARHpAj@f@TTNTLb@Xr@f@dAz@dAdA`BxBdBhCx@|AdA|BTh@n@rAr@zAXd@JRh@v@\\b@h@n@tAlAvCrBx@j@fA`A`AhARVrBxCvAzBz@jAhFzHlBpC~@rAjAtA^b@hAjANLXV|B|BdAdAvCpChAfAl@n@vAfBbAzAlCbFLZNXd@hA`@jAV~@h@`Bn@tB^nA~@rDpArFDNzAxExArD`B|D`AvBTb@^t@l@~@^j@LR`BrB|@pAPVzBdE`A`CPb@Vp@HT~@`CVn@Rd@lB~EjAvCd@jAb@jAXv@ZdAp@vBlDlLdAhDh@dBTn@jBpFZbAzA|EfB~FL\\Tp@xCdJ~@pBf@~@HL\\l@pAjBdAlBlAhCf@nAV|@Pd@fC~HlA|CtB`FlBtE`CvF|B|FrC|HZh@\\l@RLZr@\\`AbBxETj@h@xAb@lAj@`Bf@vAbAlBz@vARj@Hd@\\l@hBrCJNh@`AnErHb@p@p@hAfD|FlAfBl@t@rFtGV\\hBxBbFlGhDbFtA~BtBpDNTrDnGxEvHnCpE`B|CVf@dB|DnAbD\\~@pDjJVj@f@vAl@bB`@tA~CtL\\hAJ`@XbAJb@J^XdAf@dBJb@l@`BHRBHL`@L^p@tBnAvDb@zAl@xBTt@b@`BZdAfAfDn@hBh@tAlBpEXn@|AtC^x@v@|AtAnClCbGd@`AVd@j@hALXLXbAnB^p@h@z@x@pA|FvJZl@~@~BFNDNhA`E|BbJ^|Av@|CRz@VnBFf@HbAH|@PdBb@dDJj@d@bBTr@|A|EzAhFb@jB^`BLh@\\|AjAxENn@r@xBz@~BvAdDj@pAz@xBTj@Nf@r@nBZ`ArBpGb@hA~@dCt@jBnCfHl@bBpCzHtA~Db@jAVp@`CxFDJDJv@fB~A|DbA`C`AzBfAfCx@|Bx@vCd@jC`@~DJdCBrB?l@?xA?h@?x@Ar@EvB@v@?J?HBz@LrATnAZz@v@|An@z@t@r@VRn@b@rAp@B@fCfATJ`DxAnBhAjAz@nAjA`@b@b@j@t@bADDjAxB`BnDjBvDj@jAf@~@lBbDj@dAjHpLb@x@~HtNh@dAz@dBVd@NVRXl@fAn@lAXh@dDfGR`@PXp@r@JV|@zCZlARn@~CjLh@fB~BbJv@vC|@fCz@rBtAlCXxANd@nEnJpAdCdAzBp@tAlBjDZf@`AvAFJv@Yw@XbAvAfApAVZnA`BhA~A|@tAhCdEnArBTZNTzAtBxBzCxBnC~AtBrEvFpB`CjAlAv@t@nDxCDDZXpCrC`@`@fAhALLpIbJRT`@b@d@f@pArAh@h@t@l@|AnA|EvDVRpAdANJhErDhAjA\\b@|@nApAxBhCtEVd@n@nAdCvE`@t@nAzB~BlEVd@fAxBv@rBp@lBlAbDbA|CRr@ZlA`@|BT~BFtABt@?jAOlDEx@WvCSzCo@rKm@bIAPK`BKxAMdCQnDI|AQrBY`Eo@jJk@rIu@vKGn@GxAcAvPCl@AxBDxCf@~Hl@lI@F\\zD`@zELvAJxADf@HpAPdCHlAf@bGZjElAhQ\\xFDn@PbCNvBDv@L`B^bFXpCd@tCr@vCv@~BbA~B^t@fBhD@?\\f@HNdBfDfAjB^p@dBtCX^|BzChBvBr@v@xF`GpCjD~NrSt@bAlIhLf@r@tDbFVZnCdD|AdBv@x@xHxHRPxE|ErB|B~BzCfBdC\\j@fBxCbAtB|@hBpAzClC|FbB|Dn@jA`@f@p@~@h@n@fBfB`BpAdCxAf@Vz@ZpBp@vNnDtHfBVBNBvBd@pBj@~Bt@lBz@xChBZRXTvCxB|@p@nBzA`Az@jAtAdAzAVf@^t@Xp@`@bAVz@J`@DP`@`CPzBBfADpBJlBTlHBp@JjBFlBFnGBxFDhEBhEBvA?zB?~F?zL?XJtBNbBnAnITlC`@lK?~B?b@I|ACt@I~AGjB?nB@b@DlAPhC\\jFRnCJ|ABd@@x@F`GBhED|FDfGBbA@XBb@RjBZ`Bf@dBN`@N^h@jAd@x@|@nAbDxDzAvBfAjBv@|AZl@Zp@l@jB`@jBNvAFlA@`@AfAGnAARYnBe@jBm@zA]t@_ApBc@zASdAGZGv@Cd@Cj@@hAb@lHPzCBpA?bA?h@EpEA|FDbBFlATjB@LDTVrA^vAt@lBjAbCbApBx@`BN^v@nBX|@Lj@h@bDF`@x@tFF^Z|ALj@z@fDZhAv@~B\\bA\\pAXvATzB@RDpA@\\CtAAXCx@WxDEp@M`BU|CCPGr@APCd@EdACrBCzA@pA@xABxAHjELbCFn@JtABf@NzCXlFFpAJpEL~FTlIBfAJtA^zCJ`BT`CHdAHlAD|A@d@?fF@h@JxB@ZLnBHz@Fz@JxAVnDL~BDp@t@zKDxBGzG\\pAArAF|DPrF\\`DFf@H`@BJhArExAvDN^^f@fApBp@`AfAtArDpDxDrCt@j@LHVt@vC|Bf@`@RPpBpBj@v@b@l@PZz@bBb@`AVl@p@`Cz@jD\\`BR|@~@`E`@dBpBxIh@zBt@zC^|Ab@lBl@lCj@~BHZv@dD~A|GZpAHZb@bBBNdBlHdCpKNn@`@~B^rBPbBF`BItBIfAStDkAnSg@`KARGvASnDGj@Cn@GdC?p@Bx@NxABPVnAd@vALVx@pAj@p@z@x@NLn@b@NHRLr@\\bCn@x@Lp@H~@DhBC~@Gz@KtF{@fEq@lGaAjD[l@ElBKt@Cf@CnHa@zBKtB?\\?vADZBbADPBvBXfB\\n@NtA`@pAd@dBx@`B`AnAz@TRfB`B`BjBrAtBx@zA~@|BBHt@`Cn@tCb@xBnAjFvA|G`@fBJb@Vx@rArDfA|BdBjDp@zA|B~EnAjClB|DbG`M~@vADFJJPTFJPVNR`BjBxAtArAhAxAnAVR~DjD\\XjC|Bx@t@`BpAhCvB|AdAx@d@t@^nAf@b@L|Bt@|Bj@lARv@J@@|BPlCDhA?|@ArBKzBWjBYl@Or@QbBg@fBm@j@UlYeK`@M`Ba@rB_@tBUtBIhCAtBHVBx@Hp@Fz@Jr@N`@HtA\\n@R|@\\@?xAp@rDpBr@`@lB~@\\PpCzAvEbCfE`CNJXNnBdAhDlBvB|@hBh@zFrA~A`@~A`@z@PhKbChFjA|Bb@xARtBXvD\\nEVpFPnBPpB^nFlAdJrBxCh@TBzAXjBPzARb@Fp@JlEl@jALxAVzAXdB^vG|AvA^hEdA`AX`D|@fA`@v@^r@\\\\RhDvBr@d@jC`Cb@`@Z\\d@h@pAhBf@|@lAjCvAdEjAdEnD|LvAjFl@~Ah@dA\\n@`ApA~@dArAbAtA|@FDZNv@Zb@NdBf@VFrAT`BNfBHbIDp@@lLJ|GDnDDfCJXBbALTBdB\\fAZ`@LfBp@fB~@j@\\h@^h@`@|D`DdB`AzE~BnAp@bAh@lBrAtAlAlGxG\\^TZRVZb@|@xAJRd@|@bBpD`@v@fBxD|B|EJRt@tAtHbNj@dArEhI|@pAd@l@NP~@bArB|BpBzBpAvAbAjATVNN`BjBz@z@~E`FHHxAjBnAnBpEjJZl@dA`BrAbB|A~ARPrA`AhBhAzF~C`@Vx@d@DBjCxAxGrDfE~Bt@b@l@`@xBfBdBbBtA~ApDlFNRZf@Zb@vG|JlFbIjGpJz@|An@pABD^XPH?@z@zBz@hCx@nDVxAJv@Fd@JhALtB?V?@JP|AZb@Pp@V`AVnBXtBX@@hCj@fAT^B~@BjAFb@Bd@DH@jCVnCJdADt@Bh@@d@@l@?F?d@AVCVEDAHClA]dAYBAb@KfAUDAl@Ax@Fx@DVBl@Bx@FR@lBLx@Bv@Br@QXA@GFGNC@?NF@BBLAJv@nBr@dAr@r@tA`At@\\PHZL`ANB?zEb@`ADzAHZ@r@DFCHAHBDBDLAJCB\\l@Rb@^v@d@z@`AfBT`@lCxEBHFT@?F@DBBNABLN|@zA\\z@d@pBLn@FTjArEPn@|@xDb@`BZpA|A~FLb@h@hBPb@l@xAXl@Pb@fAbCf@dAr@fBJXb@jA|@`Cx@hCL^fBjFDHn@rBj@pBPx@Fb@Lv@z@hGPn@Lb@^pA`@nAlAfEbA~C~@vCjA|Cp@tAl@|@x@~@`A|@XX|@bA~O~Op@j@`AbA|@fANT@?H@BBHL?HCFA@rA`E|@rDBRV`AJf@FRJ^FN\\`A^v@RXpAnANNr@r@zBzCd@r@pAnCDHDDr@xAFxAfFnKdBvDf@xA\\dAFVb@vAPb@HTt@hB`DpFzAhCbAvBdAvCHV`@|ALh@d@fCl@xE`AnHBVTnBv@pFrAnHhAlGBFhD`S`BnJn@tFJt@PjAFTr@~BvA`Ed@`BvAbFbAjCb@hAP^Zl@zDzHpF`LJRtAdCxBdD|BvCVZjCzCb@`@z@z@xA|A~@jAdA`Bj@bAR`@z@tBp@zBd@pCZlCFlB@zBAf@MfCAXUlBQbAS~@Md@g@~AiAlCMVi@~@q@dAy@fAyArBw@zAEHy@vBc@bBKb@W~BIz@GtBD~BPbCNfAJl@FTf@lBr@jBz@~AfA~A`D~DbA~A~@hBt@rBr@dCf@rCJl@PdAp@xDj@~Bb@xAjD|Jz@~B~@dCv@xBpAxDPh@bB`GzAfGVhAJb@R`Ap@bDRnAd@zCtAzIh@bCl@hBPd@v@lBt@tAt@hAb@j@lBvBzEhEb@^hAbAf@j@VVx@dANVx@tAR\\P`@r@bB^jAt@jDjAxGJf@ZtAZdAL^N^p@|ATf@dAhBz@jAVZFHnArAtApA|BlBrAtA`@j@TZVd@R\\~CdGzEfJNZr@hB^nATz@\\bBZdCd@tGNnAh@vCf@fBFPt@jB`AhBzAzBDD`C~C|BnCzBjCh@j@pDrD~FtGnGxHnBhC|C~DhAzAlApBv@~APb@v@zBp@xCZzBJv@X|BX`BNv@x@xCt@xBTj@r@`BlAzB^r@rArBpBnCrGdJPTfEbGvArBn@dAnApChBlEfBjDN\\rCbGj@rAl@`Bx@`D\\hBRjBLlC@~AEdCOfFOdEQfFm@|T?VY~JGvAYzHI~ASxB]fBOn@c@rA{CpIM\\u@tBo@hBwH|SgBhFgB|Ek@nBi@`Ca@|AqAlGsAxGI\\I\\}A~HMj@[nBMfAc@tHAVMnBk@vKGnB[xQKdJCjAE|BGpFE|BElBMnFEv@In@ABWnA_@fAQ`@]n@gA~A[b@qFbH{ArBcC~DcFxIkCvEoA`CKPgBdDqAtBiBnDiAjCuA|DGTuAbFu@jDUnAIh@w@~FM|AI`AKlBEv@Cl@GrFBfDFfCLvBBb@\\nEZjDRjBZpB\\~Al@dBHTZr@b@v@tAlBtAtAzB~ANFJFlBx@r@TdAXtMtC|FpAx@TpAXb@H^JfAXfA\\`Ad@hAl@bAr@zAvA`@f@TZPVHJn@jAh@pA^rA\\`BPjAL`Al@zFr@vIr@zHh@tED`@v@|E~@jF`BrINv@jAfGlArGl@bDvArHz@zEn@jDbCvMN~@Fj@Hn@`BfUb@hHDl@Bn@DrBExCKvGUlLVd@C|@CfAElBAt@IrE@vA?p@DjAHbAL|Ap@dEx@dDb@tA|FvQTd@^j@N^HNUPuAd@ORGNCBMFOVE\\?j@Dp@B`@@dC@t@C~@EfAI`CCfAALGhCGlBKdEBdAFj@Hd@@DH^@D\\fARj@^z@h@tArAhDd@`AJTRd@n@dB@Bd@v@`@p@pAbBbAhA`BzA~@|@lAlABBt@n@h@b@VX`@b@`@d@DFvCjD~C~CBBxAlA`ChBhAz@z@p@n@f@HCD?LFBL@BADb@hANbA@FFn@@PHfB@b@D`ABhABrBAXIlAMdAAFa@bC[vBEXYzBGn@M|@c@zEMtBIfA]bCCJ?Da@`D]tA[lAyA|EcAtCk@tAc@~@g@nAiAzDSj@c@`AKLWZ[b@e@l@y@tAUp@WjAIbA?z@HfARfAXz@Zl@lAnAXVXVx@t@x@x@tB~Bl@z@`BlCFFnA`CXj@Tj@^t@rAjC@@BHKNA@{AdDy@bBGHWf@MVa@n@MNm@p@KH_BrAy@j@UNk@ZmAd@KBWHiCl@aAXa@LuBt@eBf@y@LW@s@EoAIOAi@?MAe@Cm@BaAF]@UBeD`@sC`@g@Fo@RWJURm@f@w@~@CDEHo@jCU~@kAvEg@rBc@lAYnAMd@CHWtA]zA}@`EOv@QjAEVWxBAPEzAEvCMx@q@pDkAnGGXIt@OpAEbEBbCJh@FN|@tAhA|A@F@NEj@If@a@zBKh@i@lCMl@UtACb@ClBBhCEp@G\\Or@}@tDYrB?@IdAAFIr@KbAEb@QtC?Z?bA?Z@vAArBKnDEv@ETY|@sAtCOXS`@Sd@sAvDq@nBs@pBSpAQtDA^QbBi@|FOv@O^_@v@eAzBUb@q@hAiAhBGLaAbB{BfDU`@U^[t@Sv@a@tC[nBEVi@fDOtBKbBi@vDWlBQrBc@zDKvAInBC^IdAKpBK|AARMnBAb@KpCKlBGdA]vGIpCAj@IzA?LStASfAOz@m@tCu@bEKt@O~A?RE~@Cv@IbCIvAG`AYzDQjBAZAXQvCShBMn@Id@_@v@Qb@Mf@El@@\\@d@D`BJnCC|@?r@C|AO~DO~B?FMlDATUvCSvBcAhHoB~J[xAeDbM_@fCALYhFMjAObBGjAI~@On@kA~Co@xBUp@aA~EAh@I`@Ut@_A`Dm@bC[tBI\\AJCZ?LEpAAf@GjAAJAj@?nCFhFG~AUbA?@kAvC[pCEj@KjBKhCEvAAr@G`ACh@ABOfAAFUdACb@AJC^An@?NAp@@PBdBEhBIr@e@`DQ~@i@nCI`@SbAIj@q@dHe@lDMr@SrAc@zCMz@o@nFW`CSbB_A~HSxBEx@Gv@E\\QzB{@|ICZ]bCIj@]lBo@|CgAbEW`BOv@Ib@]tACJKXWl@_@v@wAzCUd@M\\Qh@Or@I\\u@`FObCM~AG~@C\\Kt@CTEVCFe@pBMdAAFCTMnCAJEn@MhAO`AABYtA[~A_@nBGt@Qn@a@fAaAfBg@z@]d@YZGHo@z@cApBa@hASb@EHi@r@_AfBgAfBa@d@ERu@pAU`@u@nAm@dAOTMVw@tAOTsA~BcAdBOVKp@QtBGh@c@nBI`@SbAKn@o@xDYlB[xBCPYpCSzAMfAQtAM|@M~@WtBCJANIv@In@ARAJE`B?~@Dd@J~@@p@?ZIfA]lB]jBCB?JEd@E~@OzDAz@BVQTG^AxA?tABp@Dp@R~BRfBLdBDb@h@rGn@fICf@AVIZm@xBsArDaDlIeA`D_@bAENEXErBCnBS|Be@rDCRc@|CCPQjAG\\S|@UbAQ`AY|AIf@oAnHI\\[|@KX[r@}@nBy@|Au@`BWv@_AhD]`BSx@m@nB_AjCo@bCGVOj@I`@_@tB_@zAKZ}@jCOh@Kb@GVMj@QnAGZg@dCYz@e@lAADyC`I_@jACLe@hBY`AITo@vCUpACNIp@o@xCCHSr@u@xBa@hAIRc@bAYn@MPy@fAwBzBwDjDsAlAyAjA]`@OPe@n@Ub@o@hAS\\gAfBSV]x@m@jCc@hDQf@MXYd@q@tA_AvBq@zAe@`AUt@aArDGTcBtEkCdHEJoBhFYv@]dAm@~AEJcAfDe@dBO`@MZiB`F}AbDEJc@vAOl@}@zBaC`GGN}AnE[bAGX?Tw@zAmAlB{@fBGPK^g@xCe@nDI~@G`A?JEdBGf@ENENg@tAWl@Sb@]fASj@CFe@~A[bAc@hAKVO`@sCnFsAxCIPSZSVq@t@wC|Bk@p@c@t@[p@aAvCk@tAOd@Kd@IvAE^e@lBw@bCa@rASt@e@xBU`AYdAuCrKs@vB{@rCw@rBUj@eBhE}BxGu@|BYdAQdBm@~DWbCKd@qAzFsChKUlAs@rFA@OlCG~AKzAStBYvB?BEj@AZ@jFShDGt@IbAGb@QdA[xA{@dDk@zB_@hBiBjIc@nBa@bBS~@Mh@UbAKb@_@|AMh@GRI^[nAeAhFy@dE_@bBGr@GxFOvBIp@Gf@cA|Fe@vCMz@En@MpCKvBKnAUnA[jAMTQVgFzFw@|@oA`Bs@fAeBdCQZiApB[h@[d@U\\k@fAsAnDaBbEy@rBUh@_@z@_@jASr@q@jBOb@]bA[|@a@hAkAbDi@zAg@~AwAdFSl@KVaApBo@vAABuBzGe@|Ae@hBq@bCGXa@`BGT]lAOj@y@zCGPITWf@eAbBaBtB}@lAORQVwAnBSXCF{@rAYd@i@~@Ud@w@|AGLKR_@r@ELg@fAOXeAxBc@|@OXc@|@KTaApBCF{@fBSb@a@|@mAdCe@bAgAtBYj@Yl@MVe@`ASd@Yh@a@v@KPGLGJO`@YdA}A~EAF]dAQn@GNg@dBKPeB~CWb@QXeAhBKNGN_@l@[j@q@fAINg@z@m@bA]j@U^MTm@`A}@zAaA|AqAzBW`@qA|Be@x@[j@_@n@m@dAw@tAm@dAO`@]zAI`@S~@K^O^k@|@CBQRg@d@c@ZSTk@f@_A~@CBQPyAzBc@v@GLS\\k@z@gBbDYd@GPKRy@nA[j@MT]p@{@r@oAfAC@_ErDiC`C{AtAYVURs@p@i@^OHuBzA{BbBSLgBrA_Ah@i@Rw@Xg@JmAV_Fx@q@PYDYH[P[Ra@r@a@t@AJu@zASVuCzD}ArBoBjCgBdDWd@GL]r@KToBbE}AfDi@fAeBxD_B~Cg@dAg@z@y@nAw@nA{A~Ba@j@}@xAa@j@yBnDq@jA]j@mBfDg@~@S`@e@~@uAjDoBbGsArDQ`@y@`Bq@tAiAvCi@rA{BpDOTwBbDgBrCoArCs@tAm@z@cBhByApAiAbAY\\WTOVYp@Ov@w@xDqAlGo@bCWn@a@xA_@vAc@f@IJMt@@RDp@AVIn@_@~Ba@hCMvAEnCFlCJvAf@tCT|A?@LvAG`DM|AIh@sBrGUd@k@p@m@j@oAlAaAtA}ErGcAbBaAdB[n@Oj@Gj@QbCa@hDKrBIZQb@U\\o@v@q@j@q@j@w@`Ay@n@kAl@SRKNGPKr@MdACVOjA]lC?DOv@CJo@fCUfBGpAElAFnCJ`FB\\Pp@JTZhAB\\^dITpDVdDBP\\nCHb@b@jC?@?b@GHA?Y@ECGCW]eA}Ae@e@a@_AQMqAk@OOM[i@q@US}@gAUGqAGUIEMAgAIWWSKA]?mAKa@K_Ag@m@OeAQg@K]MgAa@gA[g@_@sBu@IEiAi@i@O}@IEAmAXSJ?H@DF@TKVKJAr@F`@Jh@Zd@\\nEpEj@\\f@Nv@r@Vh@dAfAHZAd@C`@@b@@@LLNBr@AF?XDd@Pf@\\RFb@Fp@BNDLTDTDlAAXGdABf@H\\NRl@ZJP@H@Z?Bi@z@I\\DJh@d@BPALm@fB@HJLPBl@EVNb@XBFDXDvAFzABpAATC^Q~@WV_Br@yBx@AD?DBLB@~@t@DNADELk@^_@NA?c@b@a@d@k@bAET@L?LDNFJNFXEL?L@THZVLVDZ@TFTJPNL^D^?D?PBb@Rl@Lr@@~@IPDBDBbADVf@fAPFP@PJDJj@z@NRDVAHc@v@GVDNVVDLBZIf@@dACTELEJ{@b@aAj@KLQ^WPWHOBc@CWAOBm@d@mAf@s@AWAo@WGAQ@GDaAfAs@`AMZC^?V@HHpARbALRNJZH`@Jn@H\\@j@CVDNDT^FVJFPBbBLXNFFFR?X?BEPQZ[XOHsBf@GPLv@APIRQJ_A\\S\\?NLLTD^E^BZNJPJXBV?PMl@Eh@@N@Lb@TVFVNDJFz@@b@R|A@R?^GRWTWJc@Fa@Ac@QCA]UIAS?K?[H]BiAOmCYoAQoBAQBu@Fs@V{@JmATEBsAl@IFKJKN?b@?f@XJR@@@f@LPBvBVr@Ll@ZJNFVSHK?CAqAe@m@Ec@@YAU@EFDHp@NXTDJ@DCPA\\Ff@JTTL`@FRHNTDj@?l@DVFNTJR@^KBEXWVBJHFL?B@PA\\Er@TZHFFL@\\Ah@Fb@J`@PPB@NFd@CLCj@WPMHGb@OLEh@?TDZ@Z@LHDLE^S^C\\@ZGjADLJFf@LLHd@h@b@z@HHFBnARd@ZRVJ^@TIZg@vAKJUFk@JOFgB`@EF?XJPLNr@f@VV@RITCBe@v@i@v@YZQ^@LJDL@xAGb@EL?`@@`@FfAXRJVVDF|@pAFXCROXK|@XfBLR^L\\FRPDPCf@Wx@Sd@gB~A}@b@{BbAQR@ZHNv@h@FJXbATT^Nz@F~BB`@Hl@`@NRBTCf@@b@Nb@X^DLBh@EXK`@KZ_CpCeA|AQ\\EZEhAUd@g@j@q@^o@n@ANNZLFTAPHRXFLXXXHXCh@a@JE^A~BDjADbBTVLDFr@z@|AbBt@f@vA`Ad@\\PJ\\RPRd@r@J`@?\\GLY^CPFd@d@v@JXPvA?N@DEzAKXMNQACKNu@@k@E]KSEImA{Aa@WMGMSKa@CiACIKU_Ay@QM_@M[Bi@VYPQ?MIWWqAwAOIOGi@QoAUq@@YHIBIHGLMLe@Vo@BGHBFLB\\Cf@QROZ_@HE^C^NNLdB~APPJTB\\NV`@FnACN?XDNJDPAz@Ix@GVOl@Sn@IZBf@DNTVTTLf@?JGh@W^}@n@OPQJGEBMTWXQLIR[DW?Ye@]}@a@SMKMCI?EDOV[BYCi@M_@G_@Ia@IGQCI@]@c@CQIWa@GYQYSQYKWBQPa@x@c@TMHAFHFR?BAVIPOH[NSNCNFRZJd@JNdAn@NVBHDb@Aj@GL_@RYZCJAP@NTt@D\\WhACHg@|@UTIZ@ZFRHPd@b@HJBrAMfBOf@@NDHFLd@b@j@`Ad@t@PTr@v@LXNr@Vp@PRTJT@PE`@[t@aAJAJFLh@BJHLp@RHJF~@Kv@Kh@@l@Ix@EhABh@HNXHFAr@MRBLHTj@JZNz@@RTn@Zz@ALG@ICSi@m@cBUm@SSOE_@Ba@Di@NODM?OI]a@]UESFWVk@@KEYCSBYHc@AUI]GIU_@ECE?GHJvA@H@LKZARFn@ETEFY\\CNDNZd@Tl@T^JP^VNBf@I\\BJFVh@L\\Rd@BL@N?pAYlD?b@DTPf@?n@Mh@Df@\\p@l@h@HBn@LNLt@rAHJ`A`AFHDL?TOJGAEYCUMOGGaAk@SSg@s@WOK?W@Q@M?e@Ug@a@U_@Mg@Ug@C_@Ao@]}@[q@Iu@KGIBAJDj@d@rAF\\?f@Ep@Tn@E\\K`@@V@DJP^PXHb@XXPl@F`@DJNRt@Dj@@p@L`@JPJLb@`@`@t@T`@BH@T@|@Jr@LPb@TH@h@@X@LFR\\BL?NAZWfAADDVLLDBTDXEp@KVD^N~AhA\\Lr@Ld@Vn@l@h@p@XNd@Df@Ah@Cd@HJJDp@Cx@M`A@^`@x@Fl@CRMPeA`@EFEF?R?BZrABd@CXQz@Et@Ml@y@bB@XPp@Fp@@n@?HGf@Sf@WXc@Hk@Fs@TiAZ]BoAEc@FOBq@CSDUXKVC^BNJRVTXPRRTj@Lv@Vv@`@h@VDb@If@AZDRFNPLn@F~@z@pAf@~BA\\MVi@l@Un@Kp@A\\JjAEPMHKBgACMBKJCRD~@L|@Tz@E`@IPi@j@y@l@e@X]JUAICa@MUM]EQ@SJSXEX@|@Fp@Gd@KT[LY@s@KOAWFKJCPFbACR]T{@XYL]^IFi@Ti@NMNGb@@PXl@Tr@PX\\H~@Nb@PJNBFDZDhA?zB?ZFr@CHEFIDKOGi@GaBM}@Qc@u@{@QCiBQq@QKEKKe@q@CCUQ[Ce@AMIGOAUHuAAm@@QL{@Pa@PQXI^Gf@QRSPWJ]?QE[]c@KW@YFa@Fi@Hk@BOFIb@i@x@cB^e@Hc@Bq@RcBTaARc@x@u@HMJUJy@@UGg@MU_@WSm@KUQKUE]@_@CSEc@MmAmAQGYCYFMHg@b@s@`@c@NaARoDP_@CUGi@c@UAOBWJQJURc@n@M\\Mt@KVYJc@@M@c@LOB_@MkAq@c@Mg@?o@J_@VWNqAj@k@\\IJQ\\IXKfAFz@GnAIt@i@`BY^qAz@s@`@aAl@a@f@Ql@C`@Cx@Hv@@XEZOt@IZADSl@CPG~@J|@E^Wd@GHKPAN@J?FE\\OVQH}ARw@Ps@Lo@LKHERAT@d@@`@GRGHQJ_@HMJY`@WLq@Dc@H{@X_@Fo@DOJKVc@v@WLk@D_@Ng@V[H]BgABWJ]XSVQ^cA|@IVJZd@j@Xr@@VGf@]bAKTSNQ@a@Eg@Gg@Ms@KcBYc@Mc@McAO]BMBw@p@e@r@_@n@[VEBSDA?MACCYU_A}@OIKEe@CSFWPc@d@UPiAt@a@NQFg@Va@TcAv@o@Vo@VQ@M?UCk@Us@_@MEg@CuCH_AFy@HaBd@OJORCf@Dj@H^Xb@VZZbA`@r@DV?d@ABGHMHOD_BZ}@XaAFqAAa@JQRg@dBU`@MLUTEd@@^Lv@P^@TEJA@KDI?K@sBI[?_@Dg@Le@Rq@p@[p@Qv@Gp@?`@@f@TfAb@vAPZ^`@RHZDTLJTHv@V`@BHD^APBd@Lp@BdACr@GVGDQ@a@MQS[MYES?g@Fq@NSB}AYe@QSKa@e@e@[YMi@YgAYg@CQ@S@]L_@\\QTo@`B[|@[bBKhCa@rFE~A?f@@d@LpACb@I^sAdEs@pCAT?TZlAB\\A\\W`B@Lb@vA@H?FAVKn@Kl@Qn@]x@[l@[^SRa@\\m@Vu@TUNEDITGd@Yj@QPsBnAcAt@[VUXQXk@nA}@lCMRs@d@MD_@N}@Xa@Xu@j@oAx@KNAPHd@b@lBp@x@DHFRFn@Cn@On@]n@YRYFK?q@CMAo@QMEi@GI@yA`@YRi@Na@BUGUSY]OGQ@SJINCRWnCG\\i@vAEX?z@Dh@ChBFNJNh@^t@Z`@Tb@v@Rt@Db@Ab@Ur@a@hAOVWJ_@BkBJg@CKCSEk@US?OLWl@]j@Od@E`BMl@SZUNs@XKHIZHx@Lj@DXLVTPf@b@l@l@n@f@DRC^UdAE\\QlAMf@Qd@WTOFW?EAu@SSAI?UFSHeA`BmBfC_@\\eAp@U^EZ?r@AJGP]d@KPO`@Cb@@LJL~@N\\^vA|B`@`ABF@T@l@E\\SfBOt@c@~@UZq@r@A?]Fa@GWOMIg@?CBCPFj@C\\qAvAO^M^UVGBYFMJKVIv@@b@Jp@JVR`@BTEP[V_@Nw@ZWFu@?u@IyAScA?[FeAl@YZO^ERPlA?HG`@IfAk@bBCL?VDZZpAj@nBb@nA\\|@Dr@?LEv@I\\OPiAZUBm@CWEc@?u@JMHSZYNu@FQCc@Wc@g@]Ss@KcAKi@KgCwAQA]ZGRCRGj@A@[fAOR]P}@JsCNa@DKBCBERBNV^~@z@`A`BXt@T~@TdBJx@Fz@AfAI`@IROPSLSHY@WAGAs@QQ?MJKTAFAn@Of@?NPn@@RGR]\\SLIV@PPNZNFJ?PUrA?`@RhAJl@@VADKv@A`@Ff@f@|AAt@Mz@D`@@NOzA?N@f@VjABJv@rBBj@C`@UdAENM^a@l@w@bAUn@y@vCGJYXg@`@W`@S~@?@KbAFb@VbA?f@E`@IVOXMP[T{@Vi@JsA@eANgD`@kAf@YVi@r@_@NODa@A[KW[_@eAKa@Iq@IIUCiCAW@YFk@b@i@RmBh@S@iAW}Ac@a@EyD^k@AaA?o@H_@JSJcAx@q@l@}@r@qC~ASJyBhAsAt@QH{Ap@cA^qBj@g@JWBI@_A?]Ae@AiCWy@EoAEqARM@gB?WFIDu@hAURUNm@Zu@l@k@h@{Bv@A@e@j@CB{BtDw@zAYb@STuBfBGF_AnAa@|@GZLj@Nb@]LG@c@LMVMb@e@xAw@~@KJo@Tc@LYT[d@_@fAAb@AjAHlCF^J\\FNnB~Ea@`A}CnHc@l@SEwAKIAA?gBC}@Ce@G_@G_@SIIG[?QAgBMv@IvAA^EhAOfAUz@s@nAEZBVd@fBTtAIx@K`@UPMByCFc@Bc@Fs@\\YTk@p@a@z@GJuA~CAf@?h@BXDV\\d@V`BHn@QQOm@Gs@MSa@QiA]UQKWH{@?MSO]QOYG[?UBMJ[lAqBRcABg@BQJWT[?EEGg@@g@Eu@SY?UDQFEb@CDO@o@C]HMJOh@CL"}, "description": "Varthur - Sarjapur Rd", "warnings": ["This route has tolls."], "viewport": {"low": {"latitude": 11.187712699999999, "longitude": 76.69615639999999}, "high": {"latitude": 12.932161299999999, "longitude": 78.2268357}}, "travelAdvisory": {}, "localizedValues": {"distance": {"text": "406 km"}, "duration": {"text": "8 hours 34 mins"}, "staticDuration": {"text": "8 hours 34 mins"}}, "routeLabels": ["DEFAULT_ROUTE"], "polylineDetails": {}}], "geocodingResults": {}}