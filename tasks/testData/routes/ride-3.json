{"routes": [{"legs": [{"distanceMeters": 21272, "duration": "2291s", "staticDuration": "2291s", "polyline": {"encodedPolyline": "sx|mAe`oyMKPb@Zn@d@l@`@LHp@h@d@Z|@j@h@`@h@\\fAx@B@LHf@\\PLn@b@VPRN`@Xl@`@PLDBp@d@XRh@^RNf@\\^V\\V~@l@PNJHPJjAn@FB\\NXLd@Tz@`@n@XRJFBLFn@\\ZNx@\\TJPDt@RNBn@DX@V@V?bDDT?rCBL?p@@pA?j@?h@Ar@AfAAtAEd@G|AQz@SDAnB}@zBeAdCmAbFaCnAm@zE}B|Aq@|BiAHE^[PQf@o@fAuA\\a@dB{Bj@u@`@i@t@_At@aAfDoEl@y@n@{@j@y@zB}C|@iAf@c@n@SxDu@lB]nAUr@OPCnCi@|AYl@MjB_@|A[fAQjDg@`BSd@ED?x@G`Ge@`@ETA`@CNANC|Ec@z@IbAKbCUn@IfD_@fAOzEq@rEo@d@KHAbDe@|@IrFq@LAlBSTAv@I\\GHAREpBa@j@MHCVIx@WvAe@r@UhAc@`@MDANEHEv@[\\Mr@U|Ai@TIz@UHCtA_@x@SbBc@H]TeATaAT{@DQJa@F[Jg@BIT_ADOPu@FUR_Ap@cDRcATiAd@gBLa@Rk@^y@JULYVk@nAqCdA_CVi@xAiDVk@v@gBb@aALYjAeCtBcFh@oAPa@N]Te@\\o@DE\\WZMf@KRGFALClBW`BYn@KFApAOdAOb@ILAxBa@zCe@bAO\\Ev@M|AW^EdAGf@Cb@ArAA^AV?dAA`@?t@?dA?X?^?F?~@I@?j@MPGPGPKbAu@t@m@~@w@DElAeAzBiBhA}@XY\\WTSvBeBXWNM^[`Ay@VU|AmAb@_@^YNMzCmC\\WLQVs@Hg@@KVyBVaCBSXiCTqBDa@Fg@@KJqAH_ALgCBc@?ILcCJuAF_A@WFwA?k@?q@Ae@IyA?c@AmA?_@A_@CoAEaB?QAYCcAAa@GkA?YCIBCDAB?f@@bI~@l@HVD`@Fz@Hl@Hp@Jv@JVBF?vEf@j@FnDVtBFt@?nBB`@?`EDj@?`BBhDD~@@`BB^?nGHvCAJA`@E|Eo@`Ca@fG}@|Ba@bLqB`Cc@|A[xAY\\GfB_@|KoBtBYh@INA^E~AKJAjE?v@@fBKnFa@hGe@h@Gx@G~D]j@EtBUvBKzBKlEUfAIj@GrCErA?`BNTB~@JlAPvARdAN~ATj@HvBTj@HfBF`@@zFPzAD|GRB?|@B~DJ`EN~AFJ@vFTjBJfDVnAJxGn@fANtHfAvIlA|Df@dAJ|BR\\BnCTZBb@BhL|@XBnBZRHfAPPDd@NRHl@\\\\V~@hA@@t@jAlAhAj@^zAf@jB~@v@VfBJl@Jz@NvAJfAVzBt@dA`@B?VHRHTP`@Zn@n@JJ`C`C~EnEJHbBzALJhAv@FBdG~BhBr@jE`BNF~ElBfBp@zCjAfBp@ND~An@tChAnFpBp@T`Ab@VJj@XTHt@\\p@ZfAd@vB`A|@b@|@^XDl@IPIJOd@yDD_@j@iFFm@L_AJw@TcBR{@FUJWNGJ]p@mB@AXcAj@eBDQx@qBd@cANWFI"}, "startLocation": {"latLng": {"latitude": 12.9321025, "longitude": 77.7422734}}, "endLocation": {"latLng": {"latitude": 12.7766925, "longitude": 77.77766280000002}}, "steps": [{"distanceMeters": 12, "staticDuration": "4s", "polyline": {"encodedPolyline": "sx|mAe`oyMKP"}, "startLocation": {"latLng": {"latitude": 12.9321025, "longitude": 77.7422734}}, "endLocation": {"latLng": {"latitude": 12.932161299999999, "longitude": 77.7421804}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head northwest toward Varthur - Sarjapur Rd"}, "localizedValues": {"distance": {"text": "12 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 6113, "staticDuration": "691s", "polyline": {"encodedPolyline": "_y|mAs_oyMb@Zn@d@l@`@LHp@h@d@Z|@j@h@`@h@\\fAx@B@LHf@\\PLn@b@VPRN`@Xl@`@PLDBp@d@XRh@^RNf@\\^V\\V~@l@PNJHPJjAn@FB\\NXLd@Tz@`@n@XRJFBLFn@\\ZNx@\\TJPDt@RNBn@DX@V@V?bDDT?rCBL?p@@pA?j@?h@Ar@AfAAtAEd@G|AQz@SDAnB}@zBeAdCmAbFaCnAm@zE}B|Aq@|BiAHE^[PQf@o@fAuA\\a@dB{Bj@u@`@i@t@_At@aAfDoEl@y@n@{@j@y@zB}C|@iAf@c@n@SxDu@lB]nAUr@OPCnCi@|AYl@MjB_@|A[fAQjDg@`BSd@ED?x@G`Ge@`@ETA`@CNANC|Ec@z@IbAKbCUn@IfD_@fAOzEq@rEo@d@KHAbDe@|@IrFq@LAlBSTAv@I\\GHAREpBa@j@MHCVIx@WvAe@r@UhAc@`@MDANEHEv@[\\Mr@U|Ai@TIz@UHCtA_@x@SbBc@"}, "startLocation": {"latLng": {"latitude": 12.932161299999999, "longitude": 77.7421804}}, "endLocation": {"latLng": {"latitude": 12.8833302, "longitude": 77.7524057}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left onto Varthur - Sarjapur Rd\nPass by Chrysalis High School, Varthur (on the left)"}, "localizedValues": {"distance": {"text": "6.1 km"}, "staticDuration": {"text": "12 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 4739, "staticDuration": "530s", "polyline": {"encodedPolyline": "ygsmAq_qyMH]TeATaAT{@DQ<PERSON><PERSON>@F[Jg@BIT_ADOPu@FUR_Ap@cDRcATiAd@gBLa@Rk@^y@JULYVk@nAqCdA_CVi@xAiDVk@v@gBb@aALYjAeCtBcFh@oAPa@N]Te@\\o@DE\\WZMf@KRGFALClBW`BYn@KFApAOdAOb@ILAxBa@zCe@bAO\\Ev@M|AW^EdAGf@Cb@ArAA^AV?dAA`@?t@?dA?X?^?F?~@I@?j@MPGPGPKbAu@t@m@~@w@DElAeAzBiBhA}@XY\\WTSvBeBXWNM^[`Ay@VU|AmAb@_@^YNMzCmC\\WLQVs@Hg@@KVyBVaCBSXiCTqBDa@Fg@@KJqAH_ALgCBc@?ILcCJuAF_A@WFwA?k@?q@Ae@IyA?c@AmA?_@A_@CoAEaB?QAYCcAAa@GkA?Y"}, "startLocation": {"latLng": {"latitude": 12.8833302, "longitude": 77.7524057}}, "endLocation": {"latLng": {"latitude": 12.8609785, "longitude": 77.783585}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left after <PERSON>ke wala (on the left)\nPass by the gas station (on the left in 3.1 km)"}, "localizedValues": {"distance": {"text": "4.7 km"}, "staticDuration": {"text": "9 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 6765, "staticDuration": "587s", "polyline": {"encodedPolyline": "c|nmAkbwyMCIBCDAB?f@@bI~@l@HVD`@Fz@Hl@Hp@Jv@JVBF?vEf@j@FnDVtBFt@?nBB`@?`EDj@?`BBhDD~@@`BB^?nGHvCAJA`@E|Eo@`Ca@fG}@|Ba@bLqB`Cc@|A[xAY\\GfB_@|KoBtBYh@INA^E~AKJAjE?v@@fBKnFa@hGe@h@Gx@G~D]j@EtBUvBKzBKlEUfAIj@GrCErA?`BNTB~@JlAPvARdAN~ATj@HvBTj@HfBF`@@zFPzAD|GRB?|@B~DJ`EN~AFJ@vFTjBJfDVnAJxGn@fANtHfAvIlA|Df@dAJ|BR\\BnCTZBb@BhL|@XBnBZRH"}, "startLocation": {"latLng": {"latitude": 12.8609785, "longitude": 77.783585}}, "endLocation": {"latLng": {"latitude": 12.8010752, "longitude": 77.782827}}, "navigationInstruction": {"maneuver": "ROUNDABOUT_LEFT", "instructions": "At the roundabout, take the 2nd exit onto NH 948A\nPass by IIFL Gold Loan (on the right)"}, "localizedValues": {"distance": {"text": "6.8 km"}, "staticDuration": {"text": "10 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 2780, "staticDuration": "308s", "polyline": {"encodedPolyline": "wecmAu}vyMfAPPDd@NRHl@\\\\V~@hA@@t@jAlAhAj@^zAf@jB~@v@VfBJl@Jz@NvAJfAVzBt@dA`@B?VHRHTP`@Zn@n@JJ`C`C~EnEJHbBzALJhAv@FBdG~BhBr@jE`BNF~ElBfBp@zCjAfBp@ND~An@tChAnFpBp@T`Ab@VJj@XTHt@\\p@ZfAd@vB`A|@b@|@^"}, "startLocation": {"latLng": {"latitude": 12.8010752, "longitude": 77.782827}}, "endLocation": {"latLng": {"latitude": 12.779499999999999, "longitude": 77.77061979999999}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "Continue onto NH 948A"}, "localizedValues": {"distance": {"text": "2.8 km"}, "staticDuration": {"text": "5 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 533, "staticDuration": "95s", "polyline": {"encodedPolyline": "{~~lAkqtyMXDl@IPIJOd@yDD_@j@iFFm@L_AJw@TcBR{@FUJWNG"}, "startLocation": {"latLng": {"latitude": 12.779499999999999, "longitude": 77.77061979999999}}, "endLocation": {"latLng": {"latitude": 12.777988299999999, "longitude": 77.7749302}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left"}, "localizedValues": {"distance": {"text": "0.5 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 330, "staticDuration": "76s", "polyline": {"encodedPolyline": "mu~lAiluyMJ]p@mB@AXcAj@eBDQx@qBd@cANWFI"}, "startLocation": {"latLng": {"latitude": 12.777988299999999, "longitude": 77.7749302}}, "endLocation": {"latLng": {"latitude": 12.7766925, "longitude": 77.77766280000002}}, "navigationInstruction": {"maneuver": "MERGE", "instructions": "Merge onto NH 4/NH 44/NH 48\nToll road\nDestination will be on the left"}, "localizedValues": {"distance": {"text": "0.3 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "21.3 km"}, "duration": {"text": "38 mins"}, "staticDuration": {"text": "38 mins"}}}, {"distanceMeters": 158754, "duration": "9536s", "staticDuration": "9536s", "polyline": {"encodedPolyline": "im~lAk}uyMFKt@aBxC_FPWt@yAx@{AHObCqEpD{HzAqDtAoCf@{@Ra@HQxAaClBiCLOb@g@RUlAkAhA{@XUx@i@h@Yr@]`Bo@fBaApGqDhAu@\\[^Uv@m@f@_@dAeATW|ByC?[b@m@`@i@TYX_@NSbHwIbB{BnH}IfEoElAkA^?hCkCNOzAeBnAgA|AeAtBmAfAo@jBgAtA_AZWv@q@|AqA`@]nDwCpCaCRQnBsAfEqDfAmAl@u@z@qA|BaEh@aAp@_Bz@iCnB{H~EmRV_A\\qAVw@j@uAhBgDh@iAn@gAp@y@nAoAt@q@VUxAkAxJcHnA{@zAgA\\UROp@g@hAw@n@e@\\Wb@_@TSb@a@RSpA_B~BcDfA{AVa@Zi@\\o@f@mAXo@ZeAf@uBX_BJg@fAyFJe@nAwGH]ZqAb@eBjCyI~@cEJe@VkAR{@R{@Ry@z@uCx@uCZ_A\\aA^{@P_@v@wAPYPYn@aAx@gAHKJO`FsGhBiCj@_AZm@v@kBZ{@Vu@ZkARaAH[Hk@PkARqBFcADeADaAHwARiBt@mDxAoHf@_DDa@R{Bv@}PF_AP}ATqAfAaGFYXsBNgAJeAHkA`@gEVuCD_@Dc@D_@PsBLyAFs@D_@L{AHo@^_DD_@Jg@XmAx@qCx@qBn@oArAwBzA{BbBgCdBeCvFqIT]f@y@|@uAf@w@b@s@j@aALWh@mAJUXs@Lc@x@{CJg@RiAxAkIFYLq@xBuLNw@|CiQNw@n@eDFYH]VkAd@_B\\}@Ti@bAoB|BcE`@q@`@q@r@aAb@e@l@k@bB_Bn@k@pA_Bl@y@l@sA`@}ADWFc@Hu@DeABy@E_ACUKcASeAu@uDSiBAsBJmBz@kFv@eDb@}Aj@eBt@sBnAuC~@eBrBeDdAyA`ByB~AeBXWZW|F_GrAuAbEcEjB{B|AqBjA}AvAeB~CeET[z@qAZq@rBiDh@o@f@k@|B{BbAmAv@kAf@cAtAsCZi@v@kAhCmDhB{Bn@}@zBaC~CaCdF}C~@m@pA{@bBqAn@o@z@{@vBqCbB}BzB}Cj@u@dF_HVc@t@mA^u@Z}@b@_Bt@eDhAoEJa@|AiGdAeDj@}BbBgFt@sC~AgGdA{Db@kBpAuGh@}Cb@}BDQ~@gEb@iBPo@d@oBf@mBLc@b@eBbC}Iz@{C|@wCr@aCp@mCt@{Cj@eCt@yCTcA`@_CBWDk@?oAC}@g@wGCmBH_CRkCT{CLkBD_@HyA?MJyEr@iFPqAPiABQXkBpBcNfAyHDYFk@PoABKPkDNuCN}BPwAZ_B`@eBz@sBd@eAxA_C~@eAhCyBz@u@j@i@hAw@`BoAnAkAz@gA`AgBxA}CtAaD|AwD`@cAZo@r@sBLi@PwABgCGkIEeCQuKCmBEaE@_@?SB}@L{AF[RaAZsAdAqEnAsFnAqFLg@|AuF`@_BjBcHNe@d@iBdCqIVgAn@}DPeBDa@b@eEh@_GDc@Fc@D[^aDj@qF\\cDr@aHHw@^kCZuAv@mC\\_AtAkEV{@jAmD|@sChBaGFURi@|AkE|AsEdAqDV{BNqCVqCLgA^uBPm@dAaC|@oBz@}ANYRc@d@{@b@cAzCqF`D}GpAoCp@gAf@q@z@_ApEcFr@iAhAuBlD_H~A}CdBgDzA{Cf@wA~AoFbAyDj@mBdD_MNk@t@iCh@gB|@_DT{@tAcFPi@|@iDt@mCXcAh@yCt@uCNi@Vu@bAqBhB_Dp@gBXgAbAwDlAoHp@qELw@^yBb@mCV}AlAuHv@sFd@kCnAyHTuA\\wAJa@b@oAN]bAyBhEgJx@iBzAyCjC{Fb@yANUVm@|BqFHOl@uAzBcFn@aBf@uBZwBp@sGLwA`AuJ@ILaAJ]Li@^gAx@aBDGbBmB`@a@b@WfAm@xCmAjFiBpDgBtB{AvAkA`DqC\\[fB_BXWlEcEhAkAvA_BfEeF|@{@~AoAxN_JdAo@fAo@hBy@\\MpBo@~NoD~Ag@RIl@_@|@u@rAsA`CqC~@mAv@cAd@k@T[d@_@hCaDfAwANSrCeDx@eAhH{IvD{E|EuFj@y@^q@Vk@\\{@Ri@Z{@Pc@zAwDJW~AyEdC{GhAoC`B}Dl@}AL]t@{B\\qARiATiBTsB^{EDy@RqC?c@?aBAe@MkBMsD@yA?CLoF?_@EaBWaCs@_EQyAGq@MmBAmC@y@@kC@i@LaBVmBd@oCJs@^_CTmAnBcLn@gC\\aAVm@FKf@y@tCoDpA}Ad@k@PUZi@j@iAFSbDgIRi@J]r@iB^cBN{@DUPiAr@oHToBLcAJs@TaAfBgFr@kBNa@jAmDJKL]v@uBPg@p@cB|@iCp@}AbAcBhBeB`@YbAk@zAs@hKkDhCeAtA}@rAoAf@k@R[pE_G^g@xBqCvAiBp@_Av@aA^c@l@}@`@w@L[l@sB\\_Bf@iDDW@k@D_@Fa@H]Rs@t@kBb@o@fAsAVYh@i@n@q@bEkEh@s@\\k@Vk@Xu@\\oAf@gDNy@`@_B\\yAj@iBr@kCL]Ps@dBsFr@aChB}FdBsFDKL[N_@Vc@fCqDfDiEX_@n@w@fCaDlCqDz@kAx@wARe@hDyGn@uA~A_DFOXm@^q@Va@r@gAFI~@cApAiA~DeDz@e@t@o@`GsELKv@u@HI|@mApCaFh@s@xA_Br@g@dDgCpCcB`DuBJIb@Yf@_@hAq@z@q@hA}@f@g@VYlAuA@?tBmCj@u@rA}A\\a@hAmAhAaAz@w@dC{BtAwAT[LM|H}IhBuBhEaFh@k@tAqADCfAw@nEwC~@g@dA}@p@k@z@{@|DyDtDuD`AmAn@_Al@iAZq@j@wARq@lByFxBiGH]xAeEXy@t@yAx@sA~BsCLMzAiBlB{BxBmC~@kAlAqAzBiBd@UnAw@ZOb@UnAo@vAi@JEnA]xBa@zBa@b@Gp[yFt@SjQ{C`B]tA[tA]rDcA`HyBpCeAr@Y~BaAtFgCr@_@pBcAhHeEpHaFxCoBzG_F`Ao@h@a@tDkCZUtKsHtGsEfEsCBCbJkGZU|HqFxAcAvEcDtOwKfAu@r@c@h@W`Ae@`Cy@x@UhCc@t@KvAMtCKt@?z@@fEPL@J@|@Ll@Ln@N|A^n@NTFvDbALDB?vBp@rEbBh@\\`FvBvB`A^P`IjDfAb@zBz@zCx@~@NTDjEb@r@FRBp@Hr@FzALjBPF@zEb@dCTn@Hb@DdAJrAPhAPpB^tCt@zEjBnBx@pC~@|Bh@`AL|Hv@|ANpCTfA@xZ|C~@HzD`@|CZdEXJ@dENfBD\\@bA?xA@jAAlLW~KWRA`AGxASx@OpEmAfBa@hBKrEi@pBSpAO^OxDc@zAUbCe@rCu@~CiA`EgB|CsA`Ac@RGbBk@f@Kl@KlAMnBGrCHN@v@FnKl@D?pCBfCGrBOvBWxCm@jCy@rCiAdDgBFEd@WzCaBvHeE|Au@nCiAbGwBrBo@rNmEZKjGmBfJoChK}CnEuAhD}@x@KtBMnB@nAHv@Jb@Fz@JZDbJrAd@FtB\\lRnCRBzB\\jL`Bv@JvCZdSrAjN~@zARfAVr@TLDrAj@|A|@n@h@dA~@zG|GLLr@r@tCxCJTfAjAnCdCf@`@r@b@p@XNFl@Rx@Pl@J|@FbABhACvAOHClD[|@Q`LkAfCOrD_@HAlDSjEUdDUzEk@nDe@bAK\\Al@AvAAv@DvANnBZ~B`@vB^lJpApCZdD^zARhALrLr@b@BjKn@fBNpIbBdCf@lE|@fDt@lCj@rG`B|Bd@|AThCRxCNzDR~CNjBJjAHfE^xALhPnAZDdE\\pBLn@@zB@`BC|CG`Mu@|EU`Dc@pASnEq@~GcA^E`CUxCIzIOjM]hEEzCCnDCdDDnDPfBLf@Db@DrFn@lGx@tAXhHvApFnAbAZbAf@fAl@hHlEr@b@jClAlNhE|E|AzBz@bDtA|IxDxErBp@VnBl@j@LhANlCLjB?pGGpDGtCEfAClDQ`Ko@z@E|Hi@`Im@xEi@lF_@J?`CKrB?t@BvAJ|ATZFZFdFfAxGzAdDr@zBh@rA^p@Xl@ZRJ`Ar@zBfBhA`ApCvBbG~Fh@f@fB`B`Az@fAbArD|DnEpF`@l@FFVR|@f@b@ZVNb@PtA`@VFvATbBLf@BbP|@t@DvCVbDXtALtDj@`C`@hBXjC\\vCTrLz@|Ij@hJp@tBR`DVrFd@lEPvBBV@n@?^ApDCnJEdACnAG|AKpMqAlCWpHw@dNyApAOl@KPC`ASnBk@zAk@v@]fAo@~@k@lFiD|@e@~@_@v@UZIxB]pAKrAA~ABjBPpATbA\\tB|@TJv@d@nAbAp@r@b@h@xDzFnAlBvBpCPTt@t@FFv@f@LHnAj@hAZbAND@`@B@?jADtAAbJY`@AfDGn@?jD@rGFhEFzDAd@@rAB~B@bBB`@?jBBb@?|KOv@?`EN|@J~ARrCl@D?rA^jA`@r@Xb@F~CdBjBrAbDnCnAdAfAr@d@XvAj@r@RZF`@Hr@HX@nADlACfG[tHc@rAAnBBxBPrAPtGpBvAf@dJjCrAZdF~@|Dj@hGt@vAXrA\\|@`@pB|@`EjB|@b@r@NtCfAz@VbAZnAd@h@Tt@^zAbAxAnApEzDHHr@j@|BbBlFrDj@`@VNxBnA|At@tBv@fBf@lAXPFvAVf@DxAPL@~CR`C@d@?hBAdENdBRfBd@f@RlB|@nDlBd@XjAl@xDlBhI|D^NfDrA~Bt@nI`CnBp@fAh@dAj@x@j@jAz@`ExCnEnDd@\\zDlCp@`@vAx@v@d@zIhEvDvA~Ah@`JhChEjALBfGhBh@Rn@Zj@Zj@Xb@T`At@d@d@t@x@bPjTd@n@jCrDvBnCx@dAzBxCpMhQbAnAn@x@l@t@rCdDjDdDXV|AtAf@d@~DnDzDnDd@`@vArAhHpGhFtEtLjKf@b@TTxBjBhC`Cz@t@VTRRLHVVh@d@vKzJxApAhA|@bAz@`BjAhCbBzBlALDhCnAp@Z^P`DnAfF`B`AVjAXbDv@zLpCfCn@vD|@jEbAzKfCtEdAtFpAzA\\pInBzA\\tBd@RFnAXlCn@tGxAdFlAfAVj@NzAZlH`B|FtAjAX^HZN`Cf@dBZrCp@rCp@VF~M|CxDz@jCv@zEdB\\LdEtBvAp@rBhAlBnA~GhFhG~E~DbDfBvAhDpCxGtFHF`HvFzJfI`@\\tDzCPNhCjBbH|FTZtAdAtEtCfB`ArFhCdBn@b@NdHzB`Cj@jDn@|HhA~MfBzInApEf@bIr@fJv@zBNrGn@jBPvFf@~VvBt@FjR~Ar@Fr@FzGl@hUpBtGl@xFd@b@D`AHlE`@N@pHl@dBNfThB`K|@f@FXF`@F|ARzIxArAVbCj@bDz@fCr@tA^l@LfDn@hARfCf@nARxKhBzAThFz@tHtAJB|KpBdFt@fALbEd@`ALrDd@rDf@pARj@HfBV~AV`@HTDl@RlBt@TLfAn@d@\\ZTz@r@xAnAlCrB~@p@pBlApCfBfBhAfBvAv@dA`AvALRxFfKpBtEb@fA`A~Bv@zAp@bAd@h@j@d@bBjAxAv@NH~@f@ZR~AfAh@h@l@h@zAlB|AvBRV`DlEJJb@f@jCxCVXvAvAlAnAd@b@|@t@hBlAbD|AzAt@\\RfAp@\\VnBtAXRhDjCvAfAr@d@vIlEb@TpAl@nAp@bBfATRZVf@d@fAlAbDzErIhMdBrBdB`BdDrC`BhBvDjFLP`AdAzApAlBrAvHnFn@f@dBxAJHNJHHnExC|IvGv@h@zA|@|@`@~Ad@\\FtBb@xA\\TFbA^|@b@PJtFzDdA|@dApAfAfBrA~BpDjGdFtIx@lAz@`ApB`BtA`AxGtEf@\\xAz@jCfAbJ~CtChAnCfARH~@h@`@XpCnBfAf@v@Rb@HxARbCXt@JRBnBPr@HnBVvB`@z@Zr@f@|@dARXfA|Ar@p@HD^Np@Jv@@r@Mt@[b@]\\m@Tq@XwATq@NWhAgAj@[r@Qz@Ap@B|@Jv@\\hF~DhCnB|@`@x@Dt@GbEeAZEb@Ap@FF?f@Pf@`@V`@`@bApA`Cx@`AvBhBd@VNHvAl@hDz@VBdAHnACxAIpDUvEQbB?F?rBNRBP@hEd@rADx@ATAbAMr@KdAQdAQzF_AxBk@l@S`Bo@z@a@`@YhBiAtB{ApBsAh@_@PMj@]d@_@z@m@hJ}Gx@i@lBiAz@k@jAs@n@g@TY\\c@l@oAZaAHWBITiAD[T}BPwBL}ATqCN}AFq@RuCXwCT_BRcAXkAnBuFhAgCp@oANUx@_ApAoAxAcAhB}@vAg@l@Od@KhBYtAMt@CjDIlDO~DOzAIpCIhBBnAJzAXb@Nv@XnAj@xBfA|Az@~B`AhEdB|Q|GhC~@xBdAZPZPtEhCjB`AhB~@fCnAtAh@tA\\TDdBT`@Bz@B|@?z@C~@GlB[ZCjAOpFa@B?tBUvBa@rA_@x@[^OvFoCjB}@|CyA`Bm@nBg@fB[vHu@hCe@bAUfAYTGRGfC}@lB{@lCyAjBiAd@[tB}AzL{K`C{Bl@m@dBqB~FcHt@gAd@u@v@sAj@qAt@mAf@q@j@s@^a@\\_@dB{A`@]TKnEsCzAaA`@U|CgBPOFEd@i@PO?Az@u@xAwAdB}AtCsC`@a@|@gA|A}BfGkJ`AwAbAuAl@o@`BsAxA{@TIfBo@r@QNCdBUt@Ev@EjCCn@AN?v@?lFMpCStKwAx@Ql@QnEaB\\Kz@UTE`@Gd@ExAGfA@Z@dBPxAVdBb@v@Vh@RhAj@fBlAbDjCnIdHpExDnFxEbCxB~MvLVTbE~CrLbI|AfAtCpBPLf@\\NH`BfAbCfBHFvA~@tHdFdB~@XLlAd@rBl@dCb@nIlAlEv@hC`@nARbC^bC\\hDd@|@LfGx@h@FjCVfBJp@BN?pHC`CAJ?v@AxACpHJH?|@@l@B|DLR?pCA|EOnCGrAEV?lHPnADr@D`@D~BPbAHfBL`DThAHvCTpDd@zCn@nDdAxBt@~@ZjBn@nBl@bAV`FlAfHfBlAZlCr@jEfAbDp@j@JpGtAbBf@vAh@nC`AbA^v@Xp@TXJVHr@Rb@LlGdBvCz@`AR~A\\~Cj@`BXnEx@zAXzAVhB\\|AX~B^dDn@j@J|BVdG\\xEXnMv@bADbCApBEdPc@pEKtAEfKWzDIlBI`@A\\A`@?PAj@C|@CF?lAEl@EpGk@zC[dAGnB?r@DlAPzA^fA^`Cz@rDpAv@Xp@VfA`@fC|@xAf@^@RDhJrCnBj@LHB?RBl@JZJ|EdAx@Px@RvBd@nB^~AXND\\b@jLlC|Dt@vGjAdKbCxBh@rCn@vCv@zBh@xBd@pBTdB@b@CNA~@GN?zB]pLqBf@StEWt@?lA@vFPbENdEJ~BHbBDxAFpBFjGT`J\\lJ\\tABdBC~@I^M~@W`B{@\\SZUPO\\]X[bEsFn@cAXg@r@u@XYjB_Cb@i@|@}@TGxBcCvA{AtEyFpBkCpAwA`JwJl@m@V[p@s@hBkBrBoBbB_BnGgG~@{@l@o@rFqHlCqDrKcOz@mAvC}DtEmGl@y@bCaD\\c@pA_BJM"}, "startLocation": {"latLng": {"latitude": 12.7766925, "longitude": 77.77766280000002}}, "endLocation": {"latLng": {"latitude": 11.7197327, "longitude": 78.0739292}}, "steps": [{"distanceMeters": 2090, "staticDuration": "213s", "polyline": {"encodedPolyline": "im~lAk}uyMFKt@aBxC_FPWt@yAx@{AHObCqEpD{HzAqDtAoCf@{@Ra@HQxAaClBiCLOb@g@RUlAkAhA{@XUx@i@h@Yr@]`Bo@fBaApGqDhAu@\\[^Uv@m@f@_@dAeATW|ByC"}, "startLocation": {"latLng": {"latitude": 12.7766925, "longitude": 77.77766280000002}}, "endLocation": {"latLng": {"latitude": 12.7644922, "longitude": 77.7918558}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head southeast on NH 4/NH 44/NH 48\nPass by Oneness Travels - Attibel<PERSON> Pickup (on the left)"}, "localizedValues": {"distance": {"text": "2.1 km"}, "staticDuration": {"text": "4 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 16, "staticDuration": "3s", "polyline": {"encodedPolyline": "aa|lAcvxyM?["}, "startLocation": {"latLng": {"latitude": 12.7644922, "longitude": 77.7918558}}, "endLocation": {"latLng": {"latitude": 12.764494299999999, "longitude": 77.7920019}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left"}, "localizedValues": {"distance": {"text": "16 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 452, "staticDuration": "75s", "polyline": {"encodedPolyline": "aa|lA_wxyMb@m@`@i@TYX_@NSbHwIbB{B"}, "startLocation": {"latLng": {"latitude": 12.764494299999999, "longitude": 77.7920019}}, "endLocation": {"latLng": {"latitude": 12.7618563, "longitude": 77.7951729}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at MRF TYRES WHEEL ALGN BALANCE\nPass by TamilNadu RTO Check Post (on the left)"}, "localizedValues": {"distance": {"text": "0.5 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 472, "staticDuration": "55s", "polyline": {"encodedPolyline": "sp{lAyjyyMnH}IfEoElAkA"}, "startLocation": {"latLng": {"latitude": 12.7618563, "longitude": 77.7951729}}, "endLocation": {"latLng": {"latitude": 12.7589486, "longitude": 77.7983423}}, "navigationInstruction": {"maneuver": "STRAIGHT", "instructions": "Continue straight"}, "localizedValues": {"distance": {"text": "0.5 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 18, "staticDuration": "2s", "polyline": {"encodedPolyline": "m~zlAs~yyM^?"}, "startLocation": {"latLng": {"latitude": 12.7589486, "longitude": 77.7983423}}, "endLocation": {"latLng": {"latitude": 12.7587856, "longitude": 77.798345}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right toward NH 4/NH 44/NH 48"}, "localizedValues": {"distance": {"text": "18 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 148261, "staticDuration": "8643s", "polyline": {"encodedPolyline": "m}zlAs~yyMhCkCNOzAeBnAgA|AeAtBmAfAo@jBgAtA_AZWv@q@|AqA`@]nDwCpCaCRQnBsAfEqDfAmAl@u@z@qA|BaEh@aAp@_Bz@iCnB{H~EmRV_A\\qAVw@j@uAhBgDh@iAn@gAp@y@nAoAt@q@VUxAkAxJcHnA{@zAgA\\UROp@g@hAw@n@e@\\Wb@_@TSb@a@RSpA_B~BcDfA{AVa@Zi@\\o@f@mAXo@ZeAf@uBX_BJg@fAyFJe@nAwGH]ZqAb@eBjCyI~@cEJe@VkAR{@R{@Ry@z@uCx@uCZ_A\\aA^{@P_@v@wAPYPYn@aAx@gAHKJO`FsGhBiCj@_AZm@v@kBZ{@Vu@ZkARaAH[Hk@PkARqBFcADeADaAHwARiBt@mDxAoHf@_DDa@R{Bv@}PF_AP}ATqAfAaGFYXsBNgAJeAHkA`@gEVuCD_@Dc@D_@PsBLyAFs@D_@L{AHo@^_DD_@Jg@XmAx@qCx@qBn@oArAwBzA{BbBgCdBeCvFqIT]f@y@|@uAf@w@b@s@j@aALWh@mAJUXs@Lc@x@{CJg@RiAxAkIFYLq@xBuLNw@|CiQNw@n@eDFYH]VkAd@_B\\}@Ti@bAoB|BcE`@q@`@q@r@aAb@e@l@k@bB_Bn@k@pA_Bl@y@l@sA`@}ADWFc@Hu@DeABy@E_ACUKcASeAu@uDSiBAsBJmBz@kFv@eDb@}Aj@eBt@sBnAuC~@eBrBeDdAyA`ByB~AeBXWZW|F_GrAuAbEcEjB{B|AqBjA}AvAeB~CeET[z@qAZq@rBiDh@o@f@k@|B{BbAmAv@kAf@cAtAsCZi@v@kAhCmDhB{Bn@}@zBaC~CaCdF}C~@m@pA{@bBqAn@o@z@{@vBqCbB}BzB}Cj@u@dF_HVc@t@mA^u@Z}@b@_Bt@eDhAoEJa@|AiGdAeDj@}BbBgFt@sC~AgGdA{Db@kBpAuGh@}Cb@}BDQ~@gEb@iBPo@d@oBf@mBLc@b@eBbC}Iz@{C|@wCr@aCp@mCt@{Cj@eCt@yCTcA`@_CBWDk@?oAC}@g@wGCmBH_CRkCT{CLkBD_@HyA?MJyEr@iFPqAPiABQXkBpBcNfAyHDYFk@PoABKPkDNuCN}BPwAZ_B`@eBz@sBd@eAxA_C~@eAhCyBz@u@j@i@hAw@`BoAnAkAz@gA`AgBxA}CtAaD|AwD`@cAZo@r@sBLi@PwABgCGkIEeCQuKCmBEaE@_@?SB}@L{AF[RaAZsAdAqEnAsFnAqFLg@|AuF`@_BjBcHNe@d@iBdCqIVgAn@}DPeBDa@b@eEh@_GDc@Fc@D[^aDj@qF\\cDr@aHHw@^kCZuAv@mC\\_AtAkEV{@jAmD|@sChBaGFURi@|AkE|AsEdAqDV{BNqCVqCLgA^uBPm@dAaC|@oBz@}ANYRc@d@{@b@cAzCqF`D}GpAoCp@gAf@q@z@_ApEcFr@iAhAuBlD_H~A}CdBgDzA{Cf@wA~AoFbAyDj@mBdD_MNk@t@iCh@gB|@_DT{@tAcFPi@|@iDt@mCXcAh@yCt@uCNi@Vu@bAqBhB_Dp@gBXgAbAwDlAoHp@qELw@^yBb@mCV}AlAuHv@sFd@kCnAyHTuA\\wAJa@b@oAN]bAyBhEgJx@iBzAyCjC{Fb@yANUVm@|BqFHOl@uAzBcFn@aBf@uBZwBp@sGLwA`AuJ@ILaAJ]Li@^gAx@aBDGbBmB`@a@b@WfAm@xCmAjFiBpDgBtB{AvAkA`DqC\\[fB_BXWlEcEhAkAvA_BfEeF|@{@~AoAxN_JdAo@fAo@hBy@\\MpBo@~NoD~Ag@RIl@_@|@u@rAsA`CqC~@mAv@cAd@k@T[d@_@hCaDfAwANSrCeDx@eAhH{IvD{E|EuFj@y@^q@Vk@\\{@Ri@Z{@Pc@zAwDJW~AyEdC{GhAoC`B}Dl@}AL]t@{B\\qARiATiBTsB^{EDy@RqC?c@?aBAe@MkBMsD@yA?CLoF?_@EaBWaCs@_EQyAGq@MmBAmC@y@@kC@i@LaBVmBd@oCJs@^_CTmAnBcLn@gC\\aAVm@FKf@y@tCoDpA}Ad@k@PUZi@j@iAFSbDgIRi@J]r@iB^cBN{@DUPiAr@oHToBLcAJs@TaAfBgFr@kBNa@jAmDJKL]v@uBPg@p@cB|@iCp@}AbAcBhBeB`@YbAk@zAs@hKkDhCeAtA}@rAoAf@k@R[pE_G^g@xBqCvAiBp@_Av@aA^c@l@}@`@w@L[l@sB\\_Bf@iDDW@k@D_@Fa@H]Rs@t@kBb@o@fAsAVYh@i@n@q@bEkEh@s@\\k@Vk@Xu@\\oAf@gDNy@`@_B\\yAj@iBr@kCL]Ps@dBsFr@aChB}FdBsFDKL[N_@Vc@fCqDfDiEX_@n@w@fCaDlCqDz@kAx@wARe@hDyGn@uA~A_DFOXm@^q@Va@r@gAFI~@cApAiA~DeDz@e@t@o@`GsELKv@u@HI|@mApCaFh@s@xA_Br@g@dDgCpCcB`DuBJIb@Yf@_@hAq@z@q@hA}@f@g@VYlAuA@?tBmCj@u@rA}A\\a@hAmAhAaAz@w@dC{BtAwAT[LM|H}IhBuBhEaFh@k@tAqADCfAw@nEwC~@g@dA}@p@k@z@{@|DyDtDuD`AmAn@_Al@iAZq@j@wARq@lByFxBiGH]xAeEXy@t@yAx@sA~BsCLMzAiBlB{BxBmC~@kAlAqAzBiBd@UnAw@ZOb@UnAo@vAi@JEnA]xBa@zBa@b@Gp[yFt@SjQ{C`B]tA[tA]rDcA`HyBpCeAr@Y~BaAtFgCr@_@pBcAhHeEpHaFxCoBzG_F`Ao@h@a@tDkCZUtKsHtGsEfEsCBCbJkGZU|HqFxAcAvEcDtOwKfAu@r@c@h@W`Ae@`Cy@x@UhCc@t@KvAMtCKt@?z@@fEPL@J@|@Ll@Ln@N|A^n@NTFvDbALDB?vBp@rEbBh@\\`FvBvB`A^P`IjDfAb@zBz@zCx@~@NTDjEb@r@FRBp@Hr@FzALjBPF@zEb@dCTn@Hb@DdAJrAPhAPpB^tCt@zEjBnBx@pC~@|Bh@`AL|Hv@|ANpCTfA@xZ|C~@HzD`@|CZdEXJ@dENfBD\\@bA?xA@jAAlLW~KWRA`AGxASx@OpEmAfBa@hBKrEi@pBSpAO^OxDc@zAUbCe@rCu@~CiA`EgB|CsA`Ac@RGbBk@f@Kl@KlAMnBGrCHN@v@FnKl@D?pCBfCGrBOvBWxCm@jCy@rCiAdDgBFEd@WzCaBvHeE|Au@nCiAbGwBrBo@rNmEZKjGmBfJoChK}CnEuAhD}@x@KtBMnB@nAHv@Jb@Fz@JZDbJrAd@FtB\\lRnCRBzB\\jL`Bv@JvCZdSrAjN~@zARfAVr@TLDrAj@|A|@n@h@dA~@zG|GLLr@r@tCxCJTfAjAnCdCf@`@r@b@p@XNFl@Rx@Pl@J|@FbABhACvAOHClD[|@Q`LkAfCOrD_@HAlDSjEUdDUzEk@nDe@bAK\\Al@AvAAv@DvANnBZ~B`@vB^lJpApCZdD^zARhALrLr@b@BjKn@fBNpIbBdCf@lE|@fDt@lCj@rG`B|Bd@|AThCRxCNzDR~CNjBJjAHfE^xALhPnAZDdE\\pBLn@@zB@`BC|CG`Mu@|EU`Dc@pASnEq@~GcA^E`CUxCIzIOjM]hEEzCCnDCdDDnDPfBLf@Db@DrFn@lGx@tAXhHvApFnAbAZbAf@fAl@hHlEr@b@jClAlNhE|E|AzBz@bDtA|IxDxErBp@VnBl@j@LhANlCLjB?pGGpDGtCEfAClDQ`Ko@z@E|Hi@`Im@xEi@lF_@J?`CKrB?t@BvAJ|ATZFZFdFfAxGzAdDr@zBh@rA^p@Xl@ZRJ`Ar@zBfBhA`ApCvBbG~Fh@f@fB`B`Az@fAbArD|DnEpF`@l@FFVR|@f@b@ZVNb@PtA`@VFvATbBLf@BbP|@t@DvCVbDXtALtDj@`C`@hBXjC\\vCTrLz@|Ij@hJp@tBR`DVrFd@lEPvBBV@n@?^ApDCnJEdACnAG|AKpMqAlCWpHw@dNyApAOl@KPC`ASnBk@zAk@v@]fAo@~@k@lFiD|@e@~@_@v@UZIxB]pAKrAA~ABjBPpATbA\\tB|@TJv@d@nAbAp@r@b@h@xDzFnAlBvBpCPTt@t@FFv@f@LHnAj@hAZbAND@`@B@?jADtAAbJY`@AfDGn@?jD@rGFhEFzDAd@@rAB~B@bBB`@?jBBb@?|KOv@?`EN|@J~ARrCl@D?rA^jA`@r@Xb@F~CdBjBrAbDnCnAdAfAr@d@XvAj@r@RZF`@Hr@HX@nADlACfG[tHc@rAAnBBxBPrAPtGpBvAf@dJjCrAZdF~@|Dj@hGt@vAXrA\\|@`@pB|@`EjB|@b@r@NtCfAz@VbAZnAd@h@Tt@^zAbAxAnApEzDHHr@j@|BbBlFrDj@`@VNxBnA|At@tBv@fBf@lAXPFvAVf@DxAPL@~CR`C@d@?hBAdENdBRfBd@f@RlB|@nDlBd@XjAl@xDlBhI|D^NfDrA~Bt@nI`CnBp@fAh@dAj@x@j@jAz@`ExCnEnDd@\\zDlCp@`@vAx@v@d@zIhEvDvA~Ah@`JhChEjALBfGhBh@Rn@Zj@Zj@Xb@T`At@d@d@t@x@bPjTd@n@jCrDvBnCx@dAzBxCpMhQbAnAn@x@l@t@rCdDjDdDXV|AtAf@d@~DnDzDnDd@`@vArAhHpGhFtEtLjKf@b@TTxBjBhC`Cz@t@VTRRLHVVh@d@vKzJxApAhA|@bAz@`BjAhCbBzBlALDhCnAp@Z^P`DnAfF`B`AVjAXbDv@zLpCfCn@vD|@jEbAzKfCtEdAtFpAzA\\pInBzA\\tBd@RFnAXlCn@tGxAdFlAfAVj@NzAZlH`B|FtAjAX^HZN`Cf@dBZrCp@rCp@VF~M|CxDz@jCv@zEdB\\LdEtBvAp@rBhAlBnA~GhFhG~E~DbDfBvAhDpCxGtFHF`HvFzJfI`@\\tDzCPNhCjBbH|FTZtAdAtEtCfB`ArFhCdBn@b@NdHzB`Cj@jDn@|HhA~MfBzInApEf@bIr@fJv@zBNrGn@jBPvFf@~VvBt@FjR~Ar@Fr@FzGl@hUpBtGl@xFd@b@D`AHlE`@N@pHl@dBNfThB`K|@f@FXF`@F|ARzIxArAVbCj@bDz@fCr@tA^l@LfDn@hARfCf@nARxKhBzAThFz@tHtAJB|KpBdFt@fALbEd@`ALrDd@rDf@pARj@HfBV~AV`@HTDl@RlBt@TLfAn@d@\\ZTz@r@xAnAlCrB~@p@pBlApCfBfBhAfBvAv@dA`AvALRxFfKpBtEb@fA`A~Bv@zAp@bAd@h@j@d@bBjAxAv@NH~@f@ZR~AfAh@h@l@h@zAlB|AvBRV`DlEJJb@f@jCxCVXvAvAlAnAd@b@|@t@hBlAbD|AzAt@\\RfAp@\\VnBtAXRhDjCvAfAr@d@vIlEb@TpAl@nAp@bBfATRZVf@d@fAlAbDzErIhMdBrBdB`BdDrC`BhBvDjFLP`AdAzApAlBrAvHnFn@f@dBxAJHNJHHnExC|IvGv@h@zA|@|@`@~Ad@\\FtBb@xA\\TFbA^|@b@PJtFzDdA|@dApAfAfBrA~BpDjGdFtIx@lAz@`ApB`BtA`AxGtEf@\\xAz@jCfAbJ~CtChAnCfARH~@h@`@XpCnBfAf@v@Rb@HxARbCXt@JRBnBPr@HnBVvB`@z@Zr@f@|@dARXfA|Ar@p@HD^Np@Jv@@r@Mt@[b@]\\m@Tq@XwATq@NWhAgAj@[r@Qz@Ap@B|@Jv@\\hF~DhCnB|@`@x@Dt@GbEeAZEb@Ap@FF?f@Pf@`@V`@`@bApA`Cx@`AvBhBd@VNHvAl@hDz@VBdAHnACxAIpDUvEQbB?F?rBNRBP@hEd@rADx@ATAbAMr@KdAQdAQzF_AxBk@l@S`Bo@z@a@`@YhBiAtB{ApBsAh@_@PMj@]d@_@z@m@hJ}Gx@i@lBiAz@k@jAs@n@g@TY\\c@l@oAZaAHWBITiAD[T}BPwBL}ATqCN}AFq@RuCXwCT_BRcAXkAnBuFhAgCp@oANUx@_ApAoAxAcAhB}@vAg@l@Od@KhBYtAMt@CjDIlDO~DOzAIpCIhBBnAJzAXb@Nv@XnAj@xBfA|Az@~B`AhEdB|Q|GhC~@xBdAZPZPtEhCjB`AhB~@fCnAtAh@tA\\TDdBT`@Bz@B|@?z@C~@GlB[ZCjAOpFa@B?tBUvBa@rA_@x@[^OvFoCjB}@|CyA`Bm@nBg@fB[vHu@hCe@bAUfAYTGRGfC}@lB{@lCyAjBiAd@[tB}AzL{K`C{Bl@m@dBqB~FcHt@gAd@u@v@sAj@qAt@mAf@q@j@s@^a@\\_@dB{A`@]TKnEsCzAaA`@U|CgBPOFEd@i@PO?Az@u@xAwAdB}AtCsC`@a@|@gA|A}BfGkJ`AwAbAuAl@o@`BsAxA{@TIfBo@r@QNCdBUt@Ev@EjCCn@AN?v@?lFMpCStKwAx@Ql@QnEaB\\Kz@UTE`@Gd@ExAGfA@Z@dBPxAVdBb@v@Vh@RhAj@fBlAbDjCnIdHpExDnFxEbCxB~MvLVTbE~CrLbI|AfAtCpBPLf@\\NH`BfAbCfBHFvA~@tHdFdB~@XLlAd@rBl@dCb@nIlAlEv@hC`@nARbC^bC\\hDd@|@LfGx@h@FjCVfBJp@BN?pHC`CAJ?v@AxACpHJH?|@@l@B|DLR?pCA|EOnCGrAEV?lHPnADr@D`@D~BPbAHfBL`DThAHvCTpDd@zCn@nDdAxBt@~@ZjBn@nBl@bAV`FlAfHfBlAZlCr@jEfAbDp@j@JpGtAbBf@vAh@nC`AbA^v@Xp@TXJVHr@Rb@LlGdBvCz@`AR~A\\~Cj@`BXnEx@zAXzAVhB\\|AX~B^dDn@j@J|BVdG\\xEXnMv@bADbCApBEdPc@pEKtAEfKWzDIlBI`@A\\A`@?PAj@C|@CF?lAEl@EpGk@zC[dAGnB?r@DlAPzA^fA^`Cz@rDpAv@Xp@VfA`@fC|@xAf@"}, "startLocation": {"latLng": {"latitude": 12.7587856, "longitude": 77.798345}}, "endLocation": {"latLng": {"latitude": 11.7753803, "longitude": 78.0559585}}, "navigationInstruction": {"maneuver": "MERGE", "instructions": "Merge onto NH 4/NH 44/NH 48\nContinue to follow NH 44\nToll road\nPass by the lake (on the right in 2.7 km)"}, "localizedValues": {"distance": {"text": "148 km"}, "staticDuration": {"text": "2 hours 24 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 336, "staticDuration": "36s", "polyline": {"encodedPolyline": "c{zfAwhl{M^@RDhJrCnBj@LHB?RB"}, "startLocation": {"latLng": {"latitude": 11.7753803, "longitude": 78.0559585}}, "endLocation": {"latLng": {"latitude": 11.772558, "longitude": 78.05489349999999}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_LEFT", "instructions": "Slight left at Rajasthani Dhaba\nPass by S <PERSON>rill Labor Works (on the left)"}, "localizedValues": {"distance": {"text": "0.3 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 310, "staticDuration": "33s", "polyline": {"encodedPolyline": "oizfAabl{Ml@JZJ|EdAx@Px@RvBd@"}, "startLocation": {"latLng": {"latitude": 11.772558, "longitude": 78.05489349999999}}, "endLocation": {"latLng": {"latitude": 11.7699029, "longitude": 78.05404109999999}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Keep right"}, "localizedValues": {"distance": {"text": "0.3 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 129, "staticDuration": "14s", "polyline": {"encodedPolyline": "{xyfAw|k{MnB^~AXND"}, "startLocation": {"latLng": {"latitude": 11.7699029, "longitude": 78.05404109999999}}, "endLocation": {"latLng": {"latitude": 11.7687843, "longitude": 78.05372400000002}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "At Royal Looks Gents beauty parlour, continue onto NH 44 Service Road"}, "localizedValues": {"distance": {"text": "0.1 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 26, "staticDuration": "3s", "polyline": {"encodedPolyline": "{qyfAwzk{M\\b@"}, "startLocation": {"latLng": {"latitude": 11.7687843, "longitude": 78.05372400000002}}, "endLocation": {"latLng": {"latitude": 11.7686275, "longitude": 78.0535427}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_RIGHT", "instructions": "Slight right toward NH 44"}, "localizedValues": {"distance": {"text": "26 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 6644, "staticDuration": "459s", "polyline": {"encodedPolyline": "}pyfAsyk{MjLlC|Dt@vGjAdKbCxBh@rCn@vCv@zBh@xBd@pBTdB@b@CNA~@GN?zB]pLqBf@StEWt@?lA@vFPbENdEJ~BHbBDxAFpBFjGT`J\\lJ\\tABdBC~@I^M~@W`B{@\\SZUPO\\]X[bEsFn@cAXg@r@u@XYjB_Cb@i@|@}@TGxBcCvA{AtEyFpBkCpAwA`JwJl@m@V[p@s@hBkBrBoBbB_BnGgG~@{@l@o@rFqHlCqDrKcOz@mAvC}DtEmGl@y@bCaD\\c@pA_BJM"}, "startLocation": {"latLng": {"latitude": 11.7686275, "longitude": 78.0535427}}, "endLocation": {"latLng": {"latitude": 11.7197327, "longitude": 78.0739292}}, "navigationInstruction": {"maneuver": "MERGE", "instructions": "Merge onto NH 44\nToll road\nPass by Sri Vinayaka Power Tools (on the left in 1.5 km)\nDestination will be on the left"}, "localizedValues": {"distance": {"text": "6.6 km"}, "staticDuration": {"text": "8 mins"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "159 km"}, "duration": {"text": "2 hours 39 mins"}, "staticDuration": {"text": "2 hours 39 mins"}}}, {"distanceMeters": 173406, "duration": "11647s", "staticDuration": "11647s", "polyline": {"encodedPolyline": "i_pfAayo{M@AzEaG~DaF~AqBt@gAl@gA`@aAJUfA{CxAeDjAyBhF_Kp@sABIz@mCTo@rAwClEmLdBwDvAqCh@cAFKrAyCVe@Vg@~D{HNWTc@fAuBd@}@`@y@Xk@Xk@\\m@hGiLtDoGXg@n@kAdC}E~DgIl@qApCkGXq@Xm@\\w@h@cAN]`@m@`AmAf@c@zByAbBy@\\QzC{AjCoAhGcD~C{A|CgB~@u@DCXWdAsAbAoBRk@BGf@sAd@mAHy@Rg@zByFd@kAFOTg@n@yAh@sA@CDUt@mB`@_A^{@LUj@y@bBwCtB_Eh@y@z@uADIlBiCr@Wb@]dCaBvA}@XUb@a@t@}@~BwDPYP[|AmCr@mA~AaCt@cAr@aAd@q@t@aAtC_EJKx@gAl@u@n@y@hBiBx@o@^WLIb@YFEz@c@|@c@zBw@z@Sd@M|B[^EbAGlAEhC@xENv@B|@DzDLtDL`BJvB^NBPDvBl@n@RrD`B`@V~@f@rBdAfAf@fBz@jFnCj@X`Bv@rIdEb@VbCnAZJb@^hBv@lBp@~A`@xBXz@D`A@n@Bx@BpDNP@N@R?fGXjMf@l@B|ETxLj@`CJrEPnAFjADbAD\\BnI\\tEPZ@R@~@BrADbBHtBNv@FtAH|AFNMh@@lDBfBEp@CVC`@CLC\\MZUR]Ha@@YGc@Sc@SQg@QWCM?o@Ja@TSTUf@Kn@I~AGhACh@El@?JAnAHr@Pj@T^`@`@\\RPFp@NL@v@Bh@?pB?lC@fMB\\?rI?nE?xEDnBFVBXBd@FzB\\bB^~Br@pAf@zC|Ax@h@r@h@rAhAt@r@\\`@rAdB|@vA|@`Bt@dBvGfQn@pAh@|@hAzATT~@|@z@l@bDdB~Al@tCv@d@LjQzE`AXnBh@rBf@`Bb@x@XvL`DhDbARHpAj@f@TTNTLb@Xr@f@dAz@dAdA`BxBdBhCx@|AdA|BTh@n@rAr@zAXd@JRh@v@\\b@h@n@tAlAvCrBx@j@fA`A`AhARVrBxCvAzBz@jAhFzHlBpC~@rAjAtA^b@hAjANLXV|B|BdAdAvCpChAfAl@n@vAfBbAzAlCbFLZNXd@hA`@jAV~@h@`Bn@tB^nA~@rDpArFDNzAxExArD`B|D`AvBTb@^t@l@~@^j@LR`BrB|@pAPVzBdE`A`CPb@Vp@HT~@`CVn@Rd@lB~EjAvCd@jAb@jAXv@ZdAp@vBlDlLdAhDh@dBTn@jBpFZbAzA|EfB~FL\\Tp@xCdJ~@pBf@~@HL\\l@pAjBdAlBlAhCf@nAV|@Pd@fC~HlA|CtB`FlBtE`CvF|B|FrC|HZh@\\l@RLZr@\\`AbBxETj@h@xAb@lAj@`Bf@vAbAlBz@vARj@Hd@\\l@hBrCJNh@`AnErHb@p@p@hAfD|FlAfBl@t@rFtGV\\hBxBbFlGhDbFtA~BtBpDNTrDnGxEvHnCpE`B|CVf@dB|DnAbD\\~@pDjJVj@f@vAl@bB`@tA~CtL\\hAJ`@XbAJb@J^XdAf@dBJb@l@`BHRBHL`@L^p@tBnAvDb@zAl@xBTt@b@`BZdAfAfDn@hBh@tAlBpEXn@|AtC^x@v@|AtAnClCbGd@`AVd@j@hALXLXbAnB^p@h@z@x@pA|FvJZl@~@~BFNDNhA`E|BbJ^|Av@|CRz@VnBFf@HbAH|@PdBb@dDJj@d@bBTr@|A|EzAhFb@jB^`BLh@\\|AjAxENn@r@xBz@~BvAdDj@pAz@xBTj@Nf@r@nBZ`ArBpGb@hA~@dCt@jBnCfHl@bBpCzHtA~Db@jAVp@`CxFDJDJv@fB~A|DbA`C`AzBfAfCx@|Bx@vCd@jC`@~DJdCBrB?l@?xA?h@?x@Ar@EvB@v@?J?HBz@LrATnAZz@v@|An@z@t@r@VRn@b@rAp@B@fCfATJ`DxAnBhAjAz@nAjA`@b@b@j@t@bADDjAxB`BnDjBvDj@jAf@~@lBbDj@dAjHpLb@x@~HtNh@dAz@dBVd@NVRXl@fAn@lAXh@dDfGR`@PXp@r@JV|@zCZlARn@~CjLh@fB~BbJv@vC|@fCz@rBtAlCXxANd@nEnJpAdCdAzBp@tAlBjDZf@`AvAFJJPv@dAfApAVZnA`BhA~A|@tAhCdEnArBTZNTzAtBxBzCxBnC~AtBrEvFpB`CjAlAv@t@nDxCDDZXpCrC`@`@fAhALLpIbJRT`@b@d@f@pArAh@h@t@l@|AnA|EvDVRpAdANJhErDhAjA\\b@|@nApAxBhCtEVd@n@nAdCvE`@t@nAzB~BlEVd@fAxBv@rBp@lBlAbDbA|CRr@ZlA`@|BT~BFtABt@?jAOlDEx@WvCSzCo@rKm@bIAPK`BKxAMdCQnDI|AQrBY`Eo@jJk@rIu@vKGn@GxAcAvPCl@AxBDxCf@~Hl@lI@F\\zD`@zELvAJxADf@HpAPdCHlAf@bGZjElAhQ\\xFDn@PbCNvBDv@L`B^bFXpCd@tCr@vCv@~BbA~B^t@fBhD@?\\f@HNdBfDfAjB^p@dBtCX^|BzChBvBr@v@xF`GpCjD~NrSt@bAlIhLf@r@tDbFVZnCdD|AdBv@x@xHxHRPxE|ErB|B~BzCfBdC\\j@fBxCbAtB|@hBpAzClC|FbB|Dn@jA`@f@p@~@h@n@fBfB`BpAdCxAf@Vz@ZpBp@vNnDtHfBVBNBvBd@pBj@~Bt@lBz@xChBZRXTvCxB|@p@nBzA`Az@jAtAdAzAVf@^t@Xp@`@bAVz@J`@DP`@`CPzBBfADpBJlBTlHBp@JjBFlBFnGBxFDhEBhEBvA?zB?~F?zL?XJtBNbBnAnITlC`@lK?~B?b@I|ACt@I~AGjB?nB@b@DlAPhC\\jFRnCJ|ABd@@x@F`GBhED|FDfGBbA@XBb@RjBZ`Bf@dBN`@N^h@jAd@x@|@nAbDxDzAvBfAjBv@|AZl@Zp@l@jB`@jBNvAFlA@`@AfAGnAARYnBe@jBm@zA]t@_ApBc@zASdAGZGv@Cd@Cj@@hAb@lHPzCBpA?bA?h@EpEA|FDbBFlATjB@LDTVrA^vAt@lBjAbCbApBx@`BN^v@nBX|@Lj@h@bDF`@x@tFF^Z|ALj@z@fDZhAv@~B\\bA\\pAXvATzB@RDpA@\\CtAAXCx@WxDEp@M`BU|CCPGr@APCd@EdACrBCzA@pA@xABxAHjELbCFn@JtABf@NzCXlFFpAJpEL~FTlIBfAJtA^zCJ`BT`CHdAHlAD|A@d@?fF@h@JxB@ZLnBHz@Fz@JxAVnDL~BDp@t@zKDxBGzG\\pAArAF|DPrF\\`DFf@H`@BJhArExAvDN^^f@fApBp@`AfAtArDpDxDrCt@j@LHVt@vC|Bf@`@RPpBpBj@v@b@l@PZz@bBb@`AVl@p@`Cz@jD\\`BR|@~@`E`@dBpBxIh@zBt@zC^|Ab@lBl@lCj@~BHZv@dD~A|GZpAHZb@bBBNdBlHdCpKNn@`@~B^rBPbBF`BItBIfAStDkAnSg@`KARGvASnDGj@Cn@GdC?p@Bx@NxABPVnAd@vALVx@pAj@p@z@x@NLn@b@NHRLr@\\bCn@x@Lp@H~@DhBC~@Gz@KtF{@fEq@lGaAjD[l@ElBKt@Cf@CnHa@zBKtB?\\?vADZBbADPBvBXfB\\n@NtA`@pAd@dBx@`B`AnAz@TRfB`B`BjBrAtBx@zA~@|BBHt@`Cn@tCb@xBnAjFvA|G`@fBJb@Vx@rArDfA|BdBjDp@zA|B~EnAjClB|DbG`M~@vADFJJPTFJPVNR`BjBxAtArAhAxAnAVR~DjD\\XjC|Bx@t@`BpAhCvB|AdAx@d@t@^nAf@b@L|Bt@|Bj@lARv@J@@|BPlCDhA?|@ArBKzBWjBYl@Or@QbBg@fBm@j@UlYeK`@M`Ba@rB_@tBUtBIhCAtBHVBx@Hp@Fz@Jr@N`@HtA\\n@R|@\\@?xAp@rDpBr@`@lB~@\\PpCzAvEbCfE`CNJXNnBdAhDlBvB|@hBh@zFrA~A`@~A`@z@PhKbChFjA|Bb@xARtBXvD\\nEVpFPnBPpB^nFlAdJrBxCh@TBzAXjBPzARb@Fp@JlEl@jALxAVzAXdB^vG|AvA^hEdA`AX`D|@fA`@v@^r@\\\\RhDvBr@d@jC`Cb@`@Z\\d@h@pAhBf@|@lAjCvAdEjAdEnD|LvAjFl@~Ah@dA\\n@`ApA~@dArAbAtA|@FDZNv@Zb@NdBf@VFrAT`BNfBHbIDp@@lLJ|GDnDDfCJXBbALTBdB\\fAZ`@LfBp@fB~@j@\\h@^h@`@|D`DdB`AzE~BnAp@bAh@lBrAtAlAlGxG\\^TZRVZb@|@xAJRd@|@bBpD`@v@fBxD|B|EJRt@tAtHbNj@dArEhI|@pAd@l@NP~@bArB|BpBzBpAvAbAjATVNN`BjBz@z@~E`FHHxAjBnAnBpEjJZl@dA`BrAbB|A~ARPrA`AhBhAzF~C`@Vx@d@DBjCxAxGrDfE~Bt@b@l@`@xBfBdBbBtA~ApDlFNRZf@Zb@vG|JlFbIjGpJz@|An@pABD^XPH?@z@zBz@hCx@nDVxAJv@Fd@JhALtB?V?@JP|AZb@Pp@V`AVnBXtBX@@hCj@fAT^B~@BjAFb@Bd@DH@jCVnCJdADt@Bh@@d@@l@?F?d@AVCVEDAHClA]dAYBAb@KfAUDAl@Ax@Fx@DVBl@Bx@FR@lBLx@Bv@Br@QXA@GFGNC@?NF@BBLAJv@nBr@dAr@r@tA`At@\\PHZL`ANB?zEb@`ADzAHZ@r@DFCHAHBDBDLAJCB\\l@Rb@^v@d@z@`AfBT`@lCxEBHFT@?F@DBBNABLN|@zA\\z@d@pBLn@FTjArEPn@|@xDb@`BZpA|A~FLb@h@hBPb@l@xAXl@Pb@fAbCf@dAr@fBJXb@jA|@`Cx@hCL^fBjFDHn@rBj@pBPx@Fb@Lv@z@hGPn@Lb@^pA`@nAlAfEbA~C~@vCjA|Cp@tAl@|@x@~@`A|@XX|@bA~O~Op@j@`AbA|@fANT@?H@BBHL?HCFA@rA`E|@rDBRV`AJf@FRJ^FN\\`A^v@RXpAnANNr@r@zBzCd@r@pAnCDHDDr@xAFxAfFnKdBvDf@xA\\dAFVb@vAPb@HTt@hB`DpFzAhCbAvBdAvCHV`@|ALh@d@fCl@xE`AnHBVTnBv@pFrAnHhAlGBFhD`S`BnJn@tFJt@PjAFTr@~BvA`Ed@`BvAbFbAjCb@hAP^Zl@zDzHpF`LJRtAdCxBdD|BvCVZjCzCb@`@z@z@xA|A~@jAdA`Bj@bAR`@z@tBp@zBd@pCZlCFlB@zBAf@MfCAXUlBQbAS~@Md@g@~AiAlCMVi@~@q@dAy@fAyArBw@zAEHy@vBc@bBKb@W~BIz@GtBD~BPbCNfAJl@FTf@lBr@jBz@~AfA~A`D~DbA~A~@hBt@rBr@dCf@rCJl@PdAp@xDj@~Bb@xAjD|Jz@~B~@dCv@xBpAxDPh@bB`GzAfGVhAJb@R`Ap@bDRnAd@zCtAzIh@bCl@hBPd@v@lBt@tAt@hAb@j@lBvBzEhEb@^hAbAf@j@VVx@dANVx@tAR\\P`@r@bB^jAt@jDjAxGJf@ZtAZdAL^N^p@|ATf@dAhBz@jAVZFHnArAtApA|BlBrAtA`@j@TZVd@R\\~CdGzEfJNZr@hB^nATz@\\bBZdCd@tGNnAh@vCf@fBFPt@jB`AhBzAzBDD`C~C|BnCzBjCh@j@pDrD~FtGnGxHnBhC|C~DhAzAlApBv@~APb@v@zBp@xCZzBJv@X|BX`BNv@x@xCt@xBTj@r@`BlAzB^r@rArBpBnCrGdJPTfEbGvArBn@dAnApChBlEfBjDN\\rCbGj@rAl@`Bx@`D\\hBRjBLlC@~AEdCOfFOdEQfFm@|T?VY~JGvAYzHI~ASxB]fBOn@c@rA{CpIM\\u@tBo@hBwH|SgBhFgB|Ek@nBi@`Ca@|AqAlGsAxGI\\I\\}A~HMj@[nBMfAc@tHAVMnBk@vKGnB[xQKdJCjAE|BGpFE|BElBMnFEv@In@ABWnA_@fAQ`@]n@gA~A[b@qFbH{ArBcC~DcFxIkCvEoA`CKPgBdDqAtBiBnDiAjCuA|DGTuAbFu@jDUnAIh@w@~FM|AI`AKlBEv@Cl@GrFBfDFfCLvBBb@\\nEZjDRjBZpB\\~Al@dBHTZr@b@v@tAlBtAtAzB~ANFJFlBx@r@TdAXtMtC|FpAx@TpAXb@H^JfAXfA\\`Ad@hAl@bAr@zAvA`@f@TZPVHJn@jAh@pA^rA\\`BPjAL`Al@zFr@vIr@zHh@tED`@v@|E~@jF`BrINv@jAfGlArGl@bDvArHz@zEn@jDbCvMN~@Fj@Hn@`BfUb@hHDl@Bn@DrBExCKvGUlLMpGMtH?|@BpAVzDHn@VdBn@|C`A|CjCjIt@|BTn@j@hBj@fBTp@pCvIz@xBdAnB`@n@jA|AxA|AjAbApChBlAv@dC|A\\XlAbAnAzAb@l@b@x@dChFt@rA^h@tAtAhCfBl@Xz@TvA^lGjATDrGjAvHrAdARvQ~ClARpCh@nBf@ZHRF`Ah@lAz@fAbAt@`A^n@t@fB^zA\\pBJv@|BzPD\\XjBb@xCn@bFjCpQRvANhAf@rDNxB@R@~@Af@Ad@G`AIv@Mt@Mj@I`@ER}@jDcBtG_@vAeB|GcDlMe@|COrBChAA`EBdGC`DMnCC^q@dIGf@a@zEMdDCfF@dDD`FCjG?x@?lE@lSFrBH`AHb@DVNp@b@zAb@`AFN`A~ApCxDjA~AjAzAZ`@hB|BbBhBtCpCrE`E`C`Cl@r@pDzEpA|A`@`@r@r@TTdC|Bf@d@ZXv@t@~AxA`BzA~CtCzAxA`BpBlE`H`AlA~HxHbAdAdBpBX^tApBrBvDvApChA~B`ArBz@dCd@tAVr@^dAZt@f@jA|BjFz@hBd@bApEpJn@tAfCbF|@tAdAjA`@\\b@Zd@XdGfDbFnCdAj@|D|BnFlD~E`CnCrAzAl@\\Pp@ZlBdAbDjBj@ZxAp@`Bh@v@Px@Jx@LvJz@hCZjCn@RFzBv@b@R`Bz@ZR~AhAx@n@pGdFzBtA^R`Ah@tC|AzFzChCpA`@Hn@RlB\\dBPfCV~B`@t@RfC`AjBdAh@\\bAz@lAnAdB~A`@b@NNTV`@f@Z\\nAlBb@r@~BzDn@dAtBfD`@n@^j@fAzAbErFdDpEV\\hBpC|BpDjHdLLPhApAv@v@~@x@tBbBlFlE^Z|AfAlAr@NHrBbApC`A~@\\n@Pr@Z|Ar@lAn@p@`@jA|@xAnApAxAtAlBnClEb@r@j@~@bEzGdAbBhBtCj@bAXh@z@nBr@pBr@hCd@lBH`@Nf@Nf@j@bBP`@j@tArAjChDdGP^`A|BL\\b@~AZnAnBdILb@jBzHdCbKXbAj@bBTl@|@rBj@nAtAhCz@bBb@`AJPh@|ADJFXhAjFH\\d@vAh@nAn@pAjB|DvA~CRf@xBhFzA`FR`ABVXpBzAlIVzALp@?@d@lCr@fEZ~Al@fDdArF`@hB~@fEb@`BjAnE\\zA~@~DJf@b@lBhAfFjAnE~AlFv@dCVt@x@tBj@pAN\\b@|@lBdDf@z@|DlGv@rA|DjG\\f@NTFJzAfCrAbCvAjCHL|ArCT`@tBxDJRbClEfDfGnBnDJPrBtDzBfEvLlTnE~H\\n@Vf@~AtC`ErHzDhHVd@NV`DzFfFjJz@|AfJ`Pj@`Av@|AhGzMzB|EZt@pArCd@`AlBbENZPBb@j@ZTp@^p@^r@n@RZPZ`@bAFPAT@LPxB?F@DBT?FDd@@R@D@V@H?FDb@DZTxA^zATl@FH^`A^bAR^\\jAvAxEv@pCJ\\d@zAZfAZdAnAfE^jArDbM@Bv@zCz@lEf@~D~@nJNzAHrADx@JlCF|AHtB@nCB~G?hA@j@AlD?|@?\\HhAR~@FRHPj@bAd@l@Xd@Xd@l@z@n@dA|@pAh@`Av@|Ad@|@PXp@bBHVLd@BFx@bBR`@Tf@hAnBfB|ClAxBNVV`@LRfAvA|AfBlAnAp@r@pBjBj@l@Z\\p@~@^l@NXTd@d@`APb@t@zAdA~BRd@\\p@bF`J`@v@f@`Ar@tANZP^P`@`@`ANb@N`@h@dBp@~Bj@lB~@jDl@bB`AzCZ|@L\\BFf@tABHHVb@bATh@n@tATl@n@pAXt@r@`Bh@nA\\p@~@bBx@~AR^Xb@t@rAdAnBJPFJp@fADFNVdBrC\\h@^n@z@pAZb@DDx@jA|BhDLPfAjAT\\tAnBT^`@r@bAxAn@z@r@|@@BzAbBhCdChAvAt@`AZb@fCbD`CpDrC~EpAdBpBrCnA~AVZdBtBf@v@xAlCd@x@p@jAd@t@~BrD|@|Ah@hA|@hCTr@Tn@d@jATp@DLLXP\\v@lBL`@Pl@FPB^B\\Ft@RpBRj@D\\`@rDXzBLdABNDv@P|BBd@HxALvB@x@Bh@FbEP|EFzA@jBJnBJb@BLNjBDf@BX`@bD\\xDHz@Bb@DbAL`BLjBNlAFl@DTb@jC^bCHf@Dn@Dj@Df@Db@?LDb@\\pBFXH\\H\\b@nAXv@\\`ATh@|@`CRr@Nn@PrB@NBx@JxBBdBBh@Bn@XvCFh@Ln@Jv@|@rDV`Ap@lCRl@JTBFV`@L\\x@vA`AvALL\\ZRN~ChBrBjAnAp@nAt@ZNzBjAxAz@\\TZVIRs@e@SMIE_E{BaAi@eAo@cBcAo@WSOkB}@{@g@m@g@{@~@MD[f@Qj@ETIb@G\\On@CLMt@?h@PfABb@?Za@~ASn@Ov@ANIn@Cn@GdCC~@@FDVRPB@NF\\LJD@NQx@E^YtCSzBANAjA?|GFNBzA@z@Ch@?HCL@fI?l@?Z@tC@t@BtB@bC@b@?FBlBHhM@dA?j@D`HJhAFd@Hx@RfBNzAx@fHHn@b@jDPvALfADLV~BFh@DZBFJz@P`BTrBJfAL~@`@hEJjAHf@Hp@L|AD^BJNhAFb@Fr@ITBHz@LbCGdCAl@DHJJx@BVHdBBhADx@@nA@PAhAAz@?\\A|C?TC|@?t@"}, "startLocation": {"latLng": {"latitude": 11.7197327, "longitude": 78.0739292}}, "endLocation": {"latLng": {"latitude": 11.***************, "longitude": 76.9558316}}, "steps": [{"distanceMeters": 4959, "staticDuration": "356s", "polyline": {"encodedPolyline": "i_pfAayo{M@AzEaG~DaF~AqBt@gAl@gA`@aAJUfA{CxAeDjAyBhF_Kp@sABIz@mCTo@rAwClEmLdBwDvAqCh@cAFKrAyCVe@Vg@~D{HNWTc@fAuBd@}@`@y@Xk@Xk@\\m@hGiLtDoGXg@n@kAdC}E~DgIl@qApCkGXq@Xm@\\w@h@cAN]`@m@`AmAf@c@zByAbBy@\\QzC{AjCoAhGcD~C{A|CgB~@u@DCXWdAsAbAoBRk@BGf@sAd@mA"}, "startLocation": {"latLng": {"latitude": 11.7197327, "longitude": 78.0739292}}, "endLocation": {"latLng": {"latitude": 11.6938734, "longitude": 78.1100819}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head southeast on NH 44 toward Periyar University, Salem. Main Entrance\nToll road\nPass by Fathima Hotel (on the left)"}, "localizedValues": {"distance": {"text": "5.0 km"}, "staticDuration": {"text": "6 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 611, "staticDuration": "85s", "polyline": {"encodedPolyline": "u}jfA_{v{MHy@Rg@zByFd@kAFOTg@n@yAh@sA@CDUt@mB`@_A^{@LUj@y@"}, "startLocation": {"latLng": {"latitude": 11.6938734, "longitude": 78.1100819}}, "endLocation": {"latLng": {"latitude": 11.6913792, "longitude": 78.11505310000001}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_LEFT", "instructions": "Slight left\nPass by Tata Motors Commercial Vehicle Dealer - V S T Motors Pvt Ltd (on the left)"}, "localizedValues": {"distance": {"text": "0.6 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 422, "staticDuration": "56s", "polyline": {"encodedPolyline": "cnjfAazw{MbBwCtB_Eh@y@z@uADIlBiC"}, "startLocation": {"latLng": {"latitude": 11.6913792, "longitude": 78.11505310000001}}, "endLocation": {"latLng": {"latitude": 11.6892044, "longitude": 78.11822889999999}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Keep left\nPass by Balambiga Fancy Store (on the left)"}, "localizedValues": {"distance": {"text": "0.4 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 32, "staticDuration": "4s", "polyline": {"encodedPolyline": "o`jfA}mx{Mr@W"}, "startLocation": {"latLng": {"latitude": 11.6892044, "longitude": 78.11822889999999}}, "endLocation": {"latLng": {"latitude": 11.6889447, "longitude": 78.11835190000001}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_RIGHT", "instructions": "Slight right at KFC toward NH 44"}, "localizedValues": {"distance": {"text": "32 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 4442, "staticDuration": "332s", "polyline": {"encodedPolyline": "{~ifAunx{Mb@]dCaBvA}@XUb@a@t@}@~BwDPYP[|AmCr@mA~AaCt@cAr@aAd@q@t@aAtC_EJKx@gAl@u@n@y@hBiBx@o@^WLIb@YFEz@c@|@c@zBw@z@Sd@M|B[^EbAGlAEhC@xENv@B|@DzDLtDL`BJvB^NBPDvBl@n@RrD`B`@V~@f@rBdAfAf@fBz@jFnCj@X`Bv@rIdEb@VbCnAZJb@^hBv@lBp@~A`@xBXz@D`A@n@Bx@BpDNP@N@R?fGXjMf@l@B|ET"}, "startLocation": {"latLng": {"latitude": 11.6889447, "longitude": 78.11835190000001}}, "endLocation": {"latLng": {"latitude": 11.654597299999999, "longitude": 78.1213006}}, "navigationInstruction": {"maneuver": "MERGE", "instructions": "Merge onto NH 44\nPass by Salem By Pass (on the right in 1 km)"}, "localizedValues": {"distance": {"text": "4.4 km"}, "staticDuration": {"text": "6 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 1250, "staticDuration": "87s", "polyline": {"encodedPolyline": "ghcfAcay{MxLj@`CJrEPnAFjADbAD\\BnI\\tEPZ@R@~@BrADbBHtBNv@FtAH|AF"}, "startLocation": {"latLng": {"latitude": 11.654597299999999, "longitude": 78.1213006}}, "endLocation": {"latLng": {"latitude": 11.6434094, "longitude": 78.1202429}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Keep right to stay on NH 44\nPass by Sri Jyotirling Transport (on the right in 350m)"}, "localizedValues": {"distance": {"text": "1.2 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 52641, "staticDuration": "3033s", "polyline": {"encodedPolyline": "ibafAozx{MNMh@@lDBfBEp@CVC`@CLC\\MZUR]Ha@@YGc@Sc@SQg@QWCM?o@Ja@TSTUf@Kn@I~AGhACh@El@?JAn<PERSON>Hr@Pj@T^`@`@\\RPFp@NL@v@Bh@?pB?lC@fMB\\?rI?nE?xEDnBFVBXBd@FzB\\bB^~Br@pAf@zC|Ax@h@r@h@rAhAt@r@\\`@rAdB|@vA|@`Bt@dBvGfQn@pAh@|@hAzATT~@|@z@l@bDdB~Al@tCv@d@LjQzE`AXnBh@rBf@`Bb@x@XvL`DhDbARHpAj@f@TTNTLb@Xr@f@dAz@dAdA`BxBdBhCx@|AdA|BTh@n@rAr@zAXd@JRh@v@\\b@h@n@tAlAvCrBx@j@fA`A`AhARVrBxCvAzBz@jAhFzHlBpC~@rAjAtA^b@hAjANLXV|B|BdAdAvCpChAfAl@n@vAfBbAzAlCbFLZNXd@hA`@jAV~@h@`Bn@tB^nA~@rDpArFDNzAxExArD`B|D`AvBTb@^t@l@~@^j@LR`BrB|@pAPVzBdE`A`CPb@Vp@HT~@`CVn@Rd@lB~EjAvCd@jAb@jAXv@ZdAp@vBlDlLdAhDh@dBTn@jBpFZbAzA|EfB~FL\\Tp@xCdJ~@pBf@~@HL\\l@pAjBdAlBlAhCf@nAV|@Pd@fC~HlA|CtB`FlBtE`CvF|B|FrC|HZh@\\l@RLZr@\\`AbBxETj@h@xAb@lAj@`Bf@vAbAlBz@vARj@Hd@\\l@hBrCJNh@`AnErHb@p@p@hAfD|FlAfBl@t@rFtGV\\hBxBbFlGhDbFtA~BtBpDNTrDnGxEvHnCpE`B|CVf@dB|DnAbD\\~@pDjJVj@f@vAl@bB`@tA~CtL\\hAJ`@XbAJb@J^XdAf@dBJb@l@`BHRBHL`@L^p@tBnAvDb@zAl@xBTt@b@`BZdAfAfDn@hBh@tAlBpEXn@|AtC^x@v@|AtAnClCbGd@`AVd@j@hALXLXbAnB^p@h@z@x@pA|FvJZl@~@~BFNDNhA`E|BbJ^|Av@|CRz@VnBFf@HbAH|@PdBb@dDJj@d@bBTr@|A|EzAhFb@jB^`BLh@\\|AjAxENn@r@xBz@~BvAdDj@pAz@xBTj@Nf@r@nBZ`ArBpGb@hA~@dCt@jBnCfHl@bBpCzHtA~Db@jAVp@`CxFDJDJv@fB~A|DbA`C`AzBfAfCx@|Bx@vCd@jC`@~DJdCBrB?l@?xA?h@?x@Ar@EvB@v@?J?HBz@LrATnAZz@v@|An@z@t@r@VRn@b@rAp@B@fCfATJ`DxAnBhAjAz@nAjA`@b@b@j@t@bADDjAxB`BnDjBvDj@jAf@~@lBbDj@dAjHpLb@x@~HtNh@dAz@dBVd@NVRXl@fAn@lAXh@dDfGR`@PXp@r@JV|@zCZlARn@~CjLh@fB~BbJv@vC|@fCz@rBtAlCXxANd@nEnJpAdCdAzBp@tAlBjDZf@`AvAFJJPv@dAfApAVZnA`BhA~A|@tAhCdEnArBTZNTzAtBxBzCxBnC~AtBrEvFpB`CjAlAv@t@nDxCDDZXpCrC`@`@fAhALLpIbJRT`@b@d@f@pArAh@h@t@l@|AnA|EvDVRpAdANJhErDhAjA\\b@|@nApAxBhCtEVd@n@nAdCvE`@t@nAzB~BlEVd@fAxBv@rBp@lBlAbDbA|CRr@ZlA`@|BT~BFtABt@?jAOlDEx@WvCSzCo@rKm@bIAPK`BKxAMdCQnDI|AQrBY`Eo@jJk@rIu@vKGn@GxAcAvPCl@AxBDxCf@~Hl@lI@F\\zD`@zELvAJxADf@HpAPdCHlAf@bGZjElAhQ\\xFDn@PbCNvBDv@L`B^bFXpCd@tCr@vCv@~BbA~B^t@fBhD@?\\f@HNdBfDfAjB^p@dBtCX^|BzChBvBr@v@xF`GpCjD~NrSt@bAlIhLf@r@tDbFVZnCdD|AdBv@x@xHxHRPxE|ErB|B~BzCfBdC\\j@fBxCbAtB|@hBpAzClC|FbB|Dn@jA`@f@p@~@h@n@fBfB`BpAdCxAf@Vz@ZpBp@vNnDtHfBVBNBvBd@pBj@~Bt@lBz@xChBZRXTvCxB|@p@nBzA`Az@jAtAdAzAVf@^t@Xp@`@bAVz@J`@DP`@`CPzBBfADpBJlBTlHBp@JjBFlBFnGBxFDhEBhEBvA?zB?~F?zL?XJtBNbBnAnITlC`@lK?~B?b@I|ACt@I~AGjB?nB@b@DlAPhC\\jFRnCJ|ABd@@x@F`GBhED|FDfGBbA@XBb@RjBZ`Bf@dBN`@N^h@jAd@x@|@nAbDxDzAvBfAjBv@|AZl@Zp@l@jB`@jBNvAFlA@`@AfAGnAARYnBe@jBm@zA]t@_ApBc@zASdAGZGv@Cd@Cj@@hAb@lHPzCBpA?bA?h@EpEA|FDbBFlATjB@LDTVrA^vAt@lBjAbCbApBx@`BN^v@nBX|@Lj@h@bDF`@x@tFF^Z|ALj@z@fDZhAv@~B\\bA\\pAXvATzB@RDpA@\\CtAAXCx@WxDEp@M`BU|CCPGr@APCd@EdACrBCzA@pA@xABxAHjELbCFn@JtABf@NzCXlFFpAJpEL~FTlIBfAJtA^zCJ`BT`CHdAHlAD|A@d@?fF@h@JxB@ZLnBHz@Fz@JxAVnDL~BDp@t@zKDxBGzG"}, "startLocation": {"latLng": {"latitude": 11.6434094, "longitude": 78.1202429}}, "endLocation": {"latLng": {"latitude": 11.4446651, "longitude": 77.7209209}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_LEFT", "instructions": "Slight left at TVS FRIENDS ENGINEERING WORKS onto NH544\nToll road\nPass by Sri Neeviyin New Mass Cooldrinks (on the left in 42.6 km)"}, "localizedValues": {"distance": {"text": "52.6 km"}, "staticDuration": {"text": "51 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 48, "staticDuration": "7s", "polyline": {"encodedPolyline": "ehzdAwzjyM\\pA"}, "startLocation": {"latLng": {"latitude": 11.4446651, "longitude": 77.7209209}}, "endLocation": {"latLng": {"latitude": 11.444517099999999, "longitude": 77.72050999999999}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_LEFT", "instructions": "Slight left toward Service Road"}, "localizedValues": {"distance": {"text": "48 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 1202, "staticDuration": "140s", "polyline": {"encodedPolyline": "ggzdAexjyMArAF|DPrF\\`DFf@H`@BJhArExAvDN^^f@fApBp@`AfAtArDpDxDrCt@j@LH"}, "startLocation": {"latLng": {"latitude": 11.444517099999999, "longitude": 77.72050999999999}}, "endLocation": {"latLng": {"latitude": 11.4399436, "longitude": 77.7111121}}, "navigationInstruction": {"maneuver": "MERGE", "instructions": "Merge onto Service Road\nPass by Ag. Engineering Works (on the left)"}, "localizedValues": {"distance": {"text": "1.2 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 32, "staticDuration": "4s", "polyline": {"encodedPolyline": "sjydAm}hyMVt@"}, "startLocation": {"latLng": {"latitude": 11.4399436, "longitude": 77.7111121}}, "endLocation": {"latLng": {"latitude": 11.439819, "longitude": 77.7108447}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_RIGHT", "instructions": "Slight right toward NH544"}, "localizedValues": {"distance": {"text": "32 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 23820, "staticDuration": "1454s", "polyline": {"encodedPolyline": "{iydAw{hyMvC|Bf@`@RPpBpBj@v@b@l@PZz@bBb@`AV<PERSON>@p@`Cz@jD\\`BR|@~@`E`@dBpBxIh@zBt@zC^|Ab@lBl@lCj@~BHZv@dD~A|GZpAHZb@bBBNdBlHdCpKNn@`@~B^rBPbBF`BItBIfAStDkAnSg@`KARGvASnDGj@Cn@GdC?p@Bx@NxABPVnAd@vALVx@pAj@p@z@x@NLn@b@NHRLr@\\bCn@x@Lp@H~@DhBC~@Gz@KtF{@fEq@lGaAjD[l@ElBKt@Cf@CnHa@zBKtB?\\?vADZBbADPBvBXfB\\n@NtA`@pAd@dBx@`B`AnAz@TRfB`B`BjBrAtBx@zA~@|BBHt@`Cn@tCb@xBnAjFvA|G`@fBJb@Vx@rArDfA|BdBjDp@zA|B~EnAjClB|DbG`M~@vADFJJPTFJPVNR`BjBxAtArAhAxAnAVR~DjD\\XjC|Bx@t@`BpAhCvB|AdAx@d@t@^nAf@b@L|Bt@|Bj@lARv@J@@|BPlCDhA?|@ArBKzBWjBYl@Or@QbBg@fBm@j@UlYeK`@M`Ba@rB_@tBUtBIhCAtBHVBx@Hp@Fz@Jr@N`@HtA\\n@R|@\\@?xAp@rDpBr@`@lB~@\\PpCzAvEbCfE`CNJXNnBdAhDlBvB|@hBh@zFrA~A`@~A`@z@PhKbChFjA|Bb@xARtBXvD\\nEVpFPnBPpB^nFlAdJrBxCh@TBzAXjBPzARb@Fp@JlEl@jALxAVzAXdB^vG|AvA^hEdA`AX`D|@fA`@v@^r@\\\\RhDvBr@d@jC`Cb@`@Z\\d@h@pAhBf@|@lAjCvAdEjAdEnD|LvAjFl@~Ah@dA\\n@`ApA~@dArAbAtA|@FDZNv@Zb@NdBf@VFrAT`BNfBHbIDp@@lLJ|GDnDDfCJXBbALTBdB\\fAZ`@LfBp@fB~@j@\\h@^h@`@|D`DdB`AzE~BnAp@bAh@lBrAtAlAlGxG\\^TZRVZb@|@xAJRd@|@bBpD`@v@fBxD|B|EJRt@tAtHbNj@dArEhI|@pAd@l@NP~@bArB|BpBzBpAvAbAjATVNN`BjBz@z@~E`FHHxAjBnAnBpEjJZl@dA`BrAbB|A~ARPrA`AhBhAzF~C`@Vx@d@DBjCxAxGrDfE~Bt@b@l@`@xBfBdBbBtA~ApDlFNRZf@Zb@vG|JlFbIjGpJz@|An@pABD"}, "startLocation": {"latLng": {"latitude": 11.439819, "longitude": 77.7108447}}, "endLocation": {"latLng": {"latitude": 11.2931314, "longitude": 77.5904803}}, "navigationInstruction": {"maneuver": "MERGE", "instructions": "Merge onto NH544\nPass by Police Car Check - Pollution Certificate (on the left)"}, "localizedValues": {"distance": {"text": "23.8 km"}, "staticDuration": {"text": "24 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 529, "staticDuration": "71s", "polyline": {"encodedPolyline": "au|cAokqxM^XPH?@z@zBz@hCx@nDVxAJv@Fd@JhALtB?V?@JP"}, "startLocation": {"latLng": {"latitude": 11.2931314, "longitude": 77.5904803}}, "endLocation": {"latLng": {"latitude": 11.2915789, "longitude": 77.5859986}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_LEFT", "instructions": "Slight left\nPass by EAGLE FURNITIRE (on the left in 500m)"}, "localizedValues": {"distance": {"text": "0.5 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 1405, "staticDuration": "137s", "polyline": {"encodedPolyline": "kk|cAoopxM|AZb@Pp@V`AVnBXtBX@@hCj@fAT^B~@BjAFb@Bd@DH@jCVnCJdADt@Bh@@d@@l@?F?d@AVCVEDAHClA]dAYBAb@KfAUDAl@Ax@Fx@DVBl@Bx@FR@lBLx@Bv@Br@QXA"}, "startLocation": {"latLng": {"latitude": 11.2915789, "longitude": 77.5859986}}, "endLocation": {"latLng": {"latitude": 11.2792526, "longitude": 77.5849417}}, "navigationInstruction": {"maneuver": "MERGE", "instructions": "Merge onto Kavindapadi - Kanjikovil - Perundurai Rd/Perundurai - Kanjikovil Rd\nPass by Krishna Mess - <PERSON><PERSON><PERSON> (on the left)"}, "localizedValues": {"distance": {"text": "1.4 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 626, "staticDuration": "91s", "polyline": {"encodedPolyline": "i~ycA{hpxM@GFGNC@?NF@BBLAJv@nBr@dAr@r@tA`At@\\PHZL`ANB?zEb@`ADzAHZ@r@D"}, "startLocation": {"latLng": {"latitude": 11.2792526, "longitude": 77.5849417}}, "endLocation": {"latLng": {"latitude": 11.2746354, "longitude": 77.58270499999999}}, "navigationInstruction": {"maneuver": "ROUNDABOUT_LEFT", "instructions": "At the roundabout, take the 3rd exit onto Perundhurai - Bhavani Rd/Thiruvaluvar Street\nPass by Aroma Bakery Perundurai (on the left)"}, "localizedValues": {"distance": {"text": "0.6 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 4035, "staticDuration": "342s", "polyline": {"encodedPolyline": "oayc<PERSON>{zoxMFCHAHBDBDLAJCB\\l@Rb@^v@d@z@`AfBT`@lCxEBHFT@?F@DBBNABLN|@zA\\z@d@pBLn@FTjArEPn@|@xDb@`BZpA|A~FLb@h@hBPb@l@xAXl@Pb@fAbCf@dAr@fBJXb@jA|@`Cx@hCL^fBjFDHn@rBj@pBPx@Fb@Lv@z@hGPn@Lb@^pA`@nAlAfEbA~C~@vCjA|Cp@tAl@|@x@~@`A|@XX|@bA~O~Op@j@`AbA|@fANT@?H@BBHL?HCFA@rA`E|@rDBRV`AJf@"}, "startLocation": {"latLng": {"latitude": 11.2746354, "longitude": 77.58270499999999}}, "endLocation": {"latLng": {"latitude": 11.2579259, "longitude": 77.5506927}}, "navigationInstruction": {"maneuver": "ROUNDABOUT_LEFT", "instructions": "At Old Bustand Cir, take the 3rd exit onto Olapalayam - Modakurichi Rd/Perundurai-Coimbatore Rd\nPass by Drychem India Pvt Ltd. Perundurai (on the right)"}, "localizedValues": {"distance": {"text": "4.0 km"}, "staticDuration": {"text": "6 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 553, "staticDuration": "45s", "polyline": {"encodedPolyline": "ayucAyrixMFRJ^FN\\`A^v@RXpAnANNr@r@zBzCd@r@pAnCDHDDr@xA"}, "startLocation": {"latLng": {"latitude": 11.2579259, "longitude": 77.5506927}}, "endLocation": {"latLng": {"latitude": 11.2550897, "longitude": 77.5465773}}, "navigationInstruction": {"maneuver": "STRAIGHT", "instructions": "Continue straight onto Perundurai-Coimbatore Rd"}, "localizedValues": {"distance": {"text": "0.6 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 58994, "staticDuration": "3332s", "polyline": {"encodedPolyline": "igucAcyhxMFxAfFnKdBvDf@xA\\dAFVb@vAPb@HTt@hB`DpFzAhCbAvBdAvCHV`@|ALh@d@fCl@xE`AnHBVTnBv@pFrAnHhAlGBFhD`S`BnJn@tFJt@PjAFTr@~BvA`Ed@`BvAbFbAjCb@hAP^Zl@zDzHpF`LJRtAdCxBdD|BvCVZjCzCb@`@z@z@xA|A~@jAdA`Bj@bAR`@z@tBp@zBd@pCZlCFlB@zBAf@MfCAXUlBQbAS~@Md@g@~AiAlCMVi@~@q@dAy@fAyArBw@zAEHy@vBc@bBKb@W~BIz@GtBD~BPbCNfAJl@FTf@lBr@jBz@~AfA~A`D~DbA~A~@hBt@rBr@dCf@rCJl@PdAp@xDj@~Bb@xAjD|Jz@~B~@dCv@xBpAxDPh@bB`GzAfGVhAJb@R`Ap@bDRnAd@zCtAzIh@bCl@hBPd@v@lBt@tAt@hAb@j@lBvBzEhEb@^hAbAf@j@VVx@dANVx@tAR\\P`@r@bB^jAt@jDjAxGJf@ZtAZdAL^N^p@|ATf@dAhBz@jAVZFHnArAtApA|BlBrAtA`@j@TZVd@R\\~CdGzEfJNZr@hB^nATz@\\bBZdCd@tGNnAh@vCf@fBFPt@jB`AhBzAzBDD`C~C|BnCzBjCh@j@pDrD~FtGnGxHnBhC|C~DhAzAlApBv@~APb@v@zBp@xCZzBJv@X|BX`BNv@x@xCt@xBTj@r@`BlAzB^r@rArBpBnCrGdJPTfEbGvArBn@dAnApChBlEfBjDN\\rCbGj@rAl@`Bx@`D\\hBRjBLlC@~AEdCOfFOdEQfFm@|T?VY~JGvAYzHI~ASxB]fBOn@c@rA{CpIM\\u@tBo@hBwH|SgBhFgB|Ek@nBi@`Ca@|AqAlGsAxGI\\I\\}A~HMj@[nBMfAc@tHAVMnBk@vKGnB[xQKdJCjAE|BGpFE|BElBMnFEv@In@ABWnA_@fAQ`@]n@gA~A[b@qFbH{ArBcC~DcFxIkCvEoA`CKPgBdDqAtBiBnDiAjCuA|DGTuAbFu@jDUnAIh@w@~FM|AI`AKlBEv@Cl@GrFBfDFfCLvBBb@\\nEZjDRjBZpB\\~Al@dBHTZr@b@v@tAlBtAtAzB~ANFJFlBx@r@TdAXtMtC|FpAx@TpAXb@H^JfAXfA\\`Ad@hAl@bAr@zAvA`@f@TZPVHJn@jAh@pA^rA\\`BPjAL`Al@zFr@vIr@zHh@tED`@v@|E~@jF`BrINv@jAfGlArGl@bDvArHz@zEn@jDbCvMN~@Fj@Hn@`BfUb@hHDl@Bn@DrBExCKvGUlLMpGMtH?|@BpAVzDHn@VdBn@|C`A|CjCjIt@|BTn@j@hBj@fBTp@pCvIz@xBdAnB`@n@jA|AxA|AjAbApChBlAv@dC|A\\XlAbAnAzAb@l@b@x@dChFt@rA^h@tAtAhCfBl@Xz@TvA^lGjATDrGjAvHrAdARvQ~ClARpCh@nBf@ZHRF`Ah@lAz@fAbAt@`A^n@t@fB^zA\\pBJv@|BzPD\\XjBb@xCn@bFjCpQRvANhAf@rDNxB@R@~@Af@Ad@G`AIv@Mt@Mj@I`@ER}@jDcBtG_@vAeB|GcDlMe@|COrBChAA`EBdGC`DMnCC^q@dIGf@a@zEMdDCfF@dDD`FCjG?x@?lE@lSFrBH`AHb@DVNp@b@zAb@`AFN`A~ApCxDjA~AjAzAZ`@hB|BbBhBtCpCrE`E`C`Cl@r@pDzEpA|A`@`@r@r@TTdC|Bf@d@ZXv@t@~AxA`BzA~CtCzAxA`BpBlE`H`AlA~HxHbAdAdBpBX^tApBrBvDvApChA~B`ArBz@dCd@tAVr@^dAZt@f@jA|BjFz@hBd@bApEpJn@tAfCbF|@tAdAjA`@\\b@Zd@XdGfDbFnCdAj@|D|BnFlD~E`CnCrAzAl@\\Pp@ZlBdAbDjBj@ZxAp@`Bh@v@Px@Jx@LvJz@hCZjCn@RFzBv@b@R`Bz@ZR~AhAx@n@pGdFzBtA^R`Ah@tC|AzFzChCpA`@Hn@RlB\\dBPfCV~B`@t@RfC`AjBdAh@\\bAz@lAnAdB~A`@b@NNTV`@f@Z\\nAlBb@r@~BzDn@dAtBfD`@n@^j@fAzAbErFdDpEV\\hBpC|BpDjHdLLPhApAv@v@~@x@tBbBlFlE^Z|AfAlAr@NHrBbApC`A~@\\n@Pr@Z|Ar@lAn@p@`@jA|@xAnApAxAtAlBnClEb@r@j@~@bEzGdAbBhBtCj@bAXh@z@nBr@pBr@hCd@lBH`@Nf@Nf@j@bBP`@j@tArAjChDdGP^`A|BL\\b@~AZnAnBdILb@jBzHdCbKXbAj@bBTl@|@rBj@nAtAhCz@bBb@`AJPh@|ADJFXhAjFH\\d@vAh@nAn@pAjB|DvA~CRf@xBhFzA`FR`ABVXpBzAlIVzALp@?@d@lCr@fEZ~Al@fDdArF`@hB~@fEb@`BjAnE\\zA~@~DJf@b@lBhAfFjAnE~AlFv@dCVt@x@tBj@pAN\\b@|@lBdDf@z@|DlGv@rA|DjG\\f@NTFJzAfCrAbCvAjCHL|ArCT`@tBxDJRbClEfDfGnBnDJPrBtDzBfEvLlTnE~H\\n@Vf@~AtC`ErHzDhHVd@NV`DzFfFjJz@|AfJ`Pj@`Av@|AhGzMzB|EZt@pArCd@`AlBbENZ"}, "startLocation": {"latLng": {"latitude": 11.2550897, "longitude": 77.5465773}}, "endLocation": {"latLng": {"latitude": 11.0646259, "longitude": 77.09397469999999}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_RIGHT", "instructions": "Slight right onto NH544\nToll road\nParts of this road may be closed at certain times or days"}, "localizedValues": {"distance": {"text": "59.0 km"}, "staticDuration": {"text": "56 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 255, "staticDuration": "21s", "polyline": {"encodedPolyline": "}`pbAilpuMPBb@j@ZTp@^p@^r@n@RZPZ`@bAFP"}, "startLocation": {"latLng": {"latitude": 11.0646259, "longitude": 77.09397469999999}}, "endLocation": {"latLng": {"latitude": 11.0630625, "longitude": 77.0923485}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_LEFT", "instructions": "Slight left"}, "localizedValues": {"distance": {"text": "0.3 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 255, "staticDuration": "19s", "polyline": {"encodedPolyline": "cwobAebpuMAT@LPxB?F@DBT?FDd@@R@D@V@H?FDb@DZTxA"}, "startLocation": {"latLng": {"latitude": 11.0630625, "longitude": 77.0923485}}, "endLocation": {"latLng": {"latitude": 11.062704499999999, "longitude": 77.0900431}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_RIGHT", "instructions": "Slight right"}, "localizedValues": {"distance": {"text": "0.3 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 12490, "staticDuration": "1407s", "polyline": {"encodedPolyline": "{tobAwsouM^zATl@FH^`A^bAR^\\jAvAxEv@pCJ\\d@zAZfAZdAnAfE^jArDbM@Bv@zCz@lEf@~D~@nJNzAHrADx@JlCF|AHtB@nCB~G?hA@j@AlD?|@?\\HhAR~@FRHPj@bAd@l@Xd@Xd@l@z@n@dA|@pAh@`Av@|Ad@|@PXp@bBHVLd@BFx@bBR`@Tf@hAnBfB|ClAxBNVV`@LRfAvA|AfBlAnAp@r@pBjBj@l@Z\\p@~@^l@NXTd@d@`APb@t@zAdA~BRd@\\p@bF`J`@v@f@`Ar@tANZP^P`@`@`ANb@N`@h@dBp@~Bj@lB~@jDl@bB`AzCZ|@L\\BFf@tABHHVb@bATh@n@tATl@n@pAXt@r@`Bh@nA\\p@~@bBx@~AR^Xb@t@rAdAnBJPFJp@fADFNVdBrC\\h@^n@z@pAZb@DDx@jA|BhDLPfAjAT\\tAnBT^`@r@bAxAn@z@r@|@@BzAbBhCdChAvAt@`AZb@fCbD`CpDrC~EpAdBpBrCnA~AVZdBtBf@v@xAlCd@x@p@jAd@t@~BrD|@|Ah@hA|@hCTr@Tn@d@jATp@DLLXP\\v@lBL`@Pl@FPB^B\\Ft@RpBRj@D\\`@rDXzBLdABNDv@P|BBd@HxALvB@x@Bh@FbEP|EFzA@jBJnBJb@BLNjBDf@BX`@bD\\xDHz@Bb@DbAL`BLjBNlAFl@DTb@jC^bCHf@Dn@Dj@Df@Db@?LDb@\\pBFXH\\H\\b@nAXv@\\`ATh@|@`CRr@Nn@PrB@NBx@JxBBdBBh@Bn@XvCFh@Ln@Jv@|@rDV`Ap@lCRl@JTBFV`@L\\x@vA`AvALL\\ZRN~ChBrBjAnAp@nAt@ZNzBjAxAz@\\TZV"}, "startLocation": {"latLng": {"latitude": 11.062704499999999, "longitude": 77.0900431}}, "endLocation": {"latLng": {"latitude": 11.016128199999999, "longitude": 76.9896128}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "Continue onto Avinashi Rd/Avinashi - Coimbatore Rd/Peelamedu - Pudur Main Rd/Perundurai-Coimbatore Rd\nContinue to follow Avinashi Rd/Perundurai-Coimbatore Rd\nPass by Sampath Hospital (on the left in 11.1 km)"}, "localizedValues": {"distance": {"text": "12.5 km"}, "staticDuration": {"text": "23 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 537, "staticDuration": "64s", "polyline": {"encodedPolyline": "yqfbAa`|tMIRs@e@SMIE_E{BaAi@eAo@cBcAo@WSOkB}@{@g@m@g@"}, "startLocation": {"latLng": {"latitude": 11.016128199999999, "longitude": 76.9896128}}, "endLocation": {"latLng": {"latitude": 11.020135999999999, "longitude": 76.99212159999999}}, "navigationInstruction": {"maneuver": "UTURN_RIGHT", "instructions": "Make a U-turn\nPass by KFC (on the left)"}, "localizedValues": {"distance": {"text": "0.5 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 1193, "staticDuration": "187s", "polyline": {"encodedPolyline": "{jgbAwo|tM{@~@MD[f@Qj@ETIb@G\\On@CLMt@?h@PfABb@?Za@~ASn@Ov@ANIn@Cn@GdCC~@@FDVRPB@NF\\LJD@NQx@E^YtCSzBANAjA?|GFNBzA@z@"}, "startLocation": {"latLng": {"latitude": 11.020135999999999, "longitude": 76.99212159999999}}, "endLocation": {"latLng": {"latitude": 11.***************, "longitude": 76.**************}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left to stay on NH 181\nPass by Sri <PERSON>a Dental College and Hospital (on the right in 450m)"}, "localizedValues": {"distance": {"text": "1.2 km"}, "staticDuration": {"text": "3 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 2373, "staticDuration": "267s", "polyline": {"encodedPolyline": "yqgbAsoztMCh@?HCL@fI?l@?Z@tC@t@BtB@bC@b@?FBlBHhM@dA?j@D`HJhAFd@Hx@RfBNzAx@fHHn@b@jDPvALfADLV~BFh@DZBFJz@P`BTrBJfAL~@`@hEJjAHf@Hp@L|AD^BJNhAFb@Fr@IT"}, "startLocation": {"latLng": {"latitude": 11.***************, "longitude": 76.**************}}, "endLocation": {"latLng": {"latitude": 11.0189109, "longitude": 76.9603631}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Keep right to stay on NH 181\nPass by S Indian Bank (on the left in 1.9 km)"}, "localizedValues": {"distance": {"text": "2.4 km"}, "staticDuration": {"text": "4 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 702, "staticDuration": "106s", "polyline": {"encodedPolyline": "ecgbAgivtMBHz@LbCGdCAl@DHJJx@BVHdBBhADx@@nA@PAhAAz@?\\A|C?TC|@?t@"}, "startLocation": {"latLng": {"latitude": 11.0189109, "longitude": 76.9603631}}, "endLocation": {"latLng": {"latitude": 11.***************, "longitude": 76.9558316}}, "navigationInstruction": {"maneuver": "ROUNDABOUT_LEFT", "instructions": "At the roundabout, take the 1st exit and stay on NH 181\nDrive along Power House (on the right for 400m)"}, "localizedValues": {"distance": {"text": "0.7 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "173 km"}, "duration": {"text": "3 hours 14 mins"}, "staticDuration": {"text": "3 hours 14 mins"}}}], "distanceMeters": 353432, "duration": "23475s", "staticDuration": "23475s", "polyline": {"encodedPolyline": "sx|mAe`oyMKPb@Zn@d@l@`@LHp@h@d@Z|@j@h@`@h@\\fAx@B@LHf@\\PLn@b@VPRN`@Xl@`@PLDBp@d@XRh@^RNf@\\^V\\V~@l@PNJHPJjAn@FB\\NXLd@Tz@`@n@XRJFBLFn@\\ZNx@\\TJPDt@RNBn@DX@V@V?bDDT?rCBL?p@@pA?j@?h@Ar@AfAAtAEd@G|AQz@SDAnB}@zBeAdCmAbFaCnAm@zE}B|Aq@|BiAHE^[PQf@o@fAuA\\a@dB{Bj@u@`@i@t@_At@aAfDoEl@y@n@{@j@y@zB}C|@iAf@c@n@SxDu@lB]nAUr@OPCnCi@|AYl@MjB_@|A[fAQjDg@`BSd@ED?x@G`Ge@`@ETA`@CNANC|Ec@z@IbAKbCUn@IfD_@fAOzEq@rEo@d@KHAbDe@|@IrFq@LAlBSTAv@I\\GHAREpBa@j@MHCVIx@WvAe@r@UhAc@`@MDANEHEv@[\\Mr@U|Ai@TIz@UHCtA_@x@SbBc@H]TeATaAT{@DQJa@F[Jg@BIT_ADOPu@FUR_Ap@cDRcATiAd@gBLa@Rk@^y@JULYVk@nAqCdA_CVi@xAiDVk@v@gBb@aALYjAeCtBcFh@oAPa@N]Te@\\o@DE\\WZMf@KRGFALClBW`BYn@KFApAOdAOb@ILAxBa@zCe@bAO\\Ev@M|AW^EdAGf@Cb@ArAA^AV?dAA`@?t@?dA?X?^?F?~@I@?j@MPGPGPKbAu@t@m@~@w@DElAeAzBiBhA}@XY\\WTSvBeBXWNM^[`Ay@VU|AmAb@_@^YNMzCmC\\WLQVs@Hg@@KVyBVaCBSXiCTqBDa@Fg@@KJqAH_ALgCBc@?ILcCJuAF_A@WFwA?k@?q@Ae@IyA?c@AmA?_@A_@CoAEaB?QAYCcAAa@GkA?YCIBCDAB?f@@bI~@l@HVD`@Fz@Hl@Hp@Jv@JVBF?vEf@j@FnDVtBFt@?nBB`@?`EDj@?`BBhDD~@@`BB^?nGHvCAJA`@E|Eo@`Ca@fG}@|Ba@bLqB`Cc@|A[xAY\\GfB_@|KoBtBYh@INA^E~AKJAjE?v@@fBKnFa@hGe@h@Gx@G~D]j@EtBUvBKzBKlEUfAIj@GrCErA?`BNTB~@JlAPvARdAN~ATj@HvBTj@HfBF`@@zFPzAD|GRB?|@B~DJ`EN~AFJ@vFTjBJfDVnAJxGn@fANtHfAvIlA|Df@dAJ|BR\\BnCTZBb@BhL|@XBnBZRHfAPPDd@NRHl@\\\\V~@hA@@t@jAlAhAj@^zAf@jB~@v@VfBJl@Jz@NvAJfAVzBt@dA`@B?VHRHTP`@Zn@n@JJ`C`C~EnEJHbBzALJhAv@FBdG~BhBr@jE`BNF~ElBfBp@zCjAfBp@ND~An@tChAnFpBp@T`Ab@VJj@XTHt@\\p@ZfAd@vB`A|@b@|@^XDl@IPIJOd@yDD_@j@iFFm@L_AJw@TcBR{@FUJWNGJ]p@mB@AXcAj@eBDQx@qBd@cANWFIFKt@aBxC_FPWt@yAx@{AHObCqEpD{HzAqDtAoCf@{@Ra@HQxAaClBiCLOb@g@RUlAkAhA{@XUx@i@h@Yr@]`Bo@fBaApGqDhAu@\\[^Uv@m@f@_@dAeATW|ByC?[b@m@`@i@TYX_@NSbHwIbB{BnH}IfEoElAkA^?hCkCNOzAeBnAgA|AeAtBmAfAo@jBgAtA_AZWv@q@|AqA`@]nDwCpCaCRQnBsAfEqDfAmAl@u@z@qA|BaEh@aAp@_Bz@iCnB{H~EmRV_A\\qAVw@j@uAhBgDh@iAn@gAp@y@nAoAt@q@VUxAkAxJcHnA{@zAgA\\UROp@g@hAw@n@e@\\Wb@_@TSb@a@RSpA_B~BcDfA{AVa@Zi@\\o@f@mAXo@ZeAf@uBX_BJg@fAyFJe@nAwGH]ZqAb@eBjCyI~@cEJe@VkAR{@R{@Ry@z@uCx@uCZ_A\\aA^{@P_@v@wAPYPYn@aAx@gAHKJO`FsGhBiCj@_AZm@v@kBZ{@Vu@ZkARaAH[Hk@PkARqBFcADeADaAHwARiBt@mDxAoHf@_DDa@R{Bv@}PF_AP}ATqAfAaGFYXsBNgAJeAHkA`@gEVuCD_@Dc@D_@PsBLyAFs@D_@L{AHo@^_DD_@Jg@XmAx@qCx@qBn@oArAwBzA{BbBgCdBeCvFqIT]f@y@|@uAf@w@b@s@j@aALWh@mAJUXs@Lc@x@{CJg@RiAxAkIFYLq@xBuLNw@|CiQNw@n@eDFYH]VkAd@_B\\}@Ti@bAoB|BcE`@q@`@q@r@aAb@e@l@k@bB_Bn@k@pA_Bl@y@l@sA`@}ADWFc@Hu@DeABy@E_ACUKcASeAu@uDSiBAsBJmBz@kFv@eDb@}Aj@eBt@sBnAuC~@eBrBeDdAyA`ByB~AeBXWZW|F_GrAuAbEcEjB{B|AqBjA}AvAeB~CeET[z@qAZq@rBiDh@o@f@k@|B{BbAmAv@kAf@cAtAsCZi@v@kAhCmDhB{Bn@}@zBaC~CaCdF}C~@m@pA{@bBqAn@o@z@{@vBqCbB}BzB}Cj@u@dF_HVc@t@mA^u@Z}@b@_Bt@eDhAoEJa@|AiGdAeDj@}BbBgFt@sC~AgGdA{Db@kBpAuGh@}Cb@}BDQ~@gEb@iBPo@d@oBf@mBLc@b@eBbC}Iz@{C|@wCr@aCp@mCt@{Cj@eCt@yCTcA`@_CBWDk@?oAC}@g@wGCmBH_CRkCT{CLkBD_@HyA?MJyEr@iFPqAPiABQXkBpBcNfAyHDYFk@PoABKPkDNuCN}BPwAZ_B`@eBz@sBd@eAxA_C~@eAhCyBz@u@j@i@hAw@`BoAnAkAz@gA`AgBxA}CtAaD|AwD`@cAZo@r@sBLi@PwABgCGkIEeCQuKCmBEaE@_@?SB}@L{AF[RaAZsAdAqEnAsFnAqFLg@|AuF`@_BjBcHNe@d@iBdCqIVgAn@}DPeBDa@b@eEh@_GDc@Fc@D[^aDj@qF\\cDr@aHHw@^kCZuAv@mC\\_AtAkEV{@jAmD|@sChBaGFURi@|AkE|AsEdAqDV{BNqCVqCLgA^uBPm@dAaC|@oBz@}ANYRc@d@{@b@cAzCqF`D}GpAoCp@gAf@q@z@_ApEcFr@iAhAuBlD_H~A}CdBgDzA{Cf@wA~AoFbAyDj@mBdD_MNk@t@iCh@gB|@_DT{@tAcFPi@|@iDt@mCXcAh@yCt@uCNi@Vu@bAqBhB_Dp@gBXgAbAwDlAoHp@qELw@^yBb@mCV}AlAuHv@sFd@kCnAyHTuA\\wAJa@b@oAN]bAyBhEgJx@iBzAyCjC{Fb@yANUVm@|BqFHOl@uAzBcFn@aBf@uBZwBp@sGLwA`AuJ@ILaAJ]Li@^gAx@aBDGbBmB`@a@b@WfAm@xCmAjFiBpDgBtB{AvAkA`DqC\\[fB_BXWlEcEhAkAvA_BfEeF|@{@~AoAxN_JdAo@fAo@hBy@\\MpBo@~NoD~Ag@RIl@_@|@u@rAsA`CqC~@mAv@cAd@k@T[d@_@hCaDfAwANSrCeDx@eAhH{IvD{E|EuFj@y@^q@Vk@\\{@Ri@Z{@Pc@zAwDJW~AyEdC{GhAoC`B}Dl@}AL]t@{B\\qARiATiBTsB^{EDy@RqC?c@?aBAe@MkBMsD@yA?CLoF?_@EaBWaCs@_EQyAGq@MmBAmC@y@@kC@i@LaBVmBd@oCJs@^_CTmAnBcLn@gC\\aAVm@FKf@y@tCoDpA}Ad@k@PUZi@j@iAFSbDgIRi@J]r@iB^cBN{@DUPiAr@oHToBLcAJs@TaAfBgFr@kBNa@jAmDJKL]v@uBPg@p@cB|@iCp@}AbAcBhBeB`@YbAk@zAs@hKkDhCeAtA}@rAoAf@k@R[pE_G^g@xBqCvAiBp@_Av@aA^c@l@}@`@w@L[l@sB\\_Bf@iDDW@k@D_@Fa@H]Rs@t@kBb@o@fAsAVYh@i@n@q@bEkEh@s@\\k@Vk@Xu@\\oAf@gDNy@`@_B\\yAj@iBr@kCL]Ps@dBsFr@aChB}FdBsFDKL[N_@Vc@fCqDfDiEX_@n@w@fCaDlCqDz@kAx@wARe@hDyGn@uA~A_DFOXm@^q@Va@r@gAFI~@cApAiA~DeDz@e@t@o@`GsELKv@u@HI|@mApCaFh@s@xA_Br@g@dDgCpCcB`DuBJIb@Yf@_@hAq@z@q@hA}@f@g@VYlAuA@?tBmCj@u@rA}A\\a@hAmAhAaAz@w@dC{BtAwAT[LM|H}IhBuBhEaFh@k@tAqADCfAw@nEwC~@g@dA}@p@k@z@{@|DyDtDuD`AmAn@_Al@iAZq@j@wARq@lByFxBiGH]xAeEXy@t@yAx@sA~BsCLMzAiBlB{BxBmC~@kAlAqAzBiBd@UnAw@ZOb@UnAo@vAi@JEnA]xBa@zBa@b@Gp[yFt@SjQ{C`B]tA[tA]rDcA`HyBpCeAr@Y~BaAtFgCr@_@pBcAhHeEpHaFxCoBzG_F`Ao@h@a@tDkCZUtKsHtGsEfEsCBCbJkGZU|HqFxAcAvEcDtOwKfAu@r@c@h@W`Ae@`Cy@x@UhCc@t@KvAMtCKt@?z@@fEPL@J@|@Ll@Ln@N|A^n@NTFvDbALDB?vBp@rEbBh@\\`FvBvB`A^P`IjDfAb@zBz@zCx@~@NTDjEb@r@FRBp@Hr@FzALjBPF@zEb@dCTn@Hb@DdAJrAPhAPpB^tCt@zEjBnBx@pC~@|Bh@`AL|Hv@|ANpCTfA@xZ|C~@HzD`@|CZdEXJ@dENfBD\\@bA?xA@jAAlLW~KWRA`AGxASx@OpEmAfBa@hBKrEi@pBSpAO^OxDc@zAUbCe@rCu@~CiA`EgB|CsA`Ac@RGbBk@f@Kl@KlAMnBGrCHN@v@FnKl@D?pCBfCGrBOvBWxCm@jCy@rCiAdDgBFEd@WzCaBvHeE|Au@nCiAbGwBrBo@rNmEZKjGmBfJoChK}CnEuAhD}@x@KtBMnB@nAHv@Jb@Fz@JZDbJrAd@FtB\\lRnCRBzB\\jL`Bv@JvCZdSrAjN~@zARfAVr@TLDrAj@|A|@n@h@dA~@zG|GLLr@r@tCxCJTfAjAnCdCf@`@r@b@p@XNFl@Rx@Pl@J|@FbABhACvAOHClD[|@Q`LkAfCOrD_@HAlDSjEUdDUzEk@nDe@bAK\\Al@AvAAv@DvANnBZ~B`@vB^lJpApCZdD^zARhALrLr@b@BjKn@fBNpIbBdCf@lE|@fDt@lCj@rG`B|Bd@|AThCRxCNzDR~CNjBJjAHfE^xALhPnAZDdE\\pBLn@@zB@`BC|CG`Mu@|EU`Dc@pASnEq@~GcA^E`CUxCIzIOjM]hEEzCCnDCdDDnDPfBLf@Db@DrFn@lGx@tAXhHvApFnAbAZbAf@fAl@hHlEr@b@jClAlNhE|E|AzBz@bDtA|IxDxErBp@VnBl@j@LhANlCLjB?pGGpDGtCEfAClDQ`Ko@z@E|Hi@`Im@xEi@lF_@J?`CKrB?t@BvAJ|ATZFZFdFfAxGzAdDr@zBh@rA^p@Xl@ZRJ`Ar@zBfBhA`ApCvBbG~Fh@f@fB`B`Az@fAbArD|DnEpF`@l@FFVR|@f@b@ZVNb@PtA`@VFvATbBLf@BbP|@t@DvCVbDXtALtDj@`C`@hBXjC\\vCTrLz@|Ij@hJp@tBR`DVrFd@lEPvBBV@n@?^ApDCnJEdACnAG|AKpMqAlCWpHw@dNyApAOl@KPC`ASnBk@zAk@v@]fAo@~@k@lFiD|@e@~@_@v@UZIxB]pAKrAA~ABjBPpATbA\\tB|@TJv@d@nAbAp@r@b@h@xDzFnAlBvBpCPTt@t@FFv@f@LHnAj@hAZbAND@`@B@?jADtAAbJY`@AfDGn@?jD@rGFhEFzDAd@@rAB~B@bBB`@?jBBb@?|KOv@?`EN|@J~ARrCl@D?rA^jA`@r@Xb@F~CdBjBrAbDnCnAdAfAr@d@XvAj@r@RZF`@Hr@HX@nADlACfG[tHc@rAAnBBxBPrAPtGpBvAf@dJjCrAZdF~@|Dj@hGt@vAXrA\\|@`@pB|@`EjB|@b@r@NtCfAz@VbAZnAd@h@Tt@^zAbAxAnApEzDHHr@j@|BbBlFrDj@`@VNxBnA|At@tBv@fBf@lAXPFvAVf@DxAPL@~CR`C@d@?hBAdENdBRfBd@f@RlB|@nDlBd@XjAl@xDlBhI|D^NfDrA~Bt@nI`CnBp@fAh@dAj@x@j@jAz@`ExCnEnDd@\\zDlCp@`@vAx@v@d@zIhEvDvA~Ah@`JhChEjALBfGhBh@Rn@Zj@Zj@Xb@T`At@d@d@t@x@bPjTd@n@jCrDvBnCx@dAzBxCpMhQbAnAn@x@l@t@rCdDjDdDXV|AtAf@d@~DnDzDnDd@`@vArAhHpGhFtEtLjKf@b@TTxBjBhC`Cz@t@VTRRLHVVh@d@vKzJxApAhA|@bAz@`BjAhCbBzBlALDhCnAp@Z^P`DnAfF`B`AVjAXbDv@zLpCfCn@vD|@jEbAzKfCtEdAtFpAzA\\pInBzA\\tBd@RFnAXlCn@tGxAdFlAfAVj@NzAZlH`B|FtAjAX^HZN`Cf@dBZrCp@rCp@VF~M|CxDz@jCv@zEdB\\LdEtBvAp@rBhAlBnA~GhFhG~E~DbDfBvAhDpCxGtFHF`HvFzJfI`@\\tDzCPNhCjBbH|FTZtAdAtEtCfB`ArFhCdBn@b@NdHzB`Cj@jDn@|HhA~MfBzInApEf@bIr@fJv@zBNrGn@jBPvFf@~VvBt@FjR~Ar@Fr@FzGl@hUpBtGl@xFd@b@D`AHlE`@N@pHl@dBNfThB`K|@f@FXF`@F|ARzIxArAVbCj@bDz@fCr@tA^l@LfDn@hARfCf@nARxKhBzAThFz@tHtAJB|KpBdFt@fALbEd@`ALrDd@rDf@pARj@HfBV~AV`@HTDl@RlBt@TLfAn@d@\\ZTz@r@xAnAlCrB~@p@pBlApCfBfBhAfBvAv@dA`AvALRxFfKpBtEb@fA`A~Bv@zAp@bAd@h@j@d@bBjAxAv@NH~@f@ZR~AfAh@h@l@h@zAlB|AvBRV`DlEJJb@f@jCxCVXvAvAlAnAd@b@|@t@hBlAbD|AzAt@\\RfAp@\\VnBtAXRhDjCvAfAr@d@vIlEb@TpAl@nAp@bBfATRZVf@d@fAlAbDzErIhMdBrBdB`BdDrC`BhBvDjFLP`AdAzApAlBrAvHnFn@f@dBxAJHNJHHnExC|IvGv@h@zA|@|@`@~Ad@\\FtBb@xA\\TFbA^|@b@PJtFzDdA|@dApAfAfBrA~BpDjGdFtIx@lAz@`ApB`BtA`AxGtEf@\\xAz@jCfAbJ~CtChAnCfARH~@h@`@XpCnBfAf@v@Rb@HxARbCXt@JRBnBPr@HnBVvB`@z@Zr@f@|@dARXfA|Ar@p@HD^Np@Jv@@r@Mt@[b@]\\m@Tq@XwATq@NWhAgAj@[r@Qz@Ap@B|@Jv@\\hF~DhCnB|@`@x@Dt@GbEeAZEb@Ap@FF?f@Pf@`@V`@`@bApA`Cx@`AvBhBd@VNHvAl@hDz@VBdAHnACxAIpDUvEQbB?F?rBNRBP@hEd@rADx@ATAbAMr@KdAQdAQzF_AxBk@l@S`Bo@z@a@`@YhBiAtB{ApBsAh@_@PMj@]d@_@z@m@hJ}Gx@i@lBiAz@k@jAs@n@g@TY\\c@l@oAZaAHWBITiAD[T}BPwBL}ATqCN}AFq@RuCXwCT_BRcAXkAnBuFhAgCp@oANUx@_ApAoAxAcAhB}@vAg@l@Od@KhBYtAMt@CjDIlDO~DOzAIpCIhBBnAJzAXb@Nv@XnAj@xBfA|Az@~B`AhEdB|Q|GhC~@xBdAZPZPtEhCjB`AhB~@fCnAtAh@tA\\TDdBT`@Bz@B|@?z@C~@GlB[ZCjAOpFa@B?tBUvBa@rA_@x@[^OvFoCjB}@|CyA`Bm@nBg@fB[vHu@hCe@bAUfAYTGRGfC}@lB{@lCyAjBiAd@[tB}AzL{K`C{Bl@m@dBqB~FcHt@gAd@u@v@sAj@qAt@mAf@q@j@s@^a@\\_@dB{A`@]TKnEsCzAaA`@U|CgBPOFEd@i@PO?Az@u@xAwAdB}AtCsC`@a@|@gA|A}BfGkJ`AwAbAuAl@o@`BsAxA{@TIfBo@r@QNCdBUt@Ev@EjCCn@AN?v@?lFMpCStKwAx@Ql@QnEaB\\Kz@UTE`@Gd@ExAGfA@Z@dBPxAVdBb@v@Vh@RhAj@fBlAbDjCnIdHpExDnFxEbCxB~MvLVTbE~CrLbI|AfAtCpBPLf@\\NH`BfAbCfBHFvA~@tHdFdB~@XLlAd@rBl@dCb@nIlAlEv@hC`@nARbC^bC\\hDd@|@LfGx@h@FjCVfBJp@BN?pHC`CAJ?v@AxACpHJH?|@@l@B|DLR?pCA|EOnCGrAEV?lHPnADr@D`@D~BPbAHfBL`DThAHvCTpDd@zCn@nDdAxBt@~@ZjBn@nBl@bAV`FlAfHfBlAZlCr@jEfAbDp@j@JpGtAbBf@vAh@nC`AbA^v@Xp@TXJVHr@Rb@LlGdBvCz@`AR~A\\~Cj@`BXnEx@zAXzAVhB\\|AX~B^dDn@j@J|BVdG\\xEXnMv@bADbCApBEdPc@pEKtAEfKWzDIlBI`@A\\A`@?PAj@C|@CF?lAEl@EpGk@zC[dAGnB?r@DlAPzA^fA^`Cz@rDpAv@Xp@VfA`@fC|@xAf@^@RDhJrCnBj@LHB?RBl@JZJ|EdAx@Px@RvBd@nB^~AXND\\b@jLlC|Dt@vGjAdKbCxBh@rCn@vCv@zBh@xBd@pBTdB@b@CNA~@GN?zB]pLqBf@StEWt@?lA@vFPbENdEJ~BHbBDxAFpBFjGT`J\\lJ\\tABdBC~@I^M~@W`B{@\\SZUPO\\]X[bEsFn@cAXg@r@u@XYjB_Cb@i@|@}@TGxBcCvA{AtEyFpBkCpAwA`JwJl@m@V[p@s@hBkBrBoBbB_BnGgG~@{@l@o@rFqHlCqDrKcOz@mAvC}DtEmGl@y@bCaD\\c@pA_BJM@AzEaG~DaF~AqBt@gAl@gA`@aAJUfA{CxAeDjAyBhF_Kp@sABIz@mCTo@rAwClEmLdBwDvAqCh@cAFKrAyCVe@Vg@~D{HNWTc@fAuBd@}@`@y@Xk@Xk@\\m@hGiLtDoGXg@n@kAdC}E~DgIl@qApCkGXq@Xm@\\w@h@cAN]`@m@`AmAf@c@zByAbBy@\\QzC{AjCoAhGcD~C{A|CgB~@u@DCXWdAsAbAoBRk@BGf@sAd@mAHy@Rg@zByFd@kAFOTg@n@yAh@sA@CDUt@mB`@_A^{@LUj@y@bBwCtB_Eh@y@z@uADIlBiCr@Wb@]dCaBvA}@XUb@a@t@}@~BwDPYP[|AmCr@mA~AaCt@cAr@aAd@q@t@aAtC_EJKx@gAl@u@n@y@hBiBx@o@^WLIb@YFEz@c@|@c@zBw@z@Sd@M|B[^EbAGlAEhC@xENv@B|@DzDLtDL`BJvB^NBPDvBl@n@RrD`B`@V~@f@rBdAfAf@fBz@jFnCj@X`Bv@rIdEb@VbCnAZJb@^hBv@lBp@~A`@xBXz@D`A@n@Bx@BpDNP@N@R?fGXjMf@l@B|ETxLj@`CJrEPnAFjADbAD\\BnI\\tEPZ@R@~@BrADbBHtBNv@FtAH|AFNMh@@lDBfBEp@CVC`@CLC\\MZUR]Ha@@YGc@Sc@SQg@QWCM?o@Ja@TSTUf@Kn@I~AGhACh@El@?JAnAHr@Pj@T^`@`@\\RPFp@NL@v@Bh@?pB?lC@fMB\\?rI?nE?xEDnBFVBXBd@FzB\\bB^~Br@pAf@zC|Ax@h@r@h@rAhAt@r@\\`@rAdB|@vA|@`Bt@dBvGfQn@pAh@|@hAzATT~@|@z@l@bDdB~Al@tCv@d@LjQzE`AXnBh@rBf@`Bb@x@XvL`DhDbARHpAj@f@TTNTLb@Xr@f@dAz@dAdA`BxBdBhCx@|AdA|BTh@n@rAr@zAXd@JRh@v@\\b@h@n@tAlAvCrBx@j@fA`A`AhARVrBxCvAzBz@jAhFzHlBpC~@rAjAtA^b@hAjANLXV|B|BdAdAvCpChAfAl@n@vAfBbAzAlCbFLZNXd@hA`@jAV~@h@`Bn@tB^nA~@rDpArFDNzAxExArD`B|D`AvBTb@^t@l@~@^j@LR`BrB|@pAPVzBdE`A`CPb@Vp@HT~@`CVn@Rd@lB~EjAvCd@jAb@jAXv@ZdAp@vBlDlLdAhDh@dBTn@jBpFZbAzA|EfB~FL\\Tp@xCdJ~@pBf@~@HL\\l@pAjBdAlBlAhCf@nAV|@Pd@fC~HlA|CtB`FlBtE`CvF|B|FrC|HZh@\\l@RLZr@\\`AbBxETj@h@xAb@lAj@`Bf@vAbAlBz@vARj@Hd@\\l@hBrCJNh@`AnErHb@p@p@hAfD|FlAfBl@t@rFtGV\\hBxBbFlGhDbFtA~BtBpDNTrDnGxEvHnCpE`B|CVf@dB|DnAbD\\~@pDjJVj@f@vAl@bB`@tA~CtL\\hAJ`@XbAJb@J^XdAf@dBJb@l@`BHRBHL`@L^p@tBnAvDb@zAl@xBTt@b@`BZdAfAfDn@hBh@tAlBpEXn@|AtC^x@v@|AtAnClCbGd@`AVd@j@hALXLXbAnB^p@h@z@x@pA|FvJZl@~@~BFNDNhA`E|BbJ^|Av@|CRz@VnBFf@HbAH|@PdBb@dDJj@d@bBTr@|A|EzAhFb@jB^`BLh@\\|AjAxENn@r@xBz@~BvAdDj@pAz@xBTj@Nf@r@nBZ`ArBpGb@hA~@dCt@jBnCfHl@bBpCzHtA~Db@jAVp@`CxFDJDJv@fB~A|DbA`C`AzBfAfCx@|Bx@vCd@jC`@~DJdCBrB?l@?xA?h@?x@Ar@EvB@v@?J?HBz@LrATnAZz@v@|An@z@t@r@VRn@b@rAp@B@fCfATJ`DxAnBhAjAz@nAjA`@b@b@j@t@bADDjAxB`BnDjBvDj@jAf@~@lBbDj@dAjHpLb@x@~HtNh@dAz@dBVd@NVRXl@fAn@lAXh@dDfGR`@PXp@r@JV|@zCZlARn@~CjLh@fB~BbJv@vC|@fCz@rBtAlCXxANd@nEnJpAdCdAzBp@tAlBjDZf@`AvAFJJPv@dAfApAVZnA`BhA~A|@tAhCdEnArBTZNTzAtBxBzCxBnC~AtBrEvFpB`CjAlAv@t@nDxCDDZXpCrC`@`@fAhALLpIbJRT`@b@d@f@pArAh@h@t@l@|AnA|EvDVRpAdANJhErDhAjA\\b@|@nApAxBhCtEVd@n@nAdCvE`@t@nAzB~BlEVd@fAxBv@rBp@lBlAbDbA|CRr@ZlA`@|BT~BFtABt@?jAOlDEx@WvCSzCo@rKm@bIAPK`BKxAMdCQnDI|AQrBY`Eo@jJk@rIu@vKGn@GxAcAvPCl@AxBDxCf@~Hl@lI@F\\zD`@zELvAJxADf@HpAPdCHlAf@bGZjElAhQ\\xFDn@PbCNvBDv@L`B^bFXpCd@tCr@vCv@~BbA~B^t@fBhD@?\\f@HNdBfDfAjB^p@dBtCX^|BzChBvBr@v@xF`GpCjD~NrSt@bAlIhLf@r@tDbFVZnCdD|AdBv@x@xHxHRPxE|ErB|B~BzCfBdC\\j@fBxCbAtB|@hBpAzClC|FbB|Dn@jA`@f@p@~@h@n@fBfB`BpAdCxAf@Vz@ZpBp@vNnDtHfBVBNBvBd@pBj@~Bt@lBz@xChBZRXTvCxB|@p@nBzA`Az@jAtAdAzAVf@^t@Xp@`@bAVz@J`@DP`@`CPzBBfADpBJlBTlHBp@JjBFlBFnGBxFDhEBhEBvA?zB?~F?zL?XJtBNbBnAnITlC`@lK?~B?b@I|ACt@I~AGjB?nB@b@DlAPhC\\jFRnCJ|ABd@@x@F`GBhED|FDfGBbA@XBb@RjBZ`Bf@dBN`@N^h@jAd@x@|@nAbDxDzAvBfAjBv@|AZl@Zp@l@jB`@jBNvAFlA@`@AfAGnAARYnBe@jBm@zA]t@_ApBc@zASdAGZGv@Cd@Cj@@hAb@lHPzCBpA?bA?h@EpEA|FDbBFlATjB@LDTVrA^vAt@lBjAbCbApBx@`BN^v@nBX|@Lj@h@bDF`@x@tFF^Z|ALj@z@fDZhAv@~B\\bA\\pAXvATzB@RDpA@\\CtAAXCx@WxDEp@M`BU|CCPGr@APCd@EdACrBCzA@pA@xABxAHjELbCFn@JtABf@NzCXlFFpAJpEL~FTlIBfAJtA^zCJ`BT`CHdAHlAD|A@d@?fF@h@JxB@ZLnBHz@Fz@JxAVnDL~BDp@t@zKDxBGzG\\pAArAF|DPrF\\`DFf@H`@BJhArExAvDN^^f@fApBp@`AfAtArDpDxDrCt@j@LHVt@vC|Bf@`@RPpBpBj@v@b@l@PZz@bBb@`AVl@p@`Cz@jD\\`BR|@~@`E`@dBpBxIh@zBt@zC^|Ab@lBl@lCj@~BHZv@dD~A|GZpAHZb@bBBNdBlHdCpKNn@`@~B^rBPbBF`BItBIfAStDkAnSg@`KARGvASnDGj@Cn@GdC?p@Bx@NxABPVnAd@vALVx@pAj@p@z@x@NLn@b@NHRLr@\\bCn@x@Lp@H~@DhBC~@Gz@KtF{@fEq@lGaAjD[l@ElBKt@Cf@CnHa@zBKtB?\\?vADZBbADPBvBXfB\\n@NtA`@pAd@dBx@`B`AnAz@TRfB`B`BjBrAtBx@zA~@|BBHt@`Cn@tCb@xBnAjFvA|G`@fBJb@Vx@rArDfA|BdBjDp@zA|B~EnAjClB|DbG`M~@vADFJJPTFJPVNR`BjBxAtArAhAxAnAVR~DjD\\XjC|Bx@t@`BpAhCvB|AdAx@d@t@^nAf@b@L|Bt@|Bj@lARv@J@@|BPlCDhA?|@ArBKzBWjBYl@Or@QbBg@fBm@j@UlYeK`@M`Ba@rB_@tBUtBIhCAtBHVBx@Hp@Fz@Jr@N`@HtA\\n@R|@\\@?xAp@rDpBr@`@lB~@\\PpCzAvEbCfE`CNJXNnBdAhDlBvB|@hBh@zFrA~A`@~A`@z@PhKbChFjA|Bb@xARtBXvD\\nEVpFPnBPpB^nFlAdJrBxCh@TBzAXjBPzARb@Fp@JlEl@jALxAVzAXdB^vG|AvA^hEdA`AX`D|@fA`@v@^r@\\\\RhDvBr@d@jC`Cb@`@Z\\d@h@pAhBf@|@lAjCvAdEjAdEnD|LvAjFl@~Ah@dA\\n@`ApA~@dArAbAtA|@FDZNv@Zb@NdBf@VFrAT`BNfBHbIDp@@lLJ|GDnDDfCJXBbALTBdB\\fAZ`@LfBp@fB~@j@\\h@^h@`@|D`DdB`AzE~BnAp@bAh@lBrAtAlAlGxG\\^TZRVZb@|@xAJRd@|@bBpD`@v@fBxD|B|EJRt@tAtHbNj@dArEhI|@pAd@l@NP~@bArB|BpBzBpAvAbAjATVNN`BjBz@z@~E`FHHxAjBnAnBpEjJZl@dA`BrAbB|A~ARPrA`AhBhAzF~C`@Vx@d@DBjCxAxGrDfE~Bt@b@l@`@xBfBdBbBtA~ApDlFNRZf@Zb@vG|JlFbIjGpJz@|An@pABD^XPH?@z@zBz@hCx@nDVxAJv@Fd@JhALtB?V?@JP|AZb@Pp@V`AVnBXtBX@@hCj@fAT^B~@BjAFb@Bd@DH@jCVnCJdADt@Bh@@d@@l@?F?d@AVCVEDAHClA]dAYBAb@KfAUDAl@Ax@Fx@DVBl@Bx@FR@lBLx@Bv@Br@QXA@GFGNC@?NF@BBLAJv@nBr@dAr@r@tA`At@\\PHZL`ANB?zEb@`ADzAHZ@r@DFCHAHBDBDLAJCB\\l@Rb@^v@d@z@`AfBT`@lCxEBHFT@?F@DBBNABLN|@zA\\z@d@pBLn@FTjArEPn@|@xDb@`BZpA|A~FLb@h@hBPb@l@xAXl@Pb@fAbCf@dAr@fBJXb@jA|@`Cx@hCL^fBjFDHn@rBj@pBPx@Fb@Lv@z@hGPn@Lb@^pA`@nAlAfEbA~C~@vCjA|Cp@tAl@|@x@~@`A|@XX|@bA~O~Op@j@`AbA|@fANT@?H@BBHL?HCFA@rA`E|@rDBRV`AJf@FRJ^FN\\`A^v@RXpAnANNr@r@zBzCd@r@pAnCDHDDr@xAFxAfFnKdBvDf@xA\\dAFVb@vAPb@HTt@hB`DpFzAhCbAvBdAvCHV`@|ALh@d@fCl@xE`AnHBVTnBv@pFrAnHhAlGBFhD`S`BnJn@tFJt@PjAFTr@~BvA`Ed@`BvAbFbAjCb@hAP^Zl@zDzHpF`LJRtAdCxBdD|BvCVZjCzCb@`@z@z@xA|A~@jAdA`Bj@bAR`@z@tBp@zBd@pCZlCFlB@zBAf@MfCAXUlBQbAS~@Md@g@~AiAlCMVi@~@q@dAy@fAyArBw@zAEHy@vBc@bBKb@W~BIz@GtBD~BPbCNfAJl@FTf@lBr@jBz@~AfA~A`D~DbA~A~@hBt@rBr@dCf@rCJl@PdAp@xDj@~Bb@xAjD|Jz@~B~@dCv@xBpAxDPh@bB`GzAfGVhAJb@R`Ap@bDRnAd@zCtAzIh@bCl@hBPd@v@lBt@tAt@hAb@j@lBvBzEhEb@^hAbAf@j@VVx@dANVx@tAR\\P`@r@bB^jAt@jDjAxGJf@ZtAZdAL^N^p@|ATf@dAhBz@jAVZFHnArAtApA|BlBrAtA`@j@TZVd@R\\~CdGzEfJNZr@hB^nATz@\\bBZdCd@tGNnAh@vCf@fBFPt@jB`AhBzAzBDD`C~C|BnCzBjCh@j@pDrD~FtGnGxHnBhC|C~DhAzAlApBv@~APb@v@zBp@xCZzBJv@X|BX`BNv@x@xCt@xBTj@r@`BlAzB^r@rArBpBnCrGdJPTfEbGvArBn@dAnApChBlEfBjDN\\rCbGj@rAl@`Bx@`D\\hBRjBLlC@~AEdCOfFOdEQfFm@|T?VY~JGvAYzHI~ASxB]fBOn@c@rA{CpIM\\u@tBo@hBwH|SgBhFgB|Ek@nBi@`Ca@|AqAlGsAxGI\\I\\}A~HMj@[nBMfAc@tHAVMnBk@vKGnB[xQKdJCjAE|BGpFE|BElBMnFEv@In@ABWnA_@fAQ`@]n@gA~A[b@qFbH{ArBcC~DcFxIkCvEoA`CKPgBdDqAtBiBnDiAjCuA|DGTuAbFu@jDUnAIh@w@~FM|AI`AKlBEv@Cl@GrFBfDFfCLvBBb@\\nEZjDRjBZpB\\~Al@dBHTZr@b@v@tAlBtAtAzB~ANFJFlBx@r@TdAXtMtC|FpAx@TpAXb@H^JfAXfA\\`Ad@hAl@bAr@zAvA`@f@TZPVHJn@jAh@pA^rA\\`BPjAL`Al@zFr@vIr@zHh@tED`@v@|E~@jF`BrINv@jAfGlArGl@bDvArHz@zEn@jDbCvMN~@Fj@Hn@`BfUb@hHDl@Bn@DrBExCKvGUlLMpGMtH?|@BpAVzDHn@VdBn@|C`A|CjCjIt@|BTn@j@hBj@fBTp@pCvIz@xBdAnB`@n@jA|AxA|AjAbApChBlAv@dC|A\\XlAbAnAzAb@l@b@x@dChFt@rA^h@tAtAhCfBl@Xz@TvA^lGjATDrGjAvHrAdARvQ~ClARpCh@nBf@ZHRF`Ah@lAz@fAbAt@`A^n@t@fB^zA\\pBJv@|BzPD\\XjBb@xCn@bFjCpQRvANhAf@rDNxB@R@~@Af@Ad@G`AIv@Mt@Mj@I`@ER}@jDcBtG_@vAeB|GcDlMe@|COrBChAA`EBdGC`DMnCC^q@dIGf@a@zEMdDCfF@dDD`FCjG?x@?lE@lSFrBH`AHb@DVNp@b@zAb@`AFN`A~ApCxDjA~AjAzAZ`@hB|BbBhBtCpCrE`E`C`Cl@r@pDzEpA|A`@`@r@r@TTdC|Bf@d@ZXv@t@~AxA`BzA~CtCzAxA`BpBlE`H`AlA~HxHbAdAdBpBX^tApBrBvDvApChA~B`ArBz@dCd@tAVr@^dAZt@f@jA|BjFz@hBd@bApEpJn@tAfCbF|@tAdAjA`@\\b@Zd@XdGfDbFnCdAj@|D|BnFlD~E`CnCrAzAl@\\Pp@ZlBdAbDjBj@ZxAp@`Bh@v@Px@Jx@LvJz@hCZjCn@RFzBv@b@R`Bz@ZR~AhAx@n@pGdFzBtA^R`Ah@tC|AzFzChCpA`@Hn@RlB\\dBPfCV~B`@t@RfC`AjBdAh@\\bAz@lAnAdB~A`@b@NNTV`@f@Z\\nAlBb@r@~BzDn@dAtBfD`@n@^j@fAzAbErFdDpEV\\hBpC|BpDjHdLLPhApAv@v@~@x@tBbBlFlE^Z|AfAlAr@NHrBbApC`A~@\\n@Pr@Z|Ar@lAn@p@`@jA|@xAnApAxAtAlBnClEb@r@j@~@bEzGdAbBhBtCj@bAXh@z@nBr@pBr@hCd@lBH`@Nf@Nf@j@bBP`@j@tArAjChDdGP^`A|BL\\b@~AZnAnBdILb@jBzHdCbKXbAj@bBTl@|@rBj@nAtAhCz@bBb@`AJPh@|ADJFXhAjFH\\d@vAh@nAn@pAjB|DvA~CRf@xBhFzA`FR`ABVXpBzAlIVzALp@?@d@lCr@fEZ~Al@fDdArF`@hB~@fEb@`BjAnE\\zA~@~DJf@b@lBhAfFjAnE~AlFv@dCVt@x@tBj@pAN\\b@|@lBdDf@z@|DlGv@rA|DjG\\f@NTFJzAfCrAbCvAjCHL|ArCT`@tBxDJRbClEfDfGnBnDJPrBtDzBfEvLlTnE~H\\n@Vf@~AtC`ErHzDhHVd@NV`DzFfFjJz@|AfJ`Pj@`Av@|AhGzMzB|EZt@pArCd@`AlBbENZPBb@j@ZTp@^p@^r@n@RZPZ`@bAFPAT@LPxB?F@DBT?FDd@@R@D@V@H?FDb@DZTxA^zATl@FH^`A^bAR^\\jAvAxEv@pCJ\\d@zAZfAZdAnAfE^jArDbM@Bv@zCz@lEf@~D~@nJNzAHrADx@JlCF|AHtB@nCB~G?hA@j@AlD?|@?\\HhAR~@FRHPj@bAd@l@Xd@Xd@l@z@n@dA|@pAh@`Av@|Ad@|@PXp@bBHVLd@BFx@bBR`@Tf@hAnBfB|ClAxBNVV`@LRfAvA|AfBlAnAp@r@pBjBj@l@Z\\p@~@^l@NXTd@d@`APb@t@zAdA~BRd@\\p@bF`J`@v@f@`Ar@tANZP^P`@`@`ANb@N`@h@dBp@~Bj@lB~@jDl@bB`AzCZ|@L\\BFf@tABHHVb@bATh@n@tATl@n@pAXt@r@`Bh@nA\\p@~@bBx@~AR^Xb@t@rAdAnBJPFJp@fADFNVdBrC\\h@^n@z@pAZb@DDx@jA|BhDLPfAjAT\\tAnBT^`@r@bAxAn@z@r@|@@BzAbBhCdChAvAt@`AZb@fCbD`CpDrC~EpAdBpBrCnA~AVZdBtBf@v@xAlCd@x@p@jAd@t@~BrD|@|Ah@hA|@hCTr@Tn@d@jATp@DLLXP\\v@lBL`@Pl@FPB^B\\Ft@RpBRj@D\\`@rDXzBLdABNDv@P|BBd@HxALvB@x@Bh@FbEP|EFzA@jBJnBJb@BLNjBDf@BX`@bD\\xDHz@Bb@DbAL`BLjBNlAFl@DTb@jC^bCHf@Dn@Dj@Df@Db@?LDb@\\pBFXH\\H\\b@nAXv@\\`ATh@|@`CRr@Nn@PrB@NBx@JxBBdBBh@Bn@XvCFh@Ln@Jv@|@rDV`Ap@lCRl@JTBFV`@L\\x@vA`AvALL\\ZRN~ChBrBjAnAp@nAt@ZNzBjAxAz@\\TZVIRs@e@SMIE_E{BaAi@eAo@cBcAo@WSOkB}@{@g@m@g@{@~@MD[f@Qj@ETIb@G\\On@CLMt@?h@PfABb@?Za@~ASn@Ov@ANIn@Cn@GdCC~@@FDVRPB@NF\\LJD@NQx@E^YtCSzBANAjA?|GFNBzA@z@Ch@?HCL@fI?l@?Z@tC@t@BtB@bC@b@?FBlBHhM@dA?j@D`HJhAFd@Hx@RfBNzAx@fHHn@b@jDPvALfADLV~BFh@DZBFJz@P`BTrBJfAL~@`@hEJjAHf@Hp@L|AD^BJNhAFb@Fr@ITBHz@LbCGdCAl@DHJJx@BVHdBBhADx@@nA@PAhAAz@?\\A|C?TC|@?t@"}, "description": "Varthur - Sarjapur Rd", "warnings": ["This route has tolls."], "viewport": {"low": {"latitude": 11.016128199999999, "longitude": 76.9558316}, "high": {"latitude": 12.932161299999999, "longitude": 78.2268357}}, "travelAdvisory": {}, "localizedValues": {"distance": {"text": "353 km"}, "duration": {"text": "6 hours 31 mins"}, "staticDuration": {"text": "6 hours 31 mins"}}, "routeLabels": ["DEFAULT_ROUTE"], "polylineDetails": {}}], "geocodingResults": {}}