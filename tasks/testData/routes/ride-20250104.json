{"routes": [{"legs": [{"distanceMeters": 21272, "duration": "2291s", "staticDuration": "2291s", "polyline": {"encodedPolyline": "sx|mAe`oyMKPb@Zn@d@l@`@LHp@h@d@Z|@j@h@`@h@\\fAx@B@LHf@\\PLn@b@VPRN`@Xl@`@PLDBp@d@XRh@^RNf@\\^V\\V~@l@PNJHPJjAn@FB\\NXLd@Tz@`@n@XRJFBLFn@\\ZNx@\\TJPDt@RNBn@DX@V@V?bDDT?rCBL?p@@pA?j@?h@Ar@AfAAtAEd@G|AQz@SDAnB}@zBeAdCmAbFaCnAm@zE}B|Aq@|BiAHE^[PQf@o@fAuA\\a@dB{Bj@u@`@i@t@_At@aAfDoEl@y@n@{@j@y@zB}C|@iAf@c@n@SxDu@lB]nAUr@OPCnCi@|AYl@MjB_@|A[fAQjDg@`BSd@ED?x@G`Ge@`@ETA`@CNANC|Ec@z@IbAKbCUn@IfD_@fAOzEq@rEo@d@KHAbDe@|@IrFq@LAlBSTAv@I\\GHAREpBa@j@MHCVIx@WvAe@r@UhAc@`@MDANEHEv@[\\Mr@U|Ai@TIz@UHCtA_@x@SbBc@H]TeATaAT{@DQJa@F[Jg@BIT_ADOPu@FUR_Ap@cDRcATiAd@gBLa@Rk@^y@JULYVk@nAqCdA_CVi@xAiDVk@v@gBb@aALYjAeCtBcFh@oAPa@N]Te@\\o@DE\\WZMf@KRGFALClBW`BYn@KFApAOdAOb@ILAxBa@zCe@bAO\\Ev@M|AW^EdAGf@Cb@ArAA^AV?dAA`@?t@?dA?X?^?F?~@I@?j@MPGPGPKbAu@t@m@~@w@DElAeAzBiBhA}@XY\\WTSvBeBXWNM^[`Ay@VU|AmAb@_@^YNMzCmC\\WLQVs@Hg@@KVyBVaCBSXiCTqBDa@Fg@@KJqAH_ALgCBc@?ILcCJuAF_A@WFwA?k@?q@Ae@IyA?c@AmA?_@A_@CoAEaB?QAYCcAAa@GkA?YCIBCDAB?f@@bI~@l@HVD`@Fz@Hl@Hp@Jv@JVBF?vEf@j@FnDVtBFt@?nBB`@?`EDj@?`BBhDD~@@`BB^?nGHvCAJA`@E|Eo@`Ca@fG}@|Ba@bLqB`Cc@|A[xAY\\GfB_@|KoBtBYh@INA^E~AKJAjE?v@@fBKnFa@hGe@h@Gx@G~D]j@EtBUvBKzBKlEUfAIj@GrCErA?`BNTB~@JlAPvARdAN~ATj@HvBTj@HfBF`@@zFPzAD|GRB?|@B~DJ`EN~AFJ@vFTjBJfDVnAJxGn@fANtHfAvIlA|Df@dAJ|BR\\BnCTZBb@BhL|@XBnBZRHfAPPDd@NRHl@\\\\V~@hA@@t@jAlAhAj@^zAf@jB~@v@VfBJl@Jz@NvAJfAVzBt@dA`@B?VHRHTP`@Zn@n@JJ`C`C~EnEJHbBzALJhAv@FBdG~BhBr@jE`BNF~ElBfBp@zCjAfBp@ND~An@tChAnFpBp@T`Ab@VJj@XTHt@\\p@ZfAd@vB`A|@b@|@^XDl@IPIJOd@yDD_@j@iFFm@L_AJw@TcBR{@FUJWNGJ]p@mB@AXcAj@eBDQx@qBd@cANWFI"}, "startLocation": {"latLng": {"latitude": 12.9321025, "longitude": 77.7422734}}, "endLocation": {"latLng": {"latitude": 12.7766925, "longitude": 77.77766280000002}}, "steps": [{"distanceMeters": 12, "staticDuration": "4s", "polyline": {"encodedPolyline": "sx|mAe`oyMKP"}, "startLocation": {"latLng": {"latitude": 12.9321025, "longitude": 77.7422734}}, "endLocation": {"latLng": {"latitude": 12.932161299999999, "longitude": 77.7421804}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head northwest toward Varthur - Sarjapur Rd"}, "localizedValues": {"distance": {"text": "12 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 6113, "staticDuration": "691s", "polyline": {"encodedPolyline": "_y|mAs_oyMb@Zn@d@l@`@LHp@h@d@Z|@j@h@`@h@\\fAx@B@LHf@\\PLn@b@VPRN`@Xl@`@PLDBp@d@XRh@^RNf@\\^V\\V~@l@PNJHPJjAn@FB\\NXLd@Tz@`@n@XRJFBLFn@\\ZNx@\\TJPDt@RNBn@DX@V@V?bDDT?rCBL?p@@pA?j@?h@Ar@AfAAtAEd@G|AQz@SDAnB}@zBeAdCmAbFaCnAm@zE}B|Aq@|BiAHE^[PQf@o@fAuA\\a@dB{Bj@u@`@i@t@_At@aAfDoEl@y@n@{@j@y@zB}C|@iAf@c@n@SxDu@lB]nAUr@OPCnCi@|AYl@MjB_@|A[fAQjDg@`BSd@ED?x@G`Ge@`@ETA`@CNANC|Ec@z@IbAKbCUn@IfD_@fAOzEq@rEo@d@KHAbDe@|@IrFq@LAlBSTAv@I\\GHAREpBa@j@MHCVIx@WvAe@r@UhAc@`@MDANEHEv@[\\Mr@U|Ai@TIz@UHCtA_@x@SbBc@"}, "startLocation": {"latLng": {"latitude": 12.932161299999999, "longitude": 77.7421804}}, "endLocation": {"latLng": {"latitude": 12.8833302, "longitude": 77.7524057}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left onto Varthur - Sarjapur Rd\nPass by Chrysalis High School, Varthur (on the left)"}, "localizedValues": {"distance": {"text": "6.1 km"}, "staticDuration": {"text": "12 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 4739, "staticDuration": "530s", "polyline": {"encodedPolyline": "ygsmAq_qyMH]TeATaAT{@DQ<PERSON><PERSON>@F[Jg@BIT_ADOPu@FUR_Ap@cDRcATiAd@gBLa@Rk@^y@JULYVk@nAqCdA_CVi@xAiDVk@v@gBb@aALYjAeCtBcFh@oAPa@N]Te@\\o@DE\\WZMf@KRGFALClBW`BYn@KFApAOdAOb@ILAxBa@zCe@bAO\\Ev@M|AW^EdAGf@Cb@ArAA^AV?dAA`@?t@?dA?X?^?F?~@I@?j@MPGPGPKbAu@t@m@~@w@DElAeAzBiBhA}@XY\\WTSvBeBXWNM^[`Ay@VU|AmAb@_@^YNMzCmC\\WLQVs@Hg@@KVyBVaCBSXiCTqBDa@Fg@@KJqAH_ALgCBc@?ILcCJuAF_A@WFwA?k@?q@Ae@IyA?c@AmA?_@A_@CoAEaB?QAYCcAAa@GkA?Y"}, "startLocation": {"latLng": {"latitude": 12.8833302, "longitude": 77.7524057}}, "endLocation": {"latLng": {"latitude": 12.8609785, "longitude": 77.783585}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left after <PERSON>ke wala (on the left)\nPass by the gas station (on the left in 3.1 km)"}, "localizedValues": {"distance": {"text": "4.7 km"}, "staticDuration": {"text": "9 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 6765, "staticDuration": "587s", "polyline": {"encodedPolyline": "c|nmAkbwyMCIBCDAB?f@@bI~@l@HVD`@Fz@Hl@Hp@Jv@JVBF?vEf@j@FnDVtBFt@?nBB`@?`EDj@?`BBhDD~@@`BB^?nGHvCAJA`@E|Eo@`Ca@fG}@|Ba@bLqB`Cc@|A[xAY\\GfB_@|KoBtBYh@INA^E~AKJAjE?v@@fBKnFa@hGe@h@Gx@G~D]j@EtBUvBKzBKlEUfAIj@GrCErA?`BNTB~@JlAPvARdAN~ATj@HvBTj@HfBF`@@zFPzAD|GRB?|@B~DJ`EN~AFJ@vFTjBJfDVnAJxGn@fANtHfAvIlA|Df@dAJ|BR\\BnCTZBb@BhL|@XBnBZRH"}, "startLocation": {"latLng": {"latitude": 12.8609785, "longitude": 77.783585}}, "endLocation": {"latLng": {"latitude": 12.8010752, "longitude": 77.782827}}, "navigationInstruction": {"maneuver": "ROUNDABOUT_LEFT", "instructions": "At the roundabout, take the 2nd exit onto NH 948A\nPass by IIFL Gold Loan (on the right)"}, "localizedValues": {"distance": {"text": "6.8 km"}, "staticDuration": {"text": "10 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 2780, "staticDuration": "308s", "polyline": {"encodedPolyline": "wecmAu}vyMfAPPDd@NRHl@\\\\V~@hA@@t@jAlAhAj@^zAf@jB~@v@VfBJl@Jz@NvAJfAVzBt@dA`@B?VHRHTP`@Zn@n@JJ`C`C~EnEJHbBzALJhAv@FBdG~BhBr@jE`BNF~ElBfBp@zCjAfBp@ND~An@tChAnFpBp@T`Ab@VJj@XTHt@\\p@ZfAd@vB`A|@b@|@^"}, "startLocation": {"latLng": {"latitude": 12.8010752, "longitude": 77.782827}}, "endLocation": {"latLng": {"latitude": 12.779499999999999, "longitude": 77.77061979999999}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "Continue onto NH 948A"}, "localizedValues": {"distance": {"text": "2.8 km"}, "staticDuration": {"text": "5 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 533, "staticDuration": "95s", "polyline": {"encodedPolyline": "{~~lAkqtyMXDl@IPIJOd@yDD_@j@iFFm@L_AJw@TcBR{@FUJWNG"}, "startLocation": {"latLng": {"latitude": 12.779499999999999, "longitude": 77.77061979999999}}, "endLocation": {"latLng": {"latitude": 12.777988299999999, "longitude": 77.7749302}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left"}, "localizedValues": {"distance": {"text": "0.5 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 330, "staticDuration": "76s", "polyline": {"encodedPolyline": "mu~lAiluyMJ]p@mB@AXcAj@eBDQx@qBd@cANWFI"}, "startLocation": {"latLng": {"latitude": 12.777988299999999, "longitude": 77.7749302}}, "endLocation": {"latLng": {"latitude": 12.7766925, "longitude": 77.77766280000002}}, "navigationInstruction": {"maneuver": "MERGE", "instructions": "Merge onto NH 4/NH 44/NH 48\nToll road\nDestination will be on the left"}, "localizedValues": {"distance": {"text": "0.3 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "21.3 km"}, "duration": {"text": "38 mins"}, "staticDuration": {"text": "38 mins"}}}, {"distanceMeters": 55061, "duration": "3555s", "staticDuration": "3555s", "polyline": {"encodedPolyline": "im~lAk}uyMFKt@aBxC_FPWt@yAx@{AHObCqEpD{HzAqDtAoCf@{@Ra@HQxAaClBiCLOb@g@RUlAkAhA{@XUx@i@h@Yr@]`Bo@fBaApGqDhAu@\\[^Uv@m@f@_@dAeATW|ByC?[b@m@`@i@TYX_@NSbHwIbB{BnH}IfEoElAkA^?hCkCNOzAeBnAgA|AeAtBmAfAo@jBgAtA_AZWv@q@|AqA`@]nDwCpCaCRQnBsAfEqDfAmAl@u@z@qA|BaEh@aAp@_Bz@iCnB{H~EmRV_A\\qAVw@j@uAhBgDh@iAn@gAp@y@nAoAt@q@VUxAkAxJcHnA{@zAgA\\UROp@g@hAw@n@e@\\Wb@_@TSb@a@RSpA_B~BcDfA{AVa@Zi@\\o@f@mAXo@ZeAf@uBX_BJg@fAyFJe@nAwGH]ZqAb@eBjCyI~@cEJe@VkAR{@R{@Ry@z@uCx@uCZ_A\\aA^{@P_@v@wAPYPYn@aAx@gAHKJO`FsGhBiCj@_AZm@v@kBZ{@Vu@ZkARaAH[Hk@PkARqBFcADeADaAHwARiBt@mDxAoHf@_DDa@R{Bv@}PF_AP}ATqAfAaGFYXsBNgAJeAHkA`@gEVuCD_@Dc@D_@PsBLyAFs@D_@L{AHo@^_DD_@Jg@XmAx@qCx@qBn@oArAwBzA{BbBgCdBeCvFqIT]f@y@|@uAf@w@b@s@j@aALWh@mAJUXs@Lc@x@{CJg@RiAxAkIFYLq@xBuLNw@|CiQNw@n@eDFYH]VkAd@_B\\}@Ti@bAoB|BcE`@q@`@q@r@aAb@e@l@k@bB_Bn@k@pA_Bl@y@l@sA`@}ADWFc@Hu@DeABy@E_ACUKcASeAu@uDSiBAsBJmBz@kFv@eDb@}Aj@eBt@sBnAuC~@eBrBeDdAyA`ByB~AeBXWZW|F_GrAuAbEcEjB{B|AqBjA}AvAeB~CeET[z@qAZq@rBiDh@o@f@k@|B{BbAmAv@kAf@cAtAsCZi@v@kAhCmDhB{Bn@}@zBaC~CaCdF}C~@m@pA{@bBqAn@o@z@{@vBqCbB}BzB}Cj@u@dF_HVc@t@mA^u@Z}@b@_Bt@eDhAoEJa@|AiGdAeDj@}BbBgFt@sC~AgGdA{Db@kBpAuGh@}Cb@}BDQ~@gEb@iBPo@d@oBf@mBLc@b@eBbC}Iz@{C|@wCr@aCp@mCt@{Cj@eCt@yCTcA`@_CBWDk@?oAC}@g@wGCmBH_CRkCT{CLkBD_@HyA?MJyEr@iFPqAPiABQXkBpBcNfAyHDYFk@PoABKPkDNuCN}BPwAZ_B`@eBz@sBd@eAxA_C~@eAhCyBz@u@j@i@hAw@`BoAnAkAz@gA`AgBxA}CtAaD|AwD`@cAZo@r@sBLi@PwABgCGkIEeCQuKCmBEaE@_@?SB}@L{AF[RaAZsAdAqEnAsFnAqFLg@|AuF`@_BjBcHNe@d@iBdCqIVgAn@}DPeBDa@b@eEh@_GDc@Fc@D[^aDj@qF\\cDr@aHHw@^kCZuAv@mC\\_AtAkEV{@jAmD|@sChBaGFURi@|AkE|AsEdAqDV{BNqCVqCLgA^uBPm@dAaC|@oBz@}ANYRc@d@{@b@cAzCqF`D}GpAoCp@gAf@q@z@_ApEcFr@iAhAuBlD_H~A}CdBgDzA{Cf@wA~AoFbAyDj@mBdD_MNk@t@iCh@gB|@_DT{@tAcFPi@|@iDt@mCXcAh@yCt@uCNi@Vu@bAqBhB_Dp@gBXgAbAwDlAoHp@qELw@^yBb@mCV}AlAuHv@sFd@kCnAyHTuA\\wAJa@b@oAN]bAyBhEgJx@iBzAyCjC{Fb@yANUVm@|BqFHOl@uAzBcFn@aBf@uBZwBp@sGLwA`AuJ@ILaAJ]Li@^gAx@aBDGbBmB`@a@b@WfAm@xCmAjFiBpDgBtB{AvAkA`DqC\\[fB_BXWlEcEhAkAvA_BfEeF|@{@~AoAxN_JdAo@fAo@hBy@\\MpBo@~NoD~Ag@RIl@_@|@u@rAsA`CqC~@mAv@cAd@k@T[d@_@hCaDfAwANSrCeDx@eAhH{IvD{E|EuFj@y@^q@Vk@\\{@Ri@Z{@Pc@zAwDJW~AyEdC{GhAoC`B}Dl@}AL]t@{B\\qARiATiBTsB^{EDy@RqC?c@?aBAe@MkBMsD@yA?CLoF?_@EaBWaCs@_EQyAGq@MmBAmC@y@@kC@i@LaBVmBd@oCJs@^_CTmAnBcLn@gC\\aAVm@FKf@y@tCoDpA}Ad@k@PUZi@j@iAFSbDgIRi@J]r@iB^cBN{@DUPiAr@oHToBLcAJs@TaAfBgFr@kBNa@jAmDJKL]v@uBPg@p@cB|@iCp@}AbAcBhBeB`@YbAk@zAs@hKkDhCeAtA}@rAoAf@k@R[pE_G^g@xBqCvAiBp@_Av@aA^c@l@}@`@w@L[l@sB\\_Bf@iDDW@k@D_@Fa@H]Rs@t@kBb@o@fAsAVYh@i@n@q@bEkEh@s@\\k@Vk@Xu@\\oAf@gDNy@`@_B\\yAj@iBr@kCL]Ps@dBsFr@aChB}FdBsFDKL[N_@Vc@fCqDfDiEX_@n@w@fCaDlCqDz@kAx@wARe@hDyGn@uA~A_DFOXm@^q@Va@r@gAFI~@cApAiA~DeDz@e@t@o@`GsELKv@u@HI|@mApCaFh@s@xA_Br@g@dDgCpCcB`DuBJIb@Yf@_@hAq@z@q@hA}@f@g@VYlAuA@?tBmCj@u@rA}A\\a@hAmAhAaAz@w@dC{BtAwAT[LM|H}IhBuBhEaFh@k@tAqADCfAw@nEwC~@g@dA}@p@k@z@{@|DyDtDuD`AmAn@_Al@iAZq@j@wARq@lByFxBiGH]xAeEXy@t@yAx@sA~BsCLMzAiBlB{BxBmC~@kAlAqAzBiBd@UnAw@ZOb@UnAo@vAi@JEnA]bAS"}, "startLocation": {"latLng": {"latitude": 12.7766925, "longitude": 77.77766280000002}}, "endLocation": {"latLng": {"latitude": 12.5429478, "longitude": 78.202068}}, "steps": [{"distanceMeters": 2090, "staticDuration": "213s", "polyline": {"encodedPolyline": "im~lAk}uyMFKt@aBxC_FPWt@yAx@{AHObCqEpD{HzAqDtAoCf@{@Ra@HQxAaClBiCLOb@g@RUlAkAhA{@XUx@i@h@Yr@]`Bo@fBaApGqDhAu@\\[^Uv@m@f@_@dAeATW|ByC"}, "startLocation": {"latLng": {"latitude": 12.7766925, "longitude": 77.77766280000002}}, "endLocation": {"latLng": {"latitude": 12.7644922, "longitude": 77.7918558}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head southeast on NH 4/NH 44/NH 48\nPass by Oneness Travels - Attibel<PERSON> Pickup (on the left)"}, "localizedValues": {"distance": {"text": "2.1 km"}, "staticDuration": {"text": "4 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 16, "staticDuration": "3s", "polyline": {"encodedPolyline": "aa|lAcvxyM?["}, "startLocation": {"latLng": {"latitude": 12.7644922, "longitude": 77.7918558}}, "endLocation": {"latLng": {"latitude": 12.764494299999999, "longitude": 77.7920019}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left"}, "localizedValues": {"distance": {"text": "16 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 452, "staticDuration": "75s", "polyline": {"encodedPolyline": "aa|lA_wxyMb@m@`@i@TYX_@NSbHwIbB{B"}, "startLocation": {"latLng": {"latitude": 12.764494299999999, "longitude": 77.7920019}}, "endLocation": {"latLng": {"latitude": 12.7618563, "longitude": 77.7951729}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at MRF TYRES WHEEL ALGN BALANCE\nPass by TamilNadu RTO Check Post (on the left)"}, "localizedValues": {"distance": {"text": "0.5 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 472, "staticDuration": "55s", "polyline": {"encodedPolyline": "sp{lAyjyyMnH}IfEoElAkA"}, "startLocation": {"latLng": {"latitude": 12.7618563, "longitude": 77.7951729}}, "endLocation": {"latLng": {"latitude": 12.7589486, "longitude": 77.7983423}}, "navigationInstruction": {"maneuver": "STRAIGHT", "instructions": "Continue straight"}, "localizedValues": {"distance": {"text": "0.5 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 18, "staticDuration": "2s", "polyline": {"encodedPolyline": "m~zlAs~yyM^?"}, "startLocation": {"latLng": {"latitude": 12.7589486, "longitude": 77.7983423}}, "endLocation": {"latLng": {"latitude": 12.7587856, "longitude": 77.798345}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right toward NH 4/NH 44/NH 48"}, "localizedValues": {"distance": {"text": "18 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 52013, "staticDuration": "3207s", "polyline": {"encodedPolyline": "m}zlAs~yyMhCkCNOzAeBnAgA|AeAtBmAfAo@jBgAtA_AZWv@q@|AqA`@]nDwCpCaCRQnBsAfEqDfAmAl@u@z@qA|BaEh@aAp@_Bz@iCnB{H~EmRV_A\\qAVw@j@uAhBgDh@iAn@gAp@y@nAoAt@q@VUxAkAxJcHnA{@zAgA\\UROp@g@hAw@n@e@\\Wb@_@TSb@a@RSpA_B~BcDfA{AVa@Zi@\\o@f@mAXo@ZeAf@uBX_BJg@fAyFJe@nAwGH]ZqAb@eBjCyI~@cEJe@VkAR{@R{@Ry@z@uCx@uCZ_A\\aA^{@P_@v@wAPYPYn@aAx@gAHKJO`FsGhBiCj@_AZm@v@kBZ{@Vu@ZkARaAH[Hk@PkARqBFcADeADaAHwARiBt@mDxAoHf@_DDa@R{Bv@}PF_AP}ATqAfAaGFYXsBNgAJeAHkA`@gEVuCD_@Dc@D_@PsBLyAFs@D_@L{AHo@^_DD_@Jg@XmAx@qCx@qBn@oArAwBzA{BbBgCdBeCvFqIT]f@y@|@uAf@w@b@s@j@aALWh@mAJUXs@Lc@x@{CJg@RiAxAkIFYLq@xBuLNw@|CiQNw@n@eDFYH]VkAd@_B\\}@Ti@bAoB|BcE`@q@`@q@r@aAb@e@l@k@bB_Bn@k@pA_Bl@y@l@sA`@}ADWFc@Hu@DeABy@E_ACUKcASeAu@uDSiBAsBJmBz@kFv@eDb@}Aj@eBt@sBnAuC~@eBrBeDdAyA`ByB~AeBXWZW|F_GrAuAbEcEjB{B|AqBjA}AvAeB~CeET[z@qAZq@rBiDh@o@f@k@|B{BbAmAv@kAf@cAtAsCZi@v@kAhCmDhB{Bn@}@zBaC~CaCdF}C~@m@pA{@bBqAn@o@z@{@vBqCbB}BzB}Cj@u@dF_HVc@t@mA^u@Z}@b@_Bt@eDhAoEJa@|AiGdAeDj@}BbBgFt@sC~AgGdA{Db@kBpAuGh@}Cb@}BDQ~@gEb@iBPo@d@oBf@mBLc@b@eBbC}Iz@{C|@wCr@aCp@mCt@{Cj@eCt@yCTcA`@_CBWDk@?oAC}@g@wGCmBH_CRkCT{CLkBD_@HyA?MJyEr@iFPqAPiABQXkBpBcNfAyHDYFk@PoABKPkDNuCN}BPwAZ_B`@eBz@sBd@eAxA_C~@eAhCyBz@u@j@i@hAw@`BoAnAkAz@gA`AgBxA}CtAaD|AwD`@cAZo@r@sBLi@PwABgCGkIEeCQuKCmBEaE@_@?SB}@L{AF[RaAZsAdAqEnAsFnAqFLg@|AuF`@_BjBcHNe@d@iBdCqIVgAn@}DPeBDa@b@eEh@_GDc@Fc@D[^aDj@qF\\cDr@aHHw@^kCZuAv@mC\\_AtAkEV{@jAmD|@sChBaGFURi@|AkE|AsEdAqDV{BNqCVqCLgA^uBPm@dAaC|@oBz@}ANYRc@d@{@b@cAzCqF`D}GpAoCp@gAf@q@z@_ApEcFr@iAhAuBlD_H~A}CdBgDzA{Cf@wA~AoFbAyDj@mBdD_MNk@t@iCh@gB|@_DT{@tAcFPi@|@iDt@mCXcAh@yCt@uCNi@Vu@bAqBhB_Dp@gBXgAbAwDlAoHp@qELw@^yBb@mCV}AlAuHv@sFd@kCnAyHTuA\\wAJa@b@oAN]bAyBhEgJx@iBzAyCjC{Fb@yANUVm@|BqFHOl@uAzBcFn@aBf@uBZwBp@sGLwA`AuJ@ILaAJ]Li@^gAx@aBDGbBmB`@a@b@WfAm@xCmAjFiBpDgBtB{AvAkA`DqC\\[fB_BXWlEcEhAkAvA_BfEeF|@{@~AoAxN_JdAo@fAo@hBy@\\MpBo@~NoD~Ag@RIl@_@|@u@rAsA`CqC~@mAv@cAd@k@T[d@_@hCaDfAwANSrCeDx@eAhH{IvD{E|EuFj@y@^q@Vk@\\{@Ri@Z{@Pc@zAwDJW~AyEdC{GhAoC`B}Dl@}AL]t@{B\\qARiATiBTsB^{EDy@RqC?c@?aBAe@MkBMsD@yA?CLoF?_@EaBWaCs@_EQyAGq@MmBAmC@y@@kC@i@LaBVmBd@oCJs@^_CTmAnBcLn@gC\\aAVm@FKf@y@tCoDpA}Ad@k@PUZi@j@iAFSbDgIRi@J]r@iB^cBN{@DUPiAr@oHToBLcAJs@TaAfBgFr@kBNa@jAmDJKL]v@uBPg@p@cB|@iCp@}AbAcBhBeB`@YbAk@zAs@hKkDhCeAtA}@rAoAf@k@R[pE_G^g@xBqCvAiBp@_Av@aA^c@l@}@`@w@L[l@sB\\_Bf@iDDW@k@D_@Fa@H]Rs@t@kBb@o@fAsAVYh@i@n@q@bEkEh@s@\\k@Vk@Xu@\\oAf@gDNy@`@_B\\yAj@iBr@kCL]Ps@dBsFr@aChB}FdBsFDKL[N_@Vc@fCqDfDiEX_@n@w@fCaDlCqDz@kAx@wARe@hDyGn@uA~A_DFOXm@^q@Va@r@gAFI~@cApAiA~DeDz@e@t@o@`GsELKv@u@HI|@mApCaFh@s@xA_Br@g@dDgCpCcB`DuBJIb@Yf@_@hAq@z@q@hA}@f@g@VYlAuA@?tBmCj@u@rA}A\\a@hAmAhAaAz@w@dC{BtAwAT[LM|H}IhBuBhEaFh@k@tAqADCfAw@nEwC~@g@dA}@p@k@z@{@|DyDtDuD`AmAn@_Al@iAZq@j@wARq@lByFxBiGH]xAeEXy@t@yAx@sA~BsCLMzAiBlB{BxBmC~@kAlAqAzBiBd@UnAw@ZOb@UnAo@vAi@JEnA]bAS"}, "startLocation": {"latLng": {"latitude": 12.7587856, "longitude": 77.798345}}, "endLocation": {"latLng": {"latitude": 12.5429478, "longitude": 78.202068}}, "navigationInstruction": {"maneuver": "MERGE", "instructions": "Merge onto NH 4/NH 44/NH 48\nToll road\nPass by the lake (on the right in 2.7 km)\nDestination will be on the left"}, "localizedValues": {"distance": {"text": "52.0 km"}, "staticDuration": {"text": "53 mins"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "55.1 km"}, "duration": {"text": "59 mins"}, "staticDuration": {"text": "59 mins"}}}, {"distanceMeters": 63623, "duration": "3516s", "staticDuration": "3516s", "polyline": {"encodedPolyline": "mxpkA}yh|Mt@MzBa@b@Gp[yFt@SjQ{C`B]tA[tA]rDcA`HyBpCeAr@Y~BaAtFgCr@_@pBcAhHeEpHaFxCoBzG_F`Ao@h@a@tDkCZUtKsHtGsEfEsCBCbJkGZU|HqFxAcAvEcDtOwKfAu@r@c@h@W`Ae@`Cy@x@UhCc@t@KvAMtCKt@?z@@fEPL@J@|@Ll@Ln@N|A^n@NTFvDbALDB?vBp@rEbBh@\\`FvBvB`A^P`IjDfAb@zBz@zCx@~@NTDjEb@r@FRBp@Hr@FzALjBPF@zEb@dCTn@Hb@DdAJrAPhAPpB^tCt@zEjBnBx@pC~@|Bh@`AL|Hv@|ANpCTfA@xZ|C~@HzD`@|C<PERSON>d<PERSON><PERSON>@dENfBD\\@bA?xA@jAAlLW~KWRA`AGxASx@OpEmAfBa@hBKrEi@pBSpAO^OxDc@zAUbCe@rCu@~CiA`EgB|CsA`Ac@RGbBk@f@Kl@KlAMnBGrCHN@v@FnKl@D?pCBfCGrBOvBWxCm@jCy@rCiAdDgBFEd@WzCaBvHeE|Au@nCiAbGwBrBo@rNmEZKjGmBfJoChK}CnEuAhD}@x@KtBMnB@nAHv@Jb@Fz@JZDbJrAd@FtB\\lRnCRBzB\\jL`Bv@JvCZdSrAjN~@zARfAVr@TLDrAj@|A|@n@h@dA~@zG|GLLr@r@tCxCJTfAjAnCdCf@`@r@b@p@XNFl@Rx@Pl@J|@FbABhACvAOHClD[|@Q`LkAfCOrD_@HAlDSjEUdDUzEk@nDe@bAK\\Al@AvAAv@DvANnBZ~B`@vB^lJpApCZdD^zARhALrLr@b@BjKn@fBNpIbBdCf@lE|@fDt@lCj@rG`B|Bd@|AThCRxCNzDR~CNjBJjAHfE^xALhPnAZDdE\\pBLn@@zB@`BC|CG`Mu@|EU`Dc@pASnEq@~GcA^E`CUxCIzIOjM]hEEzCCnDCdDDnDPfBLf@Db@DrFn@lGx@tAXhHvApFnAbAZbAf@fAl@hHlEr@b@jClAlNhE|E|AzBz@bDtA|IxDxErBp@VnBl@j@LhANlCLjB?pGGpDGtCEfAClDQ`Ko@z@E|Hi@`Im@xEi@lF_@J?`CKrB?t@BvAJ|ATZFZFdFfAxGzAdDr@zBh@rA^p@Xl@ZRJ`Ar@zBfBhA`ApCvBbG~Fh@f@fB`B`Az@fAbArD|DnEpF`@l@FFVR|@f@b@ZVNb@PtA`@VFvATbBLf@BbP|@t@DvCVbDXtALtDj@`C`@hBXjC\\vCTrLz@|Ij@hJp@tBR`DVrFd@lEPvBBV@n@?^ApDCnJEdACnAG|AKpMqAlCWpHw@dNyApAOl@KPC`ASnBk@zAk@v@]fAo@~@k@lFiD|@e@~@_@v@UZIxB]pAKrAA~ABjBPpATbA\\tB|@TJv@d@nAbAp@r@b@h@xDzFnAlBvBpCPTt@t@FFv@f@LHnAj@hAZbAND@`@B@?jADtAAbJY`@AfDGn@?jD@rGFhEFzDAd@@rAB~B@bBB`@?jBBb@?|KOv@?`EN|@J~ARrCl@D?rA^jA`@r@Xb@F~CdBjBrAbDnCnAdAfAr@d@XvAj@r@RZF`@Hr@HX@nADlACfG[tHc@rAAnBBxBPrAPtGpBvAf@dJjCrAZdF~@|Dj@hGt@vAXrA\\|@`@pB|@`EjB|@b@r@NtCfAz@VbAZnAd@h@Tt@^zAbAxAnApEzDHHr@j@|BbBlFrDj@`@VNxBnA|At@tBv@fBf@lAXPFvAVf@DxAPL@~CR`C@d@?hBAdENdBRfBd@f@RlB|@nDlBd@XjAl@xDlBhI|D^NfDrA~Bt@nI`CnBp@fAh@dAj@x@j@jAz@`ExCnEnDd@\\zDlCp@`@vAx@v@d@zIhEvDvA~Ah@`JhChEjALBfGhBh@Rn@Zj@Zj@Xb@T`At@d@d@t@x@bPjTd@n@jCrDvBnCx@dAzBxCpMhQbAnAn@x@l@t@rCdDjDdDXV|AtAf@d@~DnDzDnDd@`@vArAhHpGhFtEtLjKf@b@TTxBjBhC`Cz@t@VTRRLHVVh@d@vKzJxApAhA|@bAz@`BjAhCbBzBlALDhCnAp@Z^P`DnAfF`B`AVjAXbDv@zLpCfCn@vD|@jEbAzKfCtEdAtFpAzA\\pInBzA\\tBd@RFnAXlCn@tGxAdFlAfAVj@NzAZlH`B|FtAjAX^HZN`Cf@dBZrCp@rCp@VF~M|CxDz@jCv@zEdB\\LdEtBvAp@rBhAlBnA~GhFhG~E~DbDfBvAhDpCxGtFHF`HvFzJfI`@\\tDzCPNhCjBbH|FTZtAdAtEtCfB`ArFhCdBn@b@NdHzB`Cj@jDn@|HhA~MfBzInApEf@bIr@fJv@zBNrGn@jBPvFf@~VvBt@FjR~Ar@Fr@FzGl@hUpBtGl@xFd@b@D`AHlE`@N@pHl@dBNfThB`K|@f@FXF`@F|ARzIxArAVbCj@bDz@fCr@tA^l@LfDn@hARfCf@nARxKhBzAThFz@tHtAJB|KpBdFt@fALbEd@`ALrDd@rDf@pARj@HfBV~AV`@HTDl@RlBt@TLfAn@d@\\ZTz@r@xAnAlCrB~@p@pBlApCfBINaF}CgC_BM^?Nk@hA"}, "startLocation": {"latLng": {"latitude": 12.5429478, "longitude": 78.202068}}, "endLocation": {"latLng": {"latitude": 12.024792600000001, "longitude": 78.0981455}}, "steps": [{"distanceMeters": 63293, "staticDuration": "3454s", "polyline": {"encodedPolyline": "mxpkA}yh|Mt@MzBa@b@Gp[yFt@SjQ{C`B]tA[tA]rDcA`HyBpCeAr@Y~BaAtFgCr@_@pBcAhHeEpHaFxCoBzG_F`Ao@h@a@tDkCZUtKsHtGsEfEsCBCbJkGZU|HqFxAcAvEcDtOwKfAu@r@c@h@W`Ae@`Cy@x@UhCc@t@KvAMtCKt@?z@@fEPL@J@|@Ll@Ln@N|A^n@NTFvDbALDB?vBp@rEbBh@\\`FvBvB`A^P`IjDfAb@zBz@zCx@~@NTDjEb@r@FRBp@Hr@FzALjBPF@zEb@dCTn@Hb@DdAJrAPhAPpB^tCt@zEjBnBx@pC~@|Bh@`AL|Hv@|ANpCTfA@xZ|C~@HzD`@|C<PERSON>d<PERSON><PERSON>@dENfBD\\@bA?xA@jAAlLW~KWRA`AGxASx@OpEmAfBa@hBKrEi@pBSpAO^OxDc@zAUbCe@rCu@~CiA`EgB|CsA`Ac@RGbBk@f@Kl@KlAMnBGrCHN@v@FnKl@D?pCBfCGrBOvBWxCm@jCy@rCiAdDgBFEd@WzCaBvHeE|Au@nCiAbGwBrBo@rNmEZKjGmBfJoChK}CnEuAhD}@x@KtBMnB@nAHv@Jb@Fz@JZDbJrAd@FtB\\lRnCRBzB\\jL`Bv@JvCZdSrAjN~@zARfAVr@TLDrAj@|A|@n@h@dA~@zG|GLLr@r@tCxCJTfAjAnCdCf@`@r@b@p@XNFl@Rx@Pl@J|@FbABhACvAOHClD[|@Q`LkAfCOrD_@HAlDSjEUdDUzEk@nDe@bAK\\Al@AvAAv@DvANnBZ~B`@vB^lJpApCZdD^zARhALrLr@b@BjKn@fBNpIbBdCf@lE|@fDt@lCj@rG`B|Bd@|AThCRxCNzDR~CNjBJjAHfE^xALhPnAZDdE\\pBLn@@zB@`BC|CG`Mu@|EU`Dc@pASnEq@~GcA^E`CUxCIzIOjM]hEEzCCnDCdDDnDPfBLf@Db@DrFn@lGx@tAXhHvApFnAbAZbAf@fAl@hHlEr@b@jClAlNhE|E|AzBz@bDtA|IxDxErBp@VnBl@j@LhANlCLjB?pGGpDGtCEfAClDQ`Ko@z@E|Hi@`Im@xEi@lF_@J?`CKrB?t@BvAJ|ATZFZFdFfAxGzAdDr@zBh@rA^p@Xl@ZRJ`Ar@zBfBhA`ApCvBbG~Fh@f@fB`B`Az@fAbArD|DnEpF`@l@FFVR|@f@b@ZVNb@PtA`@VFvATbBLf@BbP|@t@DvCVbDXtALtDj@`C`@hBXjC\\vCTrLz@|Ij@hJp@tBR`DVrFd@lEPvBBV@n@?^ApDCnJEdACnAG|AKpMqAlCWpHw@dNyApAOl@KPC`ASnBk@zAk@v@]fAo@~@k@lFiD|@e@~@_@v@UZIxB]pAKrAA~ABjBPpATbA\\tB|@TJv@d@nAbAp@r@b@h@xDzFnAlBvBpCPTt@t@FFv@f@LHnAj@hAZbAND@`@B@?jADtAAbJY`@AfDGn@?jD@rGFhEFzDAd@@rAB~B@bBB`@?jBBb@?|KOv@?`EN|@J~ARrCl@D?rA^jA`@r@Xb@F~CdBjBrAbDnCnAdAfAr@d@XvAj@r@RZF`@Hr@HX@nADlACfG[tHc@rAAnBBxBPrAPtGpBvAf@dJjCrAZdF~@|Dj@hGt@vAXrA\\|@`@pB|@`EjB|@b@r@NtCfAz@VbAZnAd@h@Tt@^zAbAxAnApEzDHHr@j@|BbBlFrDj@`@VNxBnA|At@tBv@fBf@lAXPFvAVf@DxAPL@~CR`C@d@?hBAdENdBRfBd@f@RlB|@nDlBd@XjAl@xDlBhI|D^NfDrA~Bt@nI`CnBp@fAh@dAj@x@j@jAz@`ExCnEnDd@\\zDlCp@`@vAx@v@d@zIhEvDvA~Ah@`JhChEjALBfGhBh@Rn@Zj@Zj@Xb@T`At@d@d@t@x@bPjTd@n@jCrDvBnCx@dAzBxCpMhQbAnAn@x@l@t@rCdDjDdDXV|AtAf@d@~DnDzDnDd@`@vArAhHpGhFtEtLjKf@b@TTxBjBhC`Cz@t@VTRRLHVVh@d@vKzJxApAhA|@bAz@`BjAhCbBzBlALDhCnAp@Z^P`DnAfF`B`AVjAXbDv@zLpCfCn@vD|@jEbAzKfCtEdAtFpAzA\\pInBzA\\tBd@RFnAXlCn@tGxAdFlAfAVj@NzAZlH`B|FtAjAX^HZN`Cf@dBZrCp@rCp@VF~M|CxDz@jCv@zEdB\\LdEtBvAp@rBhAlBnA~GhFhG~E~DbDfBvAhDpCxGtFHF`HvFzJfI`@\\tDzCPNhCjBbH|FTZtAdAtEtCfB`ArFhCdBn@b@NdHzB`Cj@jDn@|HhA~MfBzInApEf@bIr@fJv@zBNrGn@jBPvFf@~VvBt@FjR~Ar@Fr@FzGl@hUpBtGl@xFd@b@D`AHlE`@N@pHl@dBNfThB`K|@f@FXF`@F|ARzIxArAVbCj@bDz@fCr@tA^l@LfDn@hARfCf@nARxKhBzAThFz@tHtAJB|KpBdFt@fALbEd@`ALrDd@rDf@pARj@HfBV~AV`@HTDl@RlBt@TLfAn@d@\\ZTz@r@xAnAlCrB~@p@pBlApCfB"}, "startLocation": {"latLng": {"latitude": 12.5429478, "longitude": 78.202068}}, "endLocation": {"latLng": {"latitude": 12.0226358, "longitude": 78.0975713}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head south on NH 4/NH 44/NH 48 toward Service Road\nContinue to follow NH 44\nPass by Red Dhaba (on the left in 37.2 km)"}, "localizedValues": {"distance": {"text": "63.3 km"}, "staticDuration": {"text": "58 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 254, "staticDuration": "38s", "polyline": {"encodedPolyline": "odkhAylt{MINaF}CgC_B"}, "startLocation": {"latLng": {"latitude": 12.0226358, "longitude": 78.0975713}}, "endLocation": {"latLng": {"latitude": 12.0244986, "longitude": 78.0987602}}, "navigationInstruction": {"maneuver": "UTURN_RIGHT", "instructions": "Make a U-turn at Palayapudur..x.Road\nPass by GanashaTraders (on the right)"}, "localizedValues": {"distance": {"text": "0.3 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 28, "staticDuration": "10s", "polyline": {"encodedPolyline": "cpkhAgtt{MM^?N"}, "startLocation": {"latLng": {"latitude": 12.0244986, "longitude": 78.0987602}}, "endLocation": {"latLng": {"latitude": 12.0245661, "longitude": 78.0985226}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left at JOJO MOMOS - Dharmapuri NH"}, "localizedValues": {"distance": {"text": "28 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 48, "staticDuration": "14s", "polyline": {"encodedPolyline": "qpkhAwrt{Mk@hA"}, "startLocation": {"latLng": {"latitude": 12.0245661, "longitude": 78.0985226}}, "endLocation": {"latLng": {"latitude": 12.024792600000001, "longitude": 78.0981455}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_RIGHT", "instructions": "Slight right at the gas station\nDestination will be on the right"}, "localizedValues": {"distance": {"text": "48 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "63.6 km"}, "duration": {"text": "59 mins"}, "staticDuration": {"text": "59 mins"}}}], "distanceMeters": 139956, "duration": "9363s", "staticDuration": "9363s", "polyline": {"encodedPolyline": "sx|mAe`oyMKPb@Zn@d@l@`@LHp@h@d@Z|@j@h@`@h@\\fAx@B@LHf@\\PLn@b@VPRN`@Xl@`@PLDBp@d@XRh@^RNf@\\^V\\V~@l@PNJHPJjAn@FB\\NXLd@Tz@`@n@XRJFBLFn@\\ZNx@\\TJPDt@RNBn@DX@V@V?bDDT?rCBL?p@@pA?j@?h@Ar@AfAAtAEd@G|AQz@SDAnB}@zBeAdCmAbFaCnAm@zE}B|Aq@|BiAHE^[PQf@o@fAuA\\a@dB{Bj@u@`@i@t@_At@aAfDoEl@y@n@{@j@y@zB}C|@iAf@c@n@SxDu@lB]nAUr@OPCnCi@|AYl@MjB_@|A[fAQjDg@`BSd@ED?x@G`Ge@`@ETA`@CNANC|Ec@z@IbAKbCUn@IfD_@fAOzEq@rEo@d@KHAbDe@|@IrFq@LAlBSTAv@I\\GHAREpBa@j@MHCVIx@WvAe@r@UhAc@`@MDANEHEv@[\\Mr@U|Ai@TIz@UHCtA_@x@SbBc@H]TeATaAT{@DQJa@F[Jg@BIT_ADOPu@FUR_Ap@cDRcATiAd@gBLa@Rk@^y@JULYVk@nAqCdA_CVi@xAiDVk@v@gBb@aALYjAeCtBcFh@oAPa@N]Te@\\o@DE\\WZMf@KRGFALClBW`BYn@KFApAOdAOb@ILAxBa@zCe@bAO\\Ev@M|AW^EdAGf@Cb@ArAA^AV?dAA`@?t@?dA?X?^?F?~@I@?j@MPGPGPKbAu@t@m@~@w@DElAeAzBiBhA}@XY\\WTSvBeBXWNM^[`Ay@VU|AmAb@_@^YNMzCmC\\WLQVs@Hg@@KVyBVaCBSXiCTqBDa@Fg@@KJqAH_ALgCBc@?ILcCJuAF_A@WFwA?k@?q@Ae@IyA?c@AmA?_@A_@CoAEaB?QAYCcAAa@GkA?YCIBCDAB?f@@bI~@l@HVD`@Fz@Hl@Hp@Jv@JVBF?vEf@j@FnDVtBFt@?nBB`@?`EDj@?`BBhDD~@@`BB^?nGHvCAJA`@E|Eo@`Ca@fG}@|Ba@bLqB`Cc@|A[xAY\\GfB_@|KoBtBYh@INA^E~AKJAjE?v@@fBKnFa@hGe@h@Gx@G~D]j@EtBUvBKzBKlEUfAIj@GrCErA?`BNTB~@JlAPvARdAN~ATj@HvBTj@HfBF`@@zFPzAD|GRB?|@B~DJ`EN~AFJ@vFTjBJfDVnAJxGn@fANtHfAvIlA|Df@dAJ|BR\\BnCTZBb@BhL|@XBnBZRHfAPPDd@NRHl@\\\\V~@hA@@t@jAlAhAj@^zAf@jB~@v@VfBJl@Jz@NvAJfAVzBt@dA`@B?VHRHTP`@Zn@n@JJ`C`C~EnEJHbBzALJhAv@FBdG~BhBr@jE`BNF~ElBfBp@zCjAfBp@ND~An@tChAnFpBp@T`Ab@VJj@XTHt@\\p@ZfAd@vB`A|@b@|@^XDl@IPIJOd@yDD_@j@iFFm@L_AJw@TcBR{@FUJWNGJ]p@mB@AXcAj@eBDQx@qBd@cANWFIFKt@aBxC_FPWt@yAx@{AHObCqEpD{HzAqDtAoCf@{@Ra@HQxAaClBiCLOb@g@RUlAkAhA{@XUx@i@h@Yr@]`Bo@fBaApGqDhAu@\\[^Uv@m@f@_@dAeATW|ByC?[b@m@`@i@TYX_@NSbHwIbB{BnH}IfEoElAkA^?hCkCNOzAeBnAgA|AeAtBmAfAo@jBgAtA_AZWv@q@|AqA`@]nDwCpCaCRQnBsAfEqDfAmAl@u@z@qA|BaEh@aAp@_Bz@iCnB{H~EmRV_A\\qAVw@j@uAhBgDh@iAn@gAp@y@nAoAt@q@VUxAkAxJcHnA{@zAgA\\UROp@g@hAw@n@e@\\Wb@_@TSb@a@RSpA_B~BcDfA{AVa@Zi@\\o@f@mAXo@ZeAf@uBX_BJg@fAyFJe@nAwGH]ZqAb@eBjCyI~@cEJe@VkAR{@R{@Ry@z@uCx@uCZ_A\\aA^{@P_@v@wAPYPYn@aAx@gAHKJO`FsGhBiCj@_AZm@v@kBZ{@Vu@ZkARaAH[Hk@PkARqBFcADeADaAHwARiBt@mDxAoHf@_DDa@R{Bv@}PF_AP}ATqAfAaGFYXsBNgAJeAHkA`@gEVuCD_@Dc@D_@PsBLyAFs@D_@L{AHo@^_DD_@Jg@XmAx@qCx@qBn@oArAwBzA{BbBgCdBeCvFqIT]f@y@|@uAf@w@b@s@j@aALWh@mAJUXs@Lc@x@{CJg@RiAxAkIFYLq@xBuLNw@|CiQNw@n@eDFYH]VkAd@_B\\}@Ti@bAoB|BcE`@q@`@q@r@aAb@e@l@k@bB_Bn@k@pA_Bl@y@l@sA`@}ADWFc@Hu@DeABy@E_ACUKcASeAu@uDSiBAsBJmBz@kFv@eDb@}Aj@eBt@sBnAuC~@eBrBeDdAyA`ByB~AeBXWZW|F_GrAuAbEcEjB{B|AqBjA}AvAeB~CeET[z@qAZq@rBiDh@o@f@k@|B{BbAmAv@kAf@cAtAsCZi@v@kAhCmDhB{Bn@}@zBaC~CaCdF}C~@m@pA{@bBqAn@o@z@{@vBqCbB}BzB}Cj@u@dF_HVc@t@mA^u@Z}@b@_Bt@eDhAoEJa@|AiGdAeDj@}BbBgFt@sC~AgGdA{Db@kBpAuGh@}Cb@}BDQ~@gEb@iBPo@d@oBf@mBLc@b@eBbC}Iz@{C|@wCr@aCp@mCt@{Cj@eCt@yCTcA`@_CBWDk@?oAC}@g@wGCmBH_CRkCT{CLkBD_@HyA?MJyEr@iFPqAPiABQXkBpBcNfAyHDYFk@PoABKPkDNuCN}BPwAZ_B`@eBz@sBd@eAxA_C~@eAhCyBz@u@j@i@hAw@`BoAnAkAz@gA`AgBxA}CtAaD|AwD`@cAZo@r@sBLi@PwABgCGkIEeCQuKCmBEaE@_@?SB}@L{AF[RaAZsAdAqEnAsFnAqFLg@|AuF`@_BjBcHNe@d@iBdCqIVgAn@}DPeBDa@b@eEh@_GDc@Fc@D[^aDj@qF\\cDr@aHHw@^kCZuAv@mC\\_AtAkEV{@jAmD|@sChBaGFURi@|AkE|AsEdAqDV{BNqCVqCLgA^uBPm@dAaC|@oBz@}ANYRc@d@{@b@cAzCqF`D}GpAoCp@gAf@q@z@_ApEcFr@iAhAuBlD_H~A}CdBgDzA{Cf@wA~AoFbAyDj@mBdD_MNk@t@iCh@gB|@_DT{@tAcFPi@|@iDt@mCXcAh@yCt@uCNi@Vu@bAqBhB_Dp@gBXgAbAwDlAoHp@qELw@^yBb@mCV}AlAuHv@sFd@kCnAyHTuA\\wAJa@b@oAN]bAyBhEgJx@iBzAyCjC{Fb@yANUVm@|BqFHOl@uAzBcFn@aBf@uBZwBp@sGLwA`AuJ@ILaAJ]Li@^gAx@aBDGbBmB`@a@b@WfAm@xCmAjFiBpDgBtB{AvAkA`DqC\\[fB_BXWlEcEhAkAvA_BfEeF|@{@~AoAxN_JdAo@fAo@hBy@\\MpBo@~NoD~Ag@RIl@_@|@u@rAsA`CqC~@mAv@cAd@k@T[d@_@hCaDfAwANSrCeDx@eAhH{IvD{E|EuFj@y@^q@Vk@\\{@Ri@Z{@Pc@zAwDJW~AyEdC{GhAoC`B}Dl@}AL]t@{B\\qARiATiBTsB^{EDy@RqC?c@?aBAe@MkBMsD@yA?CLoF?_@EaBWaCs@_EQyAGq@MmBAmC@y@@kC@i@LaBVmBd@oCJs@^_CTmAnBcLn@gC\\aAVm@FKf@y@tCoDpA}Ad@k@PUZi@j@iAFSbDgIRi@J]r@iB^cBN{@DUPiAr@oHToBLcAJs@TaAfBgFr@kBNa@jAmDJKL]v@uBPg@p@cB|@iCp@}AbAcBhBeB`@YbAk@zAs@hKkDhCeAtA}@rAoAf@k@R[pE_G^g@xBqCvAiBp@_Av@aA^c@l@}@`@w@L[l@sB\\_Bf@iDDW@k@D_@Fa@H]Rs@t@kBb@o@fAsAVYh@i@n@q@bEkEh@s@\\k@Vk@Xu@\\oAf@gDNy@`@_B\\yAj@iBr@kCL]Ps@dBsFr@aChB}FdBsFDKL[N_@Vc@fCqDfDiEX_@n@w@fCaDlCqDz@kAx@wARe@hDyGn@uA~A_DFOXm@^q@Va@r@gAFI~@cApAiA~DeDz@e@t@o@`GsELKv@u@HI|@mApCaFh@s@xA_Br@g@dDgCpCcB`DuBJIb@Yf@_@hAq@z@q@hA}@f@g@VYlAuA@?tBmCj@u@rA}A\\a@hAmAhAaAz@w@dC{BtAwAT[LM|H}IhBuBhEaFh@k@tAqADCfAw@nEwC~@g@dA}@p@k@z@{@|DyDtDuD`AmAn@_Al@iAZq@j@wARq@lByFxBiGH]xAeEXy@t@yAx@sA~BsCLMzAiBlB{BxBmC~@kAlAqAzBiBd@UnAw@ZOb@UnAo@vAi@JEnA]bASt@MzBa@b@Gp[yFt@SjQ{C`B]tA[tA]rDcA`HyBpCeAr@Y~BaAtFgCr@_@pBcAhHeEpHaFxCoBzG_F`Ao@h@a@tDkCZUtKsHtGsEfEsCBCbJkGZU|HqFxAcAvEcDtOwKfAu@r@c@h@W`Ae@`Cy@x@UhCc@t@KvAMtCKt@?z@@fEPL@J@|@Ll@Ln@N|A^n@NTFvDbALDB?vBp@rEbBh@\\`FvBvB`A^P`IjDfAb@zBz@zCx@~@NTDjEb@r@FRBp@Hr@FzALjBPF@zEb@dCTn@Hb@DdAJrAPhAPpB^tCt@zEjBnBx@pC~@|Bh@`AL|Hv@|ANpCTfA@xZ|C~@HzD`@|CZdEXJ@dENfBD\\@bA?xA@jAAlLW~KWRA`AGxASx@OpEmAfBa@hBKrEi@pBSpAO^OxDc@zAUbCe@rCu@~CiA`EgB|CsA`Ac@RGbBk@f@Kl@KlAMnBGrCHN@v@FnKl@D?pCBfCGrBOvBWxCm@jCy@rCiAdDgBFEd@WzCaBvHeE|Au@nCiAbGwBrBo@rNmEZKjGmBfJoChK}CnEuAhD}@x@KtBMnB@nAHv@Jb@Fz@JZDbJrAd@FtB\\lRnCRBzB\\jL`Bv@JvCZdSrAjN~@zARfAVr@TLDrAj@|A|@n@h@dA~@zG|GLLr@r@tCxCJTfAjAnCdCf@`@r@b@p@XNFl@Rx@Pl@J|@FbABhACvAOHClD[|@Q`LkAfCOrD_@HAlDSjEUdDUzEk@nDe@bAK\\Al@AvAAv@DvANnBZ~B`@vB^lJpApCZdD^zARhALrLr@b@BjKn@fBNpIbBdCf@lE|@fDt@lCj@rG`B|Bd@|AThCRxCNzDR~CNjBJjAHfE^xALhPnAZDdE\\pBLn@@zB@`BC|CG`Mu@|EU`Dc@pASnEq@~GcA^E`CUxCIzIOjM]hEEzCCnDCdDDnDPfBLf@Db@DrFn@lGx@tAXhHvApFnAbAZbAf@fAl@hHlEr@b@jClAlNhE|E|AzBz@bDtA|IxDxErBp@VnBl@j@LhANlCLjB?pGGpDGtCEfAClDQ`Ko@z@E|Hi@`Im@xEi@lF_@J?`CKrB?t@BvAJ|ATZFZFdFfAxGzAdDr@zBh@rA^p@Xl@ZRJ`Ar@zBfBhA`ApCvBbG~Fh@f@fB`B`Az@fAbArD|DnEpF`@l@FFVR|@f@b@ZVNb@PtA`@VFvATbBLf@BbP|@t@DvCVbDXtALtDj@`C`@hBXjC\\vCTrLz@|Ij@hJp@tBR`DVrFd@lEPvBBV@n@?^ApDCnJEdACnAG|AKpMqAlCWpHw@dNyApAOl@KPC`ASnBk@zAk@v@]fAo@~@k@lFiD|@e@~@_@v@UZIxB]pAKrAA~ABjBPpATbA\\tB|@TJv@d@nAbAp@r@b@h@xDzFnAlBvBpCPTt@t@FFv@f@LHnAj@hAZbAND@`@B@?jADtAAbJY`@AfDGn@?jD@rGFhEFzDAd@@rAB~B@bBB`@?jBBb@?|KOv@?`EN|@J~ARrCl@D?rA^jA`@r@Xb@F~CdBjBrAbDnCnAdAfAr@d@XvAj@r@RZF`@Hr@HX@nADlACfG[tHc@rAAnBBxBPrAPtGpBvAf@dJjCrAZdF~@|Dj@hGt@vAXrA\\|@`@pB|@`EjB|@b@r@NtCfAz@VbAZnAd@h@Tt@^zAbAxAnApEzDHHr@j@|BbBlFrDj@`@VNxBnA|At@tBv@fBf@lAXPFvAVf@DxAPL@~CR`C@d@?hBAdENdBRfBd@f@RlB|@nDlBd@XjAl@xDlBhI|D^NfDrA~Bt@nI`CnBp@fAh@dAj@x@j@jAz@`ExCnEnDd@\\zDlCp@`@vAx@v@d@zIhEvDvA~Ah@`JhChEjALBfGhBh@Rn@Zj@Zj@Xb@T`At@d@d@t@x@bPjTd@n@jCrDvBnCx@dAzBxCpMhQbAnAn@x@l@t@rCdDjDdDXV|AtAf@d@~DnDzDnDd@`@vArAhHpGhFtEtLjKf@b@TTxBjBhC`Cz@t@VTRRLHVVh@d@vKzJxApAhA|@bAz@`BjAhCbBzBlALDhCnAp@Z^P`DnAfF`B`AVjAXbDv@zLpCfCn@vD|@jEbAzKfCtEdAtFpAzA\\pInBzA\\tBd@RFnAXlCn@tGxAdFlAfAVj@NzAZlH`B|FtAjAX^HZN`Cf@dBZrCp@rCp@VF~M|CxDz@jCv@zEdB\\LdEtBvAp@rBhAlBnA~GhFhG~E~DbDfBvAhDpCxGtFHF`HvFzJfI`@\\tDzCPNhCjBbH|FTZtAdAtEtCfB`ArFhCdBn@b@NdHzB`Cj@jDn@|HhA~MfBzInApEf@bIr@fJv@zBNrGn@jBPvFf@~VvBt@FjR~Ar@Fr@FzGl@hUpBtGl@xFd@b@D`AHlE`@N@pHl@dBNfThB`K|@f@FXF`@F|ARzIxArAVbCj@bDz@fCr@tA^l@LfDn@hARfCf@nARxKhBzAThFz@tHtAJB|KpBdFt@fALbEd@`ALrDd@rDf@pARj@HfBV~AV`@HTDl@RlBt@TLfAn@d@\\ZTz@r@xAnAlCrB~@p@pBlApCfBINaF}CgC_BM^?Nk@hA"}, "description": "Varthur - Sarjapur Rd", "warnings": ["This route has tolls."], "viewport": {"low": {"latitude": 12.0226358, "longitude": 77.7364482}, "high": {"latitude": 12.932161299999999, "longitude": 78.2268357}}, "travelAdvisory": {}, "localizedValues": {"distance": {"text": "140 km"}, "duration": {"text": "2 hours 36 mins"}, "staticDuration": {"text": "2 hours 36 mins"}}, "routeLabels": ["DEFAULT_ROUTE"], "polylineDetails": {}}], "geocodingResults": {}}