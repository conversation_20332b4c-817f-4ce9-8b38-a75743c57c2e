{"routes": [{"legs": [{"distanceMeters": 18352, "duration": "3120s", "staticDuration": "3120s", "polyline": {"encodedPolyline": "sx|mAe`oyMKPb@Zn@d@l@`@LHp@h@d@Z|@j@h@`@h@\\fAx@B@LHf@\\PLn@b@VPRN`@Xl@`@PLDBp@d@XRh@^RNf@\\^V\\V~@l@PNJHPJjAn@FB\\NXLd@Tz@`@n@XRJFBLFn@\\ZNx@\\TJPDt@RNBn@DX@V@V?bDDT?rCBL?p@@pA?j@?h@Ar@AfAAtAEd@G@FGdCAj@@R@JXp@DJX\\BDBFLZFT@N@FDp@LjBFr@B\\BRDr@JpA@R@JNzBRbC@ZJ|AFt@?T@LBr@HfD@XPvED|@Fl@Ft@Hp@Fl@Lr@DPHTLVBFDHLPRTLLZXFBPLv@\\ZLhA`@h@Vd@XBBHFLl@BZFx@FpADh@P~AAfACZ?HE\\MnAC\\RrAPrA@l@QbBKl@c@rCGpA?b@Pv@b@l@PVb@r@BHNVP^DHVl@HNFHn@bAHLb@l@LLl@x@J^FHXTHHf@VXL`@PNF\\PLFTHNHNJXLx@`@BDFJ@DCNO`@q@vAKRIVCDGLIPSh@Id@Bj@t@?\\@z@D`BExAE~FMVDlAj@dCtAd@^\\RVHj@Tv@`@\\RXNlAp@LHNFfAh@XN`Ab@^Jj@TnAd@|@ZdC~@tEjBlFjBhBh@LAb@H\\?FSTo@J[Nc@HWLDsBzFEL{AhEMZMXGNABWj@Yr@MXSb@gBbEMXOXO\\OZO\\MXWj@Yt@Sh@[n@GP]n@}@pBYl@q@zA]t@e@hA_ArBq@xAUd@GNIRIRKZGRWz@YnAMp@I`@G\\Kh@O~@Mr@Ov@[xAa@vB[nAIf@Ed@CNE^_@|BIh@AJGh@ShAG^WbBKj@u@zDOt@CPOt@Kj@EZQz@Gb@SfAENQv@IV]nAGR]nAGPEPELEPMf@Y`AEP[l@KXUz@Ut@GPc@tAELM`@M`@Od@CLKZWdA]dA]nAGRy@nC[z@O\\m@zAIRGNEJu@fB_AzBWn@GNOZELoBxESp@q@xBi@hBt@BHFF\\Px@HP^Rp@VF@p@J`@H`@Fz@DR?v@B~@BdB@^Ab@Af@LLDr@Xl@TVF`@?TGp@S`@?XHr@T|@^JDd@R|@XTDt@HVBP?P?~@CL?fBAP?L?l@GHA|@[JEf@OZEtAJ\\@r@DVBhADx@Fz@FZBP@p@D~BLtAJb@?TBl@Dp@Dd@B`@BVBf@@J?d@?jAAZ@jA?lC@~A@RB`@Dv@R`AR|EpAl@N|AZlFtAfCr@J@LDL@h@JVB^Dj@HN@D@F?~ANv@Fp@JVD`ALF?f@Fd@FZDF@n@F~@LrAJRBpALdC^RB~@NF@TDn@FJBl@FrBTfAJL@f@Bz@Dz@Bv@Bd@BjBDR?T?v@?l@@V?TAVA^?`B@fBC`ADhBDlAHN@hDRR?^D`@Lx@Hl@FH@n@JbABT?p@HF?t@Nd@Fr@FV@f@@r@NzAZDNJz@DTBHNT\\RDBFFBFTh@HZLj@HXRt@DRdA`E\\bAFLHXL\\HXLb@^nA^`BLRHV`@~@d@hAlClHdClGNd@h@xBZpAvAdFh@hB^tA`AtDF\\GxBU~CQ`DG~@Ex@Ex@E|@C~@Bf@FZFRPZHN|@hAPHTHFFZ^MJ_FpD{B`B_Al@RTNId@[j@a@BAJ?DC`BqAb@]\\Un@e@FElAcAb@[rCqBbA}@pB_BtB}A|@o@^Dt@Hd@@hALh@Hd@Lf@Hj@FnALl@Hd@Hd@Ft@JhAPb@Fp@H^D\\Dx@IXBn@Fj@F`AJp@HVBzABn@DX@R@p@FfB^TDNKP}@\\y@r@gAXo@NM"}, "startLocation": {"latLng": {"latitude": 12.9321025, "longitude": 77.7422734}}, "endLocation": {"latLng": {"latitude": 12.858626099999999, "longitude": 77.6553627}}, "steps": [{"distanceMeters": 12, "staticDuration": "4s", "polyline": {"encodedPolyline": "sx|mAe`oyMKP"}, "startLocation": {"latLng": {"latitude": 12.9321025, "longitude": 77.7422734}}, "endLocation": {"latLng": {"latitude": 12.932161299999999, "longitude": 77.7421804}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head northwest toward Varthur - Sarjapur Rd"}, "localizedValues": {"distance": {"text": "12 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 1604, "staticDuration": "323s", "polyline": {"encodedPolyline": "_y|mAs_oyMb@Zn@d@l@`@LHp@h@d@Z|@j@h@`@h@\\fAx@B@LHf@\\PLn@b@VPRN`@Xl@`@PLDBp@d@XRh@^RNf@\\^V\\V~@l@PNJHPJjAn@FB\\NXLd@Tz@`@n@XRJFBLFn@\\ZNx@\\TJPDt@RNBn@DX@V@V?bDDT?rCBL?p@@pA?j@?h@Ar@AfAAtAE"}, "startLocation": {"latLng": {"latitude": 12.932161299999999, "longitude": 77.7421804}}, "endLocation": {"latLng": {"latitude": 12.9194353, "longitude": 77.73650800000001}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left onto Varthur - Sarjapur Rd\nPass by Chrysalis High School, Varthur (on the left)"}, "localizedValues": {"distance": {"text": "1.6 km"}, "staticDuration": {"text": "5 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 2937, "staticDuration": "397s", "polyline": {"encodedPolyline": "oizmAe|myMd@G@FGdCAj@@R@JXp@DJX\\BDBFLZFT@N@FDp@LjBFr@B\\BRDr@JpA@R@JNzBRbC@ZJ|AFt@?T@LBr@HfD@XPvED|@Fl@Ft@Hp@Fl@Lr@DPHTLVBFDHLPRTLLZXFBPLv@\\ZLhA`@h@Vd@XBBHFLl@BZFx@FpADh@P~AAfACZ?HE\\MnAC\\RrAPrA@l@QbBKl@c@rCGpA?b@Pv@b@l@PVb@r@BHNVP^DHVl@HNFHn@bAHLb@l@LLl@x@J^FHXTHHf@VXL`@PNF\\PLFTHNHNJXLx@`@BDFJ@DCNO`@q@vAKRIVCDGLIPSh@Id@Bj@"}, "startLocation": {"latLng": {"latitude": 12.9194353, "longitude": 77.73650800000001}}, "endLocation": {"latLng": {"latitude": 12.9129647, "longitude": 77.7127959}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right onto Gunjur Rd/Gunjur-Doddakannelli Rd\nPass by Sri Maramma Temple (on the left in 2.9 km)"}, "localizedValues": {"distance": {"text": "2.9 km"}, "staticDuration": {"text": "7 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 1417, "staticDuration": "215s", "polyline": {"encodedPolyline": "_aymA_hiyMt@?\\@z@D`BExAE~FMVDlAj@dCtAd@^\\RVHj@Tv@`@\\RXNlAp@LHNFfAh@XN`Ab@^Jj@TnAd@|@ZdC~@tEjBlFjBhBh@LAb@H\\?"}, "startLocation": {"latLng": {"latitude": 12.9129647, "longitude": 77.7127959}}, "endLocation": {"latLng": {"latitude": 12.9012932, "longitude": 77.7084843}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left onto Carmelaram 100 Feet Rd"}, "localizedValues": {"distance": {"text": "1.4 km"}, "staticDuration": {"text": "4 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 93, "staticDuration": "18s", "polyline": {"encodedPolyline": "axvmA_mhyMFSTo@J[Nc@HW"}, "startLocation": {"latLng": {"latitude": 12.9012932, "longitude": 77.7084843}}, "endLocation": {"latLng": {"latitude": 12.9009479, "longitude": 77.7092638}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left onto Ambalipura - Sarjapur Rd/Sarjapur - Marathahalli Rd"}, "localizedValues": {"distance": {"text": "93 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 3746, "staticDuration": "545s", "polyline": {"encodedPolyline": "}uvmA{qhyMLDsBzFEL{AhEMZMXGNABWj@Yr@MXSb@gBbEMXOXO\\OZO\\MXWj@Yt@Sh@[n@GP]n@}@pBYl@q@zA]t@e@hA_ArBq@xAUd@GNIRIRKZGRWz@YnAMp@I`@G\\Kh@O~@Mr@Ov@[xAa@vB[nAIf@Ed@CNE^_@|BIh@AJGh@ShAG^WbBKj@u@zDOt@CPOt@Kj@EZQz@Gb@SfAENQv@IV]nAGR]nAGPEPELEPMf@Y`AEP[l@KXUz@Ut@GPc@tAELM`@M`@Od@CLKZWdA]dA]nAGRy@nC[z@O\\m@zAIRGNEJu@fB_AzBWn@GNOZELoBxESp@q@xBi@hB"}, "startLocation": {"latLng": {"latitude": 12.9009479, "longitude": 77.7092638}}, "endLocation": {"latLng": {"latitude": 12.9139384, "longitude": 77.6776362}}, "navigationInstruction": {"maneuver": "UTURN_RIGHT", "instructions": "Make a U-turn\nPass by State Bank of India ATM (on the left in 800m)"}, "localizedValues": {"distance": {"text": "3.7 km"}, "staticDuration": {"text": "9 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 4386, "staticDuration": "824s", "polyline": {"encodedPolyline": "cgymAglbyMt@BHFF\\Px@HP^Rp@VF@p@J`@H`@Fz@DR?v@B~@BdB@^Ab@Af@LLDr@Xl@TVF`@?TGp@S`@?XHr@T|@^JDd@R|@XTDt@HVBP?P?~@CL?fBAP?L?l@GHA|@[JEf@OZEtAJ\\@r@DVBhADx@Fz@FZBP@p@D~BLtAJb@?TBl@Dp@Dd@B`@BVBf@@J?d@?jAAZ@jA?lC@~A@RB`@Dv@R`AR|EpAl@N|AZlFtAfCr@J@LDL@h@JVB^Dj@HN@D@F?~ANv@Fp@JVD`ALF?f@Fd@FZDF@n@F~@LrAJRBpALdC^RB~@NF@TDn@FJBl@FrBTfAJL@f@Bz@Dz@Bv@Bd@BjBDR?T?v@?l@@V?TAVA^?`B@fBC`ADhBDlAHN@hDRR?^D`@Lx@Hl@FH@n@JbABT?p@HF?t@Nd@Fr@FV@f@@r@NzAZ"}, "startLocation": {"latLng": {"latitude": 12.9139384, "longitude": 77.6776362}}, "endLocation": {"latLng": {"latitude": 12.8757776, "longitude": 77.6713539}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left onto Hosa Rd\nPass by New U Fitness (on the left in 1.1 km)"}, "localizedValues": {"distance": {"text": "4.4 km"}, "staticDuration": {"text": "14 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 2100, "staticDuration": "401s", "polyline": {"encodedPolyline": "sxqmA}dayMDNJz@DTBHNT\\RDBFFBFTh@HZLj@HXRt@DRdA`E\\bAFLHXL\\HXLb@^nA^`BLRHV`@~@d@hAlClHdClGNd@h@xBZpAvAdFh@hB^tA`AtDF\\GxBU~CQ`DG~@Ex@Ex@E|@C~@Bf@FZFRPZHN|@hAPHTH"}, "startLocation": {"latLng": {"latitude": 12.8757776, "longitude": 77.6713539}}, "endLocation": {"latLng": {"latitude": 12.869979599999999, "longitude": 77.65354060000001}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right after <PERSON>eshwara Bakery & Sweets (on the left)\nPass by Apollo Clinic (on the left in 1.4 km)"}, "localizedValues": {"distance": {"text": "2.1 km"}, "staticDuration": {"text": "7 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 327, "staticDuration": "63s", "polyline": {"encodedPolyline": "ktpmAsu}xMFFZ^MJ_FpD{B`B_Al@"}, "startLocation": {"latLng": {"latitude": 12.869979599999999, "longitude": 77.65354060000001}}, "endLocation": {"latLng": {"latitude": 12.8719255, "longitude": 77.651669}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at Hi - Taste Bake ' n' Sweets onto NH 4/NH 44/NH 48"}, "localizedValues": {"distance": {"text": "0.3 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 697, "staticDuration": "126s", "polyline": {"encodedPolyline": "q`qmA}i}xMRTNId@[j@a@BAJ?DC`BqAb@]\\Un@e@FElAcAb@[rCqBbA}@pB_BtB}A|@o@"}, "startLocation": {"latLng": {"latitude": 12.8719255, "longitude": 77.651669}}, "endLocation": {"latLng": {"latitude": 12.8670064, "longitude": 77.65539009999999}}, "navigationInstruction": {"maneuver": "TURN_SHARP_LEFT", "instructions": "<PERSON> left onto Service Rd\nPass by Pillar Number 163 (on the left in 650m)"}, "localizedValues": {"distance": {"text": "0.7 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 860, "staticDuration": "171s", "polyline": {"encodedPolyline": "yapmA<PERSON>~xM^Dt@Hd@@hALh@Hd@Lf@Hj@FnALl@Hd@Hd@Ft@JhAPb@Fp@H^D\\Dx@IXBn@Fj@F`AJp@HVBzABn@DX@R@p@FfB^TD"}, "startLocation": {"latLng": {"latitude": 12.8670064, "longitude": 77.65539009999999}}, "endLocation": {"latLng": {"latitude": 12.8594224, "longitude": 77.6540298}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right onto Chikkathogur Main Rd"}, "localizedValues": {"distance": {"text": "0.9 km"}, "staticDuration": {"text": "3 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 11, "staticDuration": "2s", "polyline": {"encodedPolyline": "krnmAux}xMNK"}, "startLocation": {"latLng": {"latitude": 12.8594224, "longitude": 77.6540298}}, "endLocation": {"latLng": {"latitude": 12.8593426, "longitude": 77.65408819999999}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left onto Soni Rd"}, "localizedValues": {"distance": {"text": "11 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 162, "staticDuration": "31s", "polyline": {"encodedPolyline": "{qnmAay}xMP}@\\y@r@gAXo@NM"}, "startLocation": {"latLng": {"latitude": 12.8593426, "longitude": 77.65408819999999}}, "endLocation": {"latLng": {"latitude": 12.858626099999999, "longitude": 77.6553627}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left\nDestination will be on the left"}, "localizedValues": {"distance": {"text": "0.2 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "18.4 km"}, "duration": {"text": "52 mins"}, "staticDuration": {"text": "52 mins"}}}, {"distanceMeters": 27502, "duration": "2017s", "staticDuration": "2017s", "polyline": {"encodedPolyline": "mmnmA_a~xMOLk@jAk@|@Sf@Q|@OJUEgB_@q@GSAYAo@Ee@AgACEAq@IaAKk@Go@GYCI?o@H}@Kq@Ic@GiAQu@Ke@Ge@Im@IoAMk@Gg@Ie@Mi@IiAMe@Au@I_@EUQIFs@f@eE|CgE~CkA|@MJ[_@LKfFyDvAeAt@i@x@k@d@_@l@c@pByA`CiBzGcF`BmAr@g@~@o@jBsAb@_@bCiBx@k@x@k@|AkAzDwCjDgCxBcBCGJKlGsEKeAGUS[QIa@Gc@BSHGFSTG^@\\X~AFb@Lp@Fb@Hb@DZN`ABRFj@HpBHTIdC_@xDe@tDYjBQnAk@tDI^m@vB[`A]z@gAhDe@tA}HzTaAnCaDbI{@vBm@xAi@jAu@lBk@xAaD|Hk@|Aq@lB_BlFoAdFsAzHa@`DMjAYxCMlBQhEGz@AtD@jAHvDLxBDr@V~Bn@rE\\jBf@zBTx@~AbFd@nAp@~AjBzDfB~CzIjPvClFf@|@lBvCnBnC~ApBvEjG|@nAtBxClAjB~AvCrBxEhC~HNn@j@rCl@|DFb@RlBHfAVbDNnBJ~@\\zBX~A^~ALn@XdBPrAHjALrCB`AD|B@bADfDHzG@v@@j@@v@PjL@x@@f@@j@DvCH~AT`B\\nAl@`B\\r@\\n@\\f@fCpC^`@lAtAvA|Bj@pANb@Nd@FP^dBHh@DZDb@DxA?t@IbDGdCCv@IpBQvHGvBA|@KzDIjDQbHAnDH`CFx@PrBZjBnAnFfBtH|@rEf@`DF\\p@lFDr@XlD^rFNfFDvD?tC?n@ArBKlE[|FYrDKlAy@tG{@pFaA`FADGRCL{@nDiArEs@zC{@xEi@vD[dDc@dFc@~Fi@|Fa@tD}@rF{@zEmA|Go@nDi@rCo@hDs@bEiAbGuAtGKl@oCtMm@rCoCnMkAtFq@dDKd@Op@iBdHe@~Bw@fDm@zB[jAaBjFMb@cChJk@hBi@`Bk@jBmDrKaA~CeAdDiAzD}@|CcCvIiFnQ]dAkA~Ci@jAMVoAdCgApBmApBaBpC{CdFgLpRiJpOcDpF}@xAKNaAtAW\\UVmBbBeAp@yBhAi@Ro@TODmCx@eB\\yEzAeA^mGxBQJa@PiAp@mAbAs@n@{@dACHqA~AcAjB_DfEc@l@yBvCaBzBcI~JwAfBaAjAwCvCiChC]\\m@l@sCpCKJKHQT{@v@cDlDcC|CgEjFIJuDvEc@j@mErFmIfKSVu@`AKJmB`C{D`FkC`D_AbAkAfAyApAo@d@]f@g@lAS|@A^Cl@K^GJEH_@T[Fe@Ce@IOCa@?OB}@Pm@HuATC?w@\\]\\KTCd@DTDNXXRTLJTXDFJNj@q@"}, "startLocation": {"latLng": {"latitude": 12.858626099999999, "longitude": 77.6553627}}, "endLocation": {"latLng": {"latitude": 12.901739899999999, "longitude": 77.4691213}}, "steps": [{"distanceMeters": 162, "staticDuration": "31s", "polyline": {"encodedPolyline": "mmnmA_a~xMOLk@jAk@|@Sf@Q|@"}, "startLocation": {"latLng": {"latitude": 12.858626099999999, "longitude": 77.6553627}}, "endLocation": {"latLng": {"latitude": 12.8593426, "longitude": 77.65408819999999}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head northwest toward Soni Rd"}, "localizedValues": {"distance": {"text": "0.2 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 871, "staticDuration": "174s", "polyline": {"encodedPolyline": "{qnmAay}xMOJUEgB_@q@GSAYAo@Ee@AgACEAq@IaAKk@Go@GYCI?o@H}@Kq@Ic@GiAQu@Ke@Ge@Im@IoAMk@Gg@Ie@Mi@IiAMe@Au@I_@E"}, "startLocation": {"latLng": {"latitude": 12.8593426, "longitude": 77.65408819999999}}, "endLocation": {"latLng": {"latitude": 12.8670064, "longitude": 77.65539009999999}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at the 1st cross street onto Chikkathogur Main Rd"}, "localizedValues": {"distance": {"text": "0.9 km"}, "staticDuration": {"text": "3 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 393, "staticDuration": "50s", "polyline": {"encodedPolyline": "yapmAea~xMUQIFs@f@eE|CgE~CkA|@"}, "startLocation": {"latLng": {"latitude": 12.8670064, "longitude": 77.65539009999999}}, "endLocation": {"latLng": {"latitude": 12.869799200000001, "longitude": 77.6533427}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left onto NH 4/NH 44/NH 48"}, "localizedValues": {"distance": {"text": "0.4 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 1611, "staticDuration": "172s", "polyline": {"encodedPolyline": "gspmAkt}xMMJ[_@LKfFyDvAeAt@i@x@k@d@_@l@c@pByA`CiBzGcF`BmAr@g@~@o@jBsAb@_@bCiBx@k@x@k@|AkAzDwCjDgCxBcB"}, "startLocation": {"latLng": {"latitude": 12.869799200000001, "longitude": 77.6533427}}, "endLocation": {"latLng": {"latitude": 12.8588064, "longitude": 77.662369}}, "navigationInstruction": {"maneuver": "UTURN_RIGHT", "instructions": "Make a U-turn at Hosa Rd/New Mico Rd"}, "localizedValues": {"distance": {"text": "1.6 km"}, "staticDuration": {"text": "3 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 203, "staticDuration": "36s", "polyline": {"encodedPolyline": "qnnmAyl_yMCGJKlGsE"}, "startLocation": {"latLng": {"latitude": 12.8588064, "longitude": 77.662369}}, "endLocation": {"latLng": {"latitude": 12.8574228, "longitude": 77.6635288}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Keep left to continue on Service Rd"}, "localizedValues": {"distance": {"text": "0.2 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 496, "staticDuration": "51s", "polyline": {"encodedPolyline": "{enmAat_yMKeAGUS[QIa@Gc@BSHGFSTG^@\\X~AFb@Lp@Fb@Hb@DZN`ABRFj@HpBHT"}, "startLocation": {"latLng": {"latitude": 12.8574228, "longitude": 77.6635288}}, "endLocation": {"latLng": {"latitude": 12.857726999999999, "longitude": 77.6609539}}, "navigationInstruction": {"maneuver": "RAMP_LEFT", "instructions": "Turn left onto the ramp to NH 48"}, "localizedValues": {"distance": {"text": "0.5 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 23115, "staticDuration": "1378s", "polyline": {"encodedPolyline": "ygnmA}c_yMIdC_@xDe@tDYjBQnAk@tDI^m@vB[`A]z@gAhDe@tA}HzTaAnCaDbI{@vBm@xAi@jAu@lBk@xAaD|Hk@|Aq@lB_BlFoAdFsAzHa@`DMjAYxCMlBQhEGz@AtD@jAHvDLxBDr@V~Bn@rE\\jBf@zBTx@~AbFd@nAp@~AjBzDfB~CzIjPvClFf@|@lBvCnBnC~ApBvEjG|@nAtBxClAjB~AvCrBxEhC~HNn@j@rCl@|DFb@RlBHfAVbDNnBJ~@\\zBX~A^~ALn@XdBPrAHjALrCB`AD|B@bADfDHzG@v@@j@@v@PjL@x@@f@@j@DvCH~AT`B\\nAl@`B\\r@\\n@\\f@fCpC^`@lAtAvA|Bj@pANb@Nd@FP^dBHh@DZDb@DxA?t@IbDGdCCv@IpBQvHGvBA|@KzDIjDQbHAnDH`CFx@PrBZjBnAnFfBtH|@rEf@`DF\\p@lFDr@XlD^rFNfFDvD?tC?n@ArBKlE[|FYrDKlAy@tG{@pFaA`FADGRCL{@nDiArEs@zC{@xEi@vD[dDc@dFc@~Fi@|Fa@tD}@rF{@zEmA|Go@nDi@rCo@hDs@bEiAbGuAtGKl@oCtMm@rCoCnMkAtFq@dDKd@Op@iBdHe@~Bw@fDm@zB[jAaBjFMb@cChJk@hBi@`Bk@jBmDrKaA~CeAdDiAzD}@|CcCvIiFnQ]dAkA~Ci@jAMVoAdCgApBmApBaBpC{CdFgLpRiJpOcDpF}@xAKNaAtAW\\UVmBbBeAp@yBhAi@Ro@TODmCx@eB\\yEzAeA^mGxBQJa@PiAp@mAbAs@n@{@dACHqA~AcAjB_DfEc@l@yBvCaBzBcI~JwAfBaAjAwCvCiChC]\\m@l@sCpCKJKHQT{@v@cDlDcC|CgEjFIJuDvEc@j@mErFmIfKSVu@`AKJmB`C{D`FkC`D_AbAkAfAyApAo@d@"}, "startLocation": {"latLng": {"latitude": 12.857726999999999, "longitude": 77.6609539}}, "endLocation": {"latLng": {"latitude": 12.8993957, "longitude": 77.4721021}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "Continue onto NH 48\nToll road\nDrive along the lake (on the left for 280m)"}, "localizedValues": {"distance": {"text": "23.1 km"}, "staticDuration": {"text": "23 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 614, "staticDuration": "117s", "polyline": {"encodedPolyline": "glvmAsgzwM]f@g@lAS|@A^Cl@K^GJEH_@T[Fe@Ce@IOCa@?OB}@Pm@HuATC?w@\\]\\KTCd@DTDNXXRTLJTXDFJN"}, "startLocation": {"latLng": {"latitude": 12.8993957, "longitude": 77.4721021}}, "endLocation": {"latLng": {"latitude": 12.901962, "longitude": 77.46887029999999}}, "navigationInstruction": {"maneuver": "RAMP_LEFT", "instructions": "Exit onto NH 275"}, "localizedValues": {"distance": {"text": "0.6 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 37, "staticDuration": "8s", "polyline": {"encodedPolyline": "g|vmAmsywMj@q@"}, "startLocation": {"latLng": {"latitude": 12.901962, "longitude": 77.46887029999999}}, "endLocation": {"latLng": {"latitude": 12.901739899999999, "longitude": 77.4691213}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left\nDestination will be on the right"}, "localizedValues": {"distance": {"text": "37 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "27.5 km"}, "duration": {"text": "34 mins"}, "staticDuration": {"text": "34 mins"}}}, {"distanceMeters": 124751, "duration": "6232s", "staticDuration": "6232s", "polyline": {"encodedPolyline": "{zvmA_uywMk@p@~@xAb@t@zAlCtBfE`@t@|AlCT\\v@vAjBzCdAfBPZd@t@jCnE^h@h@t@~@dAXVt@l@\\VrDfC`@XnDdCp@d@LH`Ar@lBvAl@h@j@h@h@n@PXp@jAPXl@hAbBxC~ArCj@~@T\\BBvCdEp@z@\\\\b@b@RRb@^vDtC`D`Ch@^fAv@jHnFXRh@`@jDfCnBhA`Af@~@b@lG|CdAh@dAh@bCrAl@\\lBdA`Af@d@Vf@X`@T|@f@zDtBpAv@^VjA`Ab@^z@x@zBjCjBjCPVX`@`AxAX`@RVjBnCNRb@d@\\^z@n@j@^|Ar@~Aj@hF`B`DbAlDjAxDrAr@ZfAh@VNpCpBZXrB`Bz@p@dDfCj@d@fBrA`@\\XTZTPLj@b@xCzBpCxBzFxEn@d@^Vv@z@v@z@~@bAnCjDRXT^dAlALL~AxArDhC`B~@~@f@dAd@fC`AtAd@~@XnBj@NDxErBhB`ApBdAnAj@rBx@bA`@p@RbBh@bCv@hC~@bElA~Af@zErAdBf@PFXJb@RdA`@j@Z^Tt@d@xC`Cj@n@t@l@|@x@hB~AzClCrAlAbCzBrAnA\\ZhAbAXRZV^VNJ|Ax@fChAvBz@vBz@d@Rl@Tx@Zp@Xp@Xx@\\bBr@zB|@v@XnCp@vB^xCX~AJh@Bt@HnC^hARhARrATfARxCj@|FdAfEr@nAR|@L^DXDhCZbCZfB`@d@NjAb@|Av@`Al@fAx@XV`AbAXZTZh@t@r@rAP^N^j@dBT`A`@hCFpA@v@B|@DlF@x@BrABnA\\fTFfD@XBtBLhJD~BF~B@l@B|BHvENvFRlDP|ADd@Hf@n@~Dp@tC^pA|@nCr@lBpBdEz@~A`ChD`ApAdCnCdGnFzDhDdI`HTTtCfCr@l@jKdJhDfCrA~@fCtAnClAbC`AfElAjDv@`El@xD^fBF\\BhBDrCBnJ@dC@V?hA@tACrGCjC@`@?t@@~@@dA@zC@z@@pA@`FFnADlBRf@Hb@Hp@NpAb@~@^dB|@zAhAv@l@dJnHxApATPzBbC`AvAf@z@fAbC^v@JVXp@zF|MXn@lB`EP^N^hApCXv@HPxBnGvApDf@jA`AhBR\\n@lAjGrKfFjJXh@l@rAn@`B|@hCPh@`AvDv@zDp@rF^|DdAtOb@zGFfAD`@RtBnAjRHvAL~ADb@Db@Z|Dd@tCxAtFf@vAr@tCl@~AVl@^r@BDfA|Ap@x@`BlBhE`Fj@l@|AhB~AbBdD|CpG`G^ZdHrGhEzDlBfBd@`@LLfClCf@p@zAtB~@vAbClDl@x@NVPVhEjGZ^pArAnBzA\\TrHfFdC~AJFzJxGl@`@bAr@zAjAnBfBpBvBhB~B`AxAnBnD~B`G^hA`@~AtA|GX`Bj@bDrAlIxA~Ht@~EjApGj@`DdBxJBJJn@Hd@`ArFxArI|BpMVrBJbBFbCC~COhCOnAYzAk@zBe@`BSt@aAbDM^Ur@iAvDu@fCWz@yGzTUx@g@`BuB`H{@|CM`@wFdRgDvK}CnK{BvHuB`HwDhMcAvD]tB[dCQrCGzBJbDb@fFr@zDrAvEN^|AbDr@hAjBjClBrBXXf@b@nEdDzEdDFB`IvFvBzAnIdGbKdHnDjCfD|B\\V`CtBnBvBzBnCbBlBfAnA~@fAfNfPvRlUdCtCjC`DlCtCx@n@`BjAdEhClEdCjAr@~A`A`CrApDzBlEjC~@j@hLxGnBlArBlAzAz@jI|ElFbDzRlLxQrKxDtBtI`EfF`C|At@rB|@l@ZjHhDpBbA^RhD|AvAn@jAn@VRn@j@tCtCXXdLvKrIjIpDrD`AhAbCpDl@`AT^|@~A^n@T\\pGhKjBtCr@~@pBvB|@v@`@\\dAv@lAp@TNxC`BlKzFXNTLdAl@`C~An@b@j@`@`At@`@\\x@r@jDdD`H~G~A|AdFrF|@dAt@~@|BhCjCrCfCdCh@h@rEvElAlARPrDhDpB`B|AdAz@f@zCnApA`@tCp@dC`@zDh@hARfB^|@N|AXhAZjAZfDlArAp@^RvAx@xA`AdAv@jAbAf@b@j@d@~BrCb@t@vA`CfAzBz@vB~AnEd@rAz@~B~DxKp@xAP^Vb@TZh@p@vAvArBrAfAh@~Af@zAXhBTbGZhCNb@BjBL~Hb@h@Dz@Lz@NjAVjA\\|Ap@~A`Aj@`@l@h@`A`ANNr@|@dAhBJTv@fB\\dA^hBRdB@H^zDDd@XrCVlC`@nEJdANdBBf@JtAPbF@j@BvBDtEFdFFxAJbAj@vCfA~El@dCJ\\^z@rBxFt@lCNt@lBfK\\nCPvCTjNR`D|@hIz@`Id@rCxDpQhArF^vCNlCFtJ@z@FpABl@r@zGv@hGHn@@LFtAZ`DPxAXtBD`@dCpRrApIh@~B|@`E`@`BZfAZ|@d@bAjC~EtHpN|AtCdCrEhHvMvApCh@nAj@hBJ`@\\|BNhCNxGHdBFz@Hz@PvBHr@Jf@Hd@RnAl@tC|@zCXz@hArCdAbC|@hBx@fBf@bAN`@Rb@`ApBbBhDXj@lCdG|B`Fx@xAdAjBJP~A~Bj@v@pCjDNRr@bAVZbCxCJLDFRVjAbBp@|@jAdBrAdCdDjHvA~CR`@R\\dAhBxArB|AbBl@j@jB~Af@`@XTtBhBzAlAj@f@|ApAPLnGnFvRdPtLzJ~DfDfI~GXVzAtAxA|Ax@bA^l@hAfBNXR`@JVh@nAv@vBz@|CNp@nBxJvIdc@Np@vDvRbCvLz@hEt@~DPv@vAjHZ|AxAlHh@|BRr@Xt@\\p@bA`B`DrERXz@lA|CdE`E`GvDbFT\\lDvEV^z@hA\\b@xH|Jt@|@TXTVh@p@pHxI`EzEdD`Ez@nAn@pAl@pA`@jAlB|Gt@rD~@fFJl@PfAd@lCb@|Bx@nCp@jBxBdGzA`F\\rAv@bDl@dCb@lBn@fCfAfDdBjFv@bCXz@hAnDrAfEzAnEdBtEPd@dAnCfAfC@BxExLXt@j@xAfCvGXr@`EtKbB~EzCtIh@tBXrAJd@ZzBXtDBPNnK@h@BpAJ`Bb@lC`@lAh@bBzBvF|JpVRf@vDfJ`FpMdBfEJTL^Xl@jA`DzA~DnA~CRh@bAjCb@jA`BbETh@Th@tF|Mz@pBb@~@rGbOx@jBt@dBrAzCLVZp@lDfHfAtBXf@r@vAbB`DhAvBVf@fDrGtB`EbDfGvAbCnAbCNVzA|CdA~BPb@jAdDjCvHLZlBxFHXJZb@`BNh@fAnFRz@fBnIJr@Ft@B`CQfPE`GGxGEv@Gx@G~AQxA_BpGuAnGWjAKj@M~@SnCEh@KzE@dABfAJ|B^nFLfArAvMLfATxBn@|Fd@hE@NHl@PzBPxDAjD?TCj@GtAYlCQnAc@rCu@~EiAfH]lCOvAIvB@hB?j@JrBLzA|@rFH`@nCvOl@tEDf@TvBPzBTbEHtA~Ar[x@tOdAhSv@vOLvB`AlRDt@VjFJ`D@~DEnDI~BE~@[nEc@hEs@jEKj@}BnKk@fDMfAUdBY|CM|BI|AEhDAxEDpCBp@Bn@FtATlCd@tEn@jEh@nCzA~FnAvDbBfEfCdFrBfDV^^h@fB`C~@jAxC`DpBlB|ChCzDpCZPpC`BrBbAlEjBlBt@`Cv@nBj@lDx@rEx@tC^fAJv@HbBNjJn@lN~@hBRxANxBTd@HxF~@d@HhB\\pHpAh@LnEvA^Nj@\\zBpAjAz@|@z@z@x@Z\\`ApADFtAbCfAxBZx@dA`CBHfBlEDJxB|F`@nAf@|ALZL\\Tn@\\`AdBvFXbAhCtJXjAhApEnAdFdBpGdC|JnBxHxAxENf@Rn@z@nCZ`ANd@N`@^lA~@vClAxDv@dCZ`Av@~BDHHVl@bBj@xAz@rB`@`AP^r@~A\\v@b@hAh@lA\\z@~@xBd@fA`A`CrBzERh@N^HNN^^|@r@~Al@vATf@Td@^z@^|@d@hAt@fBtE`Lp@`Bn@~Ad@dBd@bCNpAJrABx@Dv@DbATrGPlBd@bCZjAr@pB|@jBNVvAbDnA~D^dCPzBJhBR~ERlFJzBHhDLtAHhCNpCZlDBb@r@~H~@lLRxBFt@TjCFl@HdAp@dIHp@RfCNhBDd@XpCXzCBPT`C^pBLv@H\\Nh@f@fBRf@b@lAb@lAh@hAbBfElDtIp@`BjAnCZr@rBdFz@dBtB`DdBrBh@h@rBdBr@f@hCbBz@h@bC`BdAz@p@p@lA~Ab@v@nDhIbEfJ`AtBn@lAnAbCb@x@b@v@FLdAlB^r@h@|@hA|BNTVb@rA`CjG`KrAtBjBrCpCrE`@p@R^RXXb@NTPZ^j@T\\hAjBd@t@v@dAx@fAbAjAxAvArAjApElF\\\\\\\\|@pAlAtBpEdG~B`DvC|DxC|Eh@hAZx@Zt@dBjFp@lBr@dBh@fAn@pAdApB~ClGr@nA`@v@NV`@n@r@jApBdCd@d@jCrBVPp@`@`Bx@|An@dDlA~HrCbCz@zAh@vEnBpAj@dCtAvA~@n@h@x@z@`ApAxAvB|BdDlAtAf@b@bBjAhGzCfAj@~CfBtA~@lAdApAdAZVn@h@@@~DfDTTVT~@|@lB|AZVLHNN~@`ANPt@`Ad@n@n@dApChEn@~@|@xA~AbCpB~Cd@p@~@bB^x@v@`CRz@`@`CbAnFBFf@dBp@`BJTh@z@NVNPVZhB`Br@l@lA~@x@r@LHv@n@vAlAnJxHdBrA|@r@PNLJ`DpCv@n@zEzDjCxBzDdD`@ZbCnBdB`BnCzCd@j@nB`C~@hAXXRVLL|AdB~AjB~DpEhDzDRR`@d@bBjBlCxCfArAfAnARTjCrCvAdBRRnAxAdAnAzBhCVZp@n@RR^^pA|A~AxApA|@\\R`Ab@vGlClBr@`Bj@l@V|@`@rD|A`A^^Nr@X~@ZTHtCnAbCvAh@^^Xv@n@rAvA~A|B|@dBv@lBh@~An@lCNp@XvAv@jEzBrLz@|ELn@lBlKj@nDh@lCLn@R`AXdBZbBfBdJh@rCXdBDX^nBj@~Cv@~DLh@n@dCj@nBp@`B`CzEfCvEP^d@`AvAtCtAfC|A~Cp@jAl@jAn@nAt@|Aj@`Aj@v@`@d@`@^HHt@n@VN`@Vv@^TJ^Lp@P~Bl@`@H`KvBTDtCl@VF`B`@pCh@hBTP@x@DnADb@?h@@lFDlIJxA@|@@^@b@?`CBz@?dDAzAGhCQhD[|@IbBKvDQrC?fA?nBBfBLhBNrBZp@L^F\\HlAZ`AV|Bp@hD~@XHXJh@N|Br@|An@b@Tr@f@z@t@tArAlAtAvBhCl@l@hAx@l@\\`Ah@`@Pr@P`@F^Fx@JxCJfADfCFP?`DJr@BN?N?jBF^Bz@BtEN`BHnADb@@b@@T?nCGhBQd@GjEm@rAKr@CpB?nBHV@|BPzRpAp@@dDLhDLhCFnBNr@Hh@EJGNOPSFKhA{Fd@}A|@qDTmCHu@XeBd@wAjAgDXmAXgDj@yFNiANw@Vy@^u@f@q@DEn@q@lA}AjAiBtCeHtAiDbEmKtGuPTk@nBoElBmEn@uAzCaHfJkQ|CyFrP_Y`DaFbFoHT]JMVa@HM`As@vBoA@AhBkAd@YvAk@dAS|Ew@fAQhAWxCkA`E}A~DeBzCoApKsEb@KbHoAjEy@|IwAdHwA|ASxFYvBIrCKBSvD]jFm@lAMn@GhBOhAK~AOzAO|A[g@SEe@cBH"}, "startLocation": {"latLng": {"latitude": 12.901739899999999, "longitude": 77.4691213}}, "endLocation": {"latLng": {"latitude": 12.309957899999999, "longitude": 76.70291449999999}}, "steps": [{"distanceMeters": 37, "staticDuration": "9s", "polyline": {"encodedPolyline": "{zvmA_uywMk@p@"}, "startLocation": {"latLng": {"latitude": 12.901739899999999, "longitude": 77.4691213}}, "endLocation": {"latLng": {"latitude": 12.901962, "longitude": 77.46887029999999}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head northwest toward NH 275"}, "localizedValues": {"distance": {"text": "37 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 10727, "staticDuration": "686s", "polyline": {"encodedPolyline": "g|vmAmsywM~@xAb@t@zAlCtBfE`@t@|AlCT\\v@vAjBzCdAfBPZd@t@jCnE^h@h@t@~@dAXVt@l@\\VrDfC`@XnDdCp@d@LH`Ar@lBvAl@h@j@h@h@n@PXp@jAPXl@hAbBxC~ArCj@~@T\\BBvCdEp@z@\\\\b@b@RRb@^vDtC`D`Ch@^fAv@jHnFXRh@`@jDfCnBhA`Af@~@b@lG|CdAh@dAh@bCrAl@\\lBdA`Af@d@Vf@X`@T|@f@zDtBpAv@^VjA`Ab@^z@x@zBjCjBjCPVX`@`AxAX`@RVjBnCNRb@d@\\^z@n@j@^|Ar@~Aj@hF`B`DbAlDjAxDrAr@ZfAh@VNpCpBZXrB`Bz@p@dDfCj@d@fBrA`@\\XTZTPLj@b@xCzBpCxBzFxEn@d@^Vv@z@v@z@~@bAnCjDRXT^dAlALL~AxArDhC`B~@~@f@dAd@fC`AtAd@~@XnBj@NDxErBhB`ApBdAnAj@rBx@bA`@p@RbBh@bCv@hC~@bElA~Af@zErAdBf@PFXJb@RdA`@j@Z^Tt@d@xC`Cj@n@t@l@|@x@hB~AzClCrAlAbCzBrAnA\\ZhAbAXRZV^VNJ|Ax@fChAvBz@vBz@d@Rl@Tx@Zp@Xp@Xx@\\bBr@zB|@v@XnCp@vB^xCX~AJh@Bt@HnC^hARhARrATfARxCj@"}, "startLocation": {"latLng": {"latitude": 12.901962, "longitude": 77.46887029999999}}, "endLocation": {"latLng": {"latitude": 12.825829299999999, "longitude": 77.41169459999999}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left onto NH 275\nToll road\nPass by <PERSON><PERSON> (on the left in 7 km)"}, "localizedValues": {"distance": {"text": "10.7 km"}, "staticDuration": {"text": "11 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 37179, "staticDuration": "1658s", "polyline": {"encodedPolyline": "m`hmAannwM|FdAfEr@nAR|@L^DXDhCZbCZfB`@d@NjAb@|Av@`Al@fAx@XV`AbAXZTZh@t@r@rAP^N^j@dBT`A`@hCFpA@v@B|@DlF@x@BrABnA\\fTFfD@XBtBLhJD~BF~B@l@B|BHvENvFRlDP|ADd@Hf@n@~Dp@tC^pA|@nCr@lBpBdEz@~A`ChD`ApAdCnCdGnFzDhDdI`HTTtCfCr@l@jKdJhDfCrA~@fCtAnClAbC`AfElAjDv@`El@xD^fBF\\BhBDrCBnJ@dC@V?hA@tACrGCjC@`@?t@@~@@dA@zC@z@@pA@`FFnADlBRf@Hb@Hp@NpAb@~@^dB|@zAhAv@l@dJnHxApATPzBbC`AvAf@z@fAbC^v@JVXp@zF|MXn@lB`EP^N^hApCXv@HPxBnGvApDf@jA`AhBR\\n@lAjGrKfFjJXh@l@rAn@`B|@hCPh@`AvDv@zDp@rF^|DdAtOb@zGFfAD`@RtBnAjRHvAL~ADb@Db@Z|Dd@tCxAtFf@vAr@tCl@~AVl@^r@BDfA|Ap@x@`BlBhE`Fj@l@|AhB~AbBdD|CpG`G^ZdHrGhEzDlBfBd@`@LLfClCf@p@zAtB~@vAbClDl@x@NVPVhEjGZ^pArAnBzA\\TrHfFdC~AJFzJxGl@`@bAr@zAjAnBfBpBvBhB~B`AxAnBnD~B`G^hA`@~AtA|GX`Bj@bDrAlIxA~Ht@~EjApGj@`DdBxJBJJn@Hd@`ArFxArI|BpMVrBJbBFbCC~COhCOnAYzAk@zBe@`BSt@aAbDM^Ur@iAvDu@fCWz@yGzTUx@g@`BuB`H{@|CM`@wFdRgDvK}CnK{BvHuB`HwDhMcAvD]tB[dCQrCGzBJbDb@fFr@zDrAvEN^|AbDr@hAjBjClBrBXXf@b@nEdDzEdDFB`IvFvBzAnIdGbKdHnDjCfD|B\\V`CtBnBvBzBnCbBlBfAnA~@fAfNfPvRlUdCtCjC`DlCtCx@n@`BjAdEhClEdCjAr@~A`A`CrApDzBlEjC~@j@hLxGnBlArBlAzAz@jI|ElFbDzRlLxQrKxDtBtI`EfF`C|At@rB|@l@ZjHhDpBbA^RhD|AvAn@jAn@VRn@j@tCtCXXdLvKrIjIpDrD`AhAbCpDl@`AT^|@~A^n@T\\pGhKjBtCr@~@pBvB|@v@`@\\dAv@lAp@TNxC`BlKzFXNTLdAl@`C~An@b@j@`@`At@`@\\x@r@jDdD`H~G~A|AdFrF|@dAt@~@|BhCjCrCfCdCh@h@rEvElAlARPrDhDpB`B|AdAz@f@zCnApA`@tCp@dC`@zDh@hARfB^|@N|AXhAZjAZfDlArAp@^RvAx@xA`AdAv@jAbAf@b@j@d@~BrCb@t@vA`CfAzBz@vB~AnEd@rAz@~B~DxKp@xAP^Vb@TZh@p@vAvArBrAfAh@~Af@zAXhBTbGZhCNb@BjBL~Hb@h@Dz@Lz@NjAVjA\\|Ap@~A`Aj@`@l@h@`A`ANNr@|@dAhBJTv@fB\\dA^hBRdB@H^zD"}, "startLocation": {"latLng": {"latitude": 12.825829299999999, "longitude": 77.41169459999999}}, "endLocation": {"latLng": {"latitude": 12.644518999999999, "longitude": 77.1693947}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "Continue onto Bengaluru - <PERSON><PERSON><PERSON>"}, "localizedValues": {"distance": {"text": "37.2 km"}, "staticDuration": {"text": "28 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 25619, "staticDuration": "1170s", "polyline": {"encodedPolyline": "gsdlAuc_vMDd@XrCVlC`@nEJdANdBBf@JtAPbF@j@BvBDtEFdFFxAJbAj@vCfA~El@dCJ\\^z@rBxFt@lCNt@lBfK\\nCPvCTjNR`D|@hIz@`Id@rCxDpQhArF^vCNlCFtJ@z@FpABl@r@zGv@hGHn@@LFtAZ`DPxAXtBD`@dCpRrApIh@~B|@`E`@`BZfAZ|@d@bAjC~EtHpN|AtCdCrEhHvMvApCh@nAj@hBJ`@\\|BNhCNxGHdBFz@Hz@PvBHr@Jf@Hd@RnAl@tC|@zCXz@hArCdAbC|@hBx@fBf@bAN`@Rb@`ApBbBhDXj@lCdG|B`Fx@xAdAjBJP~A~Bj@v@pCjDNRr@bAVZbCxCJLDFRVjAbBp@|@jAdBrAdCdDjHvA~CR`@R\\dAhBxArB|AbBl@j@jB~Af@`@XTtBhBzAlAj@f@|ApAPLnGnFvRdPtLzJ~DfDfI~GXVzAtAxA|Ax@bA^l@hAfBNXR`@JVh@nAv@vBz@|CNp@nBxJvIdc@Np@vDvRbCvLz@hEt@~DPv@vAjHZ|AxAlHh@|BRr@Xt@\\p@bA`B`DrERXz@lA|CdE`E`GvDbFT\\lDvEV^z@hA\\b@xH|Jt@|@TXTVh@p@pHxI`EzEdD`Ez@nAn@pAl@pA`@jAlB|Gt@rD~@fFJl@PfAd@lCb@|Bx@nCp@jBxBdGzA`F\\rAv@bDl@dCb@lBn@fCfAfDdBjFv@bCXz@hAnDrAfEzAnEdBtEPd@dAnCfAfC@BxExLXt@j@xAfCvGXr@`EtKbB~EzCtIh@tBXrAJd@ZzBXtDBPNnK@h@BpAJ`Bb@lC`@lAh@bBzBvF|JpVRf@vDfJ`FpMdBfEJTL^Xl@jA`DzA~DnA~CRh@bAjCb@jA`BbETh@Th@tF|Mz@pBb@~@rGbOx@jBt@dBrAzCLVZp@lDfHfAtBXf@r@vAbB`DhAvBVf@fDrGtB`EbDfGvAbCnAbCNVzA|CdA~BPb@jAdDjCvHLZ"}, "startLocation": {"latLng": {"latitude": 12.644518999999999, "longitude": 77.1693947}}, "endLocation": {"latLng": {"latitude": 12.5479826, "longitude": 76.9607755}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "Continue onto Bengaluru - Mysuru Expy/NH 275\nPass by the gas station (on the left in 5.8 km)"}, "localizedValues": {"distance": {"text": "25.6 km"}, "staticDuration": {"text": "20 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 13460, "staticDuration": "591s", "polyline": {"encodedPolyline": "{wqkA{kvtMlBxFHXJZb@`BNh@fAnFRz@fBnIJr@Ft@B`CQfPE`GGxGEv@Gx@G~AQxA_BpGuAnGWjAKj@M~@SnCEh@KzE@dABfAJ|B^nFLfArAvMLfATxBn@|Fd@hE@NHl@PzBPxDAjD?TCj@GtAYlCQnAc@rCu@~EiAfH]lCOvAIvB@hB?j@JrBLzA|@rFH`@nCvOl@tEDf@TvBPzBTbEHtA~Ar[x@tOdAhSv@vOLvB`AlRDt@VjFJ`D@~DEnDI~BE~@[nEc@hEs@jEKj@}BnKk@fDMfAUdBY|CM|BI|AEhDAxEDpCBp@Bn@FtATlCd@tEn@jEh@nCzA~FnAvDbBfEfCdFrBfDV^^h@fB`C~@jAxC`DpBlB|ChCzDpCZPpC`BrBbAlEjBlBt@`Cv@nBj@lDx@rEx@tC^fAJv@HbBNjJn@lN~@hBRxANxBTd@HxF~@d@HhB\\pHpAh@LnEvA^Nj@\\zBpAjAz@|@z@z@x@Z\\`ApADFtAbCfAxBZx@"}, "startLocation": {"latLng": {"latitude": 12.5479826, "longitude": 76.9607755}}, "endLocation": {"latLng": {"latitude": 12.5157102, "longitude": 76.8571012}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "Continue onto Bengaluru - <PERSON><PERSON><PERSON>/Mandya By-Pass Rd"}, "localizedValues": {"distance": {"text": "13.5 km"}, "staticDuration": {"text": "10 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 30596, "staticDuration": "1502s", "polyline": {"encodedPolyline": "enkkA{cbtMdA`CBHfBlEDJxB|F`@nAf@|ALZL\\Tn@\\`AdBvFXbAhCtJXjAhApEnAdFdBpGdC|JnBxHxAxENf@Rn@z@nCZ`ANd@N`@^lA~@vClAxDv@dCZ`Av@~BDHHVl@bBj@xAz@rB`@`AP^r@~A\\v@b@hAh@lA\\z@~@xBd@fA`A`CrBzERh@N^HNN^^|@r@~Al@vATf@Td@^z@^|@d@hAt@fBtE`Lp@`Bn@~Ad@dBd@bCNpAJrABx@Dv@DbATrGPlBd@bCZjAr@pB|@jBNVvAbDnA~D^dCPzBJhBR~ERlFJzBHhDLtAHhCNpCZlDBb@r@~H~@lLRxBFt@TjCFl@HdAp@dIHp@RfCNhBDd@XpCXzCBPT`C^pBLv@H\\Nh@f@fBRf@b@lAb@lAh@hAbBfElDtIp@`BjAnCZr@rBdFz@dBtB`DdBrBh@h@rBdBr@f@hCbBz@h@bC`BdAz@p@p@lA~Ab@v@nDhIbEfJ`AtBn@lAnAbCb@x@b@v@FLdAlB^r@h@|@hA|BNTVb@rA`CjG`KrAtBjBrCpCrE`@p@R^RXXb@NTPZ^j@T\\hAjBd@t@v@dAx@fAbAjAxAvArAjApElF\\\\\\\\|@pAlAtBpEdG~B`DvC|DxC|Eh@hAZx@Zt@dBjFp@lBr@dBh@fAn@pAdApB~ClGr@nA`@v@NV`@n@r@jApBdCd@d@jCrBVPp@`@`Bx@|An@dDlA~HrCbCz@zAh@vEnBpAj@dCtAvA~@n@h@x@z@`ApAxAvB|BdDlAtAf@b@bBjAhGzCfAj@~CfBtA~@lAdApAdAZVn@h@@@~DfDTTVT~@|@lB|AZVLHNN~@`ANPt@`Ad@n@n@dApChEn@~@|@xA~AbCpB~Cd@p@~@bB^x@v@`CRz@`@`CbAnFBFf@dBp@`BJTh@z@NVNPVZhB`Br@l@lA~@x@r@LHv@n@vAlAnJxHdBrA|@r@PNLJ`DpCv@n@zEzDjCxBzDdD`@ZbCnBdB`BnCzCd@j@nB`C~@hAXXRVLL|AdB~AjB~DpEhDzDRR`@d@bBjBlCxCfArAfAnARTjCrCvAdBRRnAxAdAnAzBhCVZp@n@RR^^pA|A~AxApA|@\\R`Ab@vGlClBr@`Bj@l@V|@`@rD|A`A^^Nr@X~@ZTHtCnAbCvAh@^^Xv@n@rAvA~A|B|@dBv@lBh@~An@lCNp@XvAv@jEzBrLz@|ELn@lBlKj@nDh@lCLn@R`AXdBZbBfBdJh@rCXdBDX^nBj@~Cv@~DLh@n@dCj@nBp@`B`CzEfCvEP^d@`AvAtCtAfC|A~Cp@jAl@jAn@nAt@|Aj@`Aj@v@`@d@`@^HHt@n@VN`@Vv@^TJ^Lp@P~Bl@`@H`KvBTDtCl@VF`B`@pCh@hBTP@x@DnADb@?h@@lFDlIJxA@|@@^@b@?`CBz@?dDAzAGhCQhD[|@IbBKvDQrC?fA?nBBfBLhBNrBZp@L^F\\HlAZ`AV|Bp@hD~@XHXJh@N|Br@|An@b@Tr@f@z@t@tArAlAtAvBhCl@l@hAx@l@\\`Ah@`@Pr@P`@F^Fx@JxCJfADfCFP?`DJr@BN?N?jBF^Bz@BtEN`BHnADb@@b@@T?nCGhBQd@GjEm@rAKr@CpB?nBHV@|BPzRpAp@@dDLhDLhCFnBNr@H"}, "startLocation": {"latLng": {"latitude": 12.5157102, "longitude": 76.8571012}}, "endLocation": {"latLng": {"latitude": 12.3508722, "longitude": 76.66117}}, "navigationInstruction": {"maneuver": "NAME_CHANGE", "instructions": "Continue onto Bengaluru - Mysuru Expy/NH 275\nToll road\nPass by Sri Subramanya Swamy Temple (on the right in 27.9 km)"}, "localizedValues": {"distance": {"text": "30.6 km"}, "staticDuration": {"text": "25 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 6452, "staticDuration": "524s", "polyline": {"encodedPolyline": "}gkjAi{{rMh@EJGNOPSFKhA{Fd@}A|@qDTmCHu@XeBd@wAjAgDXmAXgDj@yFNiANw@Vy@^u@f@q@DEn@q@lA}AjAiBtCeHtAiDbEmKtGuPTk@nBoElBmEn@uAzCaHfJkQ|CyFrP_Y`DaFbFoHT]JMVa@HM`As@vBoA@AhBkAd@YvAk@dAS|Ew@fAQhAWxCkA`E}A~DeBzCoApKsEb@KbHoAjEy@|IwAdHwA|ASxFYvBIrCK"}, "startLocation": {"latLng": {"latitude": 12.3508722, "longitude": 76.66117}}, "endLocation": {"latLng": {"latitude": 12.3142924, "longitude": 76.7016393}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left onto NH-150A\nPass by Al-ameen Car Motors (on the right in 4.2 km)"}, "localizedValues": {"distance": {"text": "6.5 km"}, "staticDuration": {"text": "9 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 11, "staticDuration": "3s", "polyline": {"encodedPolyline": "icdjAgxcsMBS"}, "startLocation": {"latLng": {"latitude": 12.3142924, "longitude": 76.7016393}}, "endLocation": {"latLng": {"latitude": 12.3142712, "longitude": 76.7017403}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left"}, "localizedValues": {"distance": {"text": "11 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 570, "staticDuration": "66s", "polyline": {"encodedPolyline": "ecdjA{xcsMvD]jFm@lAMn@GhBOhAK~AOzAO|A["}, "startLocation": {"latLng": {"latitude": 12.3142712, "longitude": 76.7017403}}, "endLocation": {"latLng": {"latitude": 12.309227199999999, "longitude": 76.7026691}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right"}, "localizedValues": {"distance": {"text": "0.6 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 25, "staticDuration": "5s", "polyline": {"encodedPolyline": "uccjAu~csMg@S"}, "startLocation": {"latLng": {"latitude": 12.309227199999999, "longitude": 76.7026691}}, "endLocation": {"latLng": {"latitude": 12.309431, "longitude": 76.702772}}, "navigationInstruction": {"maneuver": "TURN_SHARP_LEFT", "instructions": "<PERSON> left at M.S.BIKE WORKS"}, "localizedValues": {"distance": {"text": "25 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 20, "staticDuration": "3s", "polyline": {"encodedPolyline": "}dcjAi_dsMEe@"}, "startLocation": {"latLng": {"latitude": 12.309431, "longitude": 76.702772}}, "endLocation": {"latLng": {"latitude": 12.309461599999999, "longitude": 76.70295589999999}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at Karnataka Tea Stall & Hotel"}, "localizedValues": {"distance": {"text": "20 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 55, "staticDuration": "15s", "polyline": {"encodedPolyline": "cecjAo`dsMcBH"}, "startLocation": {"latLng": {"latitude": 12.309461599999999, "longitude": 76.70295589999999}}, "endLocation": {"latLng": {"latitude": 12.309957899999999, "longitude": 76.70291449999999}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left at COFFEE KATTE 1964\nDestination will be on the left"}, "localizedValues": {"distance": {"text": "55 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "125 km"}, "duration": {"text": "1 hour 44 mins"}, "staticDuration": {"text": "1 hour 44 mins"}}}, {"distanceMeters": 62276, "duration": "4284s", "staticDuration": "4284s", "polyline": {"encodedPolyline": "ghcjAe`dsMbBIDd@f@Rt@ShD_AjBa@|AWfAQ|Dm@@HtAUl@G^I^ERE\\C`AIbBUdIeApEo@xAMjBSjFo@vASfEk@bAIdBKnK[vDKb@?L?^A~LHpKLhA?zEB\\ArEa@xEc@\\ErCSZAzDNfL^rDL|@HpDb@|Dd@`FjAx@RbAX|B|@vB`Ab@RdCpA~@j@nA|@@@jBbAtC~AfBjAxCdCdCpC|@`AbCxChCzClCvDhBhD`A`CbFbMpBdFJRzApDdB`EtGjMj@fAh@bAlChFvDjHd@|@t@vARn@bArGh@dDFZ\\xAd@hBpB~GtAvEhAdExAlFDNlDzK`A`DJdAFvADz@A|AKhAMv@g@zBe@`B[l@oEfHq@|@e@l@g@h@aCnC_DlDqDrDQRGJWfAqAfJOxAGv@[pEIdCEp@?FGfA?h@DlDHpDBl@Rh@JJRH|CJrBH\\?bAAlIi@^C\\Cn@Gd@K`@IfA]x@_@vAaAx@s@n@g@HEVS~@e@fAa@pB_@tAMLA\\?J?NAl@ApEXzE^`BHvAAjBQjFu@h@GdBGh@C|BIn@AdCIRAXCz@Cr@A^ApAEp@ALA|AGpAOxCi@XIpHgBfAQ~CUVC^IfBs@t@]JIXSxBcBdAi@r@S`@KdAKt@ChAA|EC~AAjKG~ABfKMlACdAARArACjBCjBE~EE`AEnBITAdAGvJi@`AGnAAdCBD?vBH`FDvA?xI@dEAdB@|A@~@?hE?R?\\?dAG`@Gj@K@?`A]h@K`ASfCk@vGuA`Cm@fBa@zBa@`AMxBQv@Ix@It@EjAI|AM~EMnA@hACzCCp@AX?vGExHEtAEl@ExAWRIfA]rAm@ZMj@Wt@]xBeA^MlA[dDi@vASb@AN?h@@hAJfATb@LPJf@Rl@V|@^h@L`@Bb@?hEMN?NAdBCr@GrBc@dCo@zBq@fCo@hBg@VGx@QdBa@~Ac@fCu@vGcBhFqAfCu@XIZItA_@xDaAhDw@h@OFAbBc@b@KVGfEaApEmAvBi@jA]~@Uf@MrEiAf@MdAYb@MVG\\K|A]f@M|J{Bv@UfCeAh@[\\Y\\WfCcCDEpAwAjBoBbAeAt@u@VWl@m@r@s@JKLMxA_B`AaARSLKJMLOzAsAxA{A`A}@ZUpA}@`@U^SXQt@]rCaAxDqANGrBw@f@On@Sn@OTIb@KhBU@?bAEN?l@?J?rBN~BZb@DPBj@Fb@FdAJt@Dh@@jB?tH[xFSzEITAH?n@AvFGjC?hAHxAZTF\\LXNl@XxAx@lAp@`Az@`@h@JTLX`@tAH\\Nn@n@`Bf@`AZb@p@x@nAhAn@^p@\\z@Xl@F`DD`@@^JRLNPN\\`AlCbDzIZt@TXVPb@P`Cj@p@N~AXvAHpACXCj@I\\Gp@Sf@Q`NiGlAi@`Ai@vCoA|FoC|FmCxJqErAc@^Il@Ij@Cr@?`@BhBPzGn@~@RbBPrBPrATPDDB|@XXLd@Xl@`@l@d@tC|Bx@r@zCxBdAr@@?nBdAVJd@R\\LjA\\b@Lh@LnAVlAP~CTnCHzEFZ?~CLdDThBPjBLD?jDFrD@jAB~AFbBTbANjEhA\\H~A^p@R~A^l@NjA\\p@PZHh@LVFhCn@RDx@Rn@JNBz@@tBQ^Gx@IvJiA|FUlGMTC|FUnEc@@?xDk@nDk@HCxDw@fDaA|Bg@f@OdBa@hDk@pBWnBQfBQhC[jC_@tDc@v@ItE[`AI|AEnAA~A@F?~AFr@FLH`@BTB~BN\\?bBY`Dy@vBi@l@QNEJCPCh@Ox@UREVCREXCPAt@GJ?`ACR?@?~@?jA?\\?`B?`HBlH@z@?b@@`@@F@PAvA@nI@tB@hHDdB@fC@`FAn@?N?rA@lBJjC`@vCf@rEz@fB\\|AXTBfBPZ@`@@`@?hAG|DY|BWbAIjFg@nDc@|Bc@fBc@tAe@jD_AzBg@n@Ep@CrBJdS~AlHj@jGh@h@DfK|@~L`A`E\\x@F~BR`DTxDXxEZV@lG`@~@FZBL@lAFdAFjAF|BJN@d@Bv@DXBZ@^BH?f@BrFZzH^vJf@vETbSbAxFVhAHdBXZFpCx@n@Pd@LfF|ApFfBbWnHt@N|@HdFFtB?~C@rO`@j@BjGPxBA`EOtTm@xO_@fJSrBIdRg@n@@xA?`ADhI`AxBVnEd@`G`@v@@jA?rDKxFQfJWpDKvBEdKc@bBGrDOtHW~@ChBE|FKrJObFIlFMvEM~LSrDMz@A|GGnHGxGCdF?xLIhD?xPIrCD|ADB@|BNj@@pBLb@@vDDtCCb@ApMIjFKnAC|A?RAlEEjIKzOa@bGSt@CL?r@C`@AR?hB@pAFdALx@HdAHrALzCP`ENhDTjEZj@BfADz@FxAHbBJlBLhLn@bBDr@@nBEfCIdCOPArCWrAQnFu@pG{@xDc@bBQhDa@fHs@tDc@hLiAvDa@rFm@`BGjJTjAFfEHzCPn@@tCJtBVt@Td@Nt@ZjAp@pExELNvB`CrGxFxI|HhAbAbAv@^TzAn@f@Nd@Fv@FzCLfDHjBF|GN|FLrDHhHNrIHlOIvCH`DJ`BA`@CdAIvEe@dGm@~I]jCK`AIfBUj@IlIiB`@IdN{CfDm@lC]`BSjIu@xIqAhDi@xAWxAU|A[`De@pEs@~VmD^EdGw@tC[lC_@rAQf@IpDi@bAOdCYrCc@pDe@XEdI_A\\ElBQVGJCdD]bEe@bHo@lEm@tCg@zD{@rDkAnGqB@AtBm@xBq@d@OhIeClEyAf@Q|@]pC_AdDcAzDqAlA_@lAa@f@OdA_@tAa@|Bw@rAa@pA_@RGzAg@fCw@r@UzBu@|Bu@rAa@zBu@xAe@pAc@fCy@RE\\K`@Mn@SNExBq@zAe@dBi@DAbA[b@QrAa@dAa@tB}@z@g@dDgBb@Yv@c@`Ag@ZQXUhAo@bAe@HClAg@jAi@~@a@nAi@p@SRId@Q|@[dCw@pB{@XM"}, "startLocation": {"latLng": {"latitude": 12.309957899999999, "longitude": 76.70291449999999}}, "endLocation": {"latLng": {"latitude": 11.812721400000001, "longitude": 76.6867549}}, "steps": [{"distanceMeters": 55, "staticDuration": "13s", "polyline": {"encodedPolyline": "ghcjAe`dsMbBI"}, "startLocation": {"latLng": {"latitude": 12.309957899999999, "longitude": 76.70291449999999}}, "endLocation": {"latLng": {"latitude": 12.309461599999999, "longitude": 76.70295589999999}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head south"}, "localizedValues": {"distance": {"text": "55 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 20, "staticDuration": "8s", "polyline": {"encodedPolyline": "cecjAo`dsMDd@"}, "startLocation": {"latLng": {"latitude": 12.309461599999999, "longitude": 76.70295589999999}}, "endLocation": {"latLng": {"latitude": 12.309431, "longitude": 76.702772}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right"}, "localizedValues": {"distance": {"text": "20 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 25, "staticDuration": "5s", "polyline": {"encodedPolyline": "}dcjAi_dsMf@R"}, "startLocation": {"latLng": {"latitude": 12.309431, "longitude": 76.702772}}, "endLocation": {"latLng": {"latitude": 12.309227199999999, "longitude": 76.7026691}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left"}, "localizedValues": {"distance": {"text": "25 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 399, "staticDuration": "46s", "polyline": {"encodedPolyline": "uccjAu~csMt@ShD_AjBa@|AWfAQ|Dm@"}, "startLocation": {"latLng": {"latitude": 12.309227199999999, "longitude": 76.7026691}}, "endLocation": {"latLng": {"latitude": 12.3057856, "longitude": 76.703704}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left"}, "localizedValues": {"distance": {"text": "0.4 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 6, "staticDuration": "1s", "polyline": {"encodedPolyline": "enbjAcedsM@H"}, "startLocation": {"latLng": {"latitude": 12.3057856, "longitude": 76.703704}}, "endLocation": {"latLng": {"latitude": 12.3057806, "longitude": 76.7036485}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right toward NH-150A/NH 275K"}, "localizedValues": {"distance": {"text": "6 m"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 9443, "staticDuration": "732s", "polyline": {"encodedPolyline": "cnbjAyddsMtAUl@G^I^ERE\\C`AIbBUdIeApEo@xAMjBSjFo@vASfEk@bAIdBKnK[vDKb@?L?^A~LHpKLhA?zEB\\ArEa@xEc@\\ErCSZAzDNfL^rDL|@HpDb@|Dd@`FjAx@RbAX|B|@vB`Ab@RdCpA~@j@nA|@@@jBbAtC~AfBjAxCdCdCpC|@`AbCxChCzClCvDhBhD`A`CbFbMpBdFJRzApDdB`EtGjMj@fAh@bAlChFvDjHd@|@t@vARn@bArGh@dDFZ\\xAd@hBpB~GtAvEhAdExAlFDNlDzK`A`DJdAFvADz@A|AKhAMv@g@zBe@`B[l@oEfHq@|@e@l@g@h@aCnC_DlDqDrDQRGJWfAqAfJOxAGv@[pEIdCEp@?FGfA?h@DlDHpDBl@"}, "startLocation": {"latLng": {"latitude": 12.3057806, "longitude": 76.7036485}}, "endLocation": {"latLng": {"latitude": 12.2618075, "longitude": 76.657258}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left onto NH-150A/NH 275K"}, "localizedValues": {"distance": {"text": "9.4 km"}, "staticDuration": {"text": "12 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 52328, "staticDuration": "3479s", "polyline": {"encodedPolyline": "i{yiA{b{rMRh@JJRH|CJrBH\\?bAAlIi@^C\\Cn@Gd@K`@IfA]x@_@vAaAx@s@n@g@HEVS~@e@fAa@pB_@tAMLA\\?J?NAl@ApEXzE^`BHvAAjBQjFu@h@GdBGh@C|BIn@AdCIRAXCz@Cr@A^ApAEp@ALA|AGpAOxCi@XIpHgBfAQ~CUVC^IfBs@t@]JIXSxBcBdAi@r@S`@KdAKt@ChAA|EC~AAjKG~ABfKMlACdAARArACjBCjBE~EE`AEnBITAdAGvJi@`AGnAAdCBD?vBH`FDvA?xI@dEAdB@|A@~@?hE?R?\\?dAG`@Gj@K@?`A]h@K`ASfCk@vGuA`Cm@fBa@zBa@`AMxBQv@Ix@It@EjAI|AM~EMnA@hACzCCp@AX?vGExHEtAEl@ExAWRIfA]rAm@ZMj@Wt@]xBeA^MlA[dDi@vASb@AN?h@@hAJfATb@LPJf@Rl@V|@^h@L`@Bb@?hEMN?NAdBCr@GrBc@dCo@zBq@fCo@hBg@VGx@QdBa@~Ac@fCu@vGcBhFqAfCu@XIZItA_@xDaAhDw@h@OFAbBc@b@KVGfEaApEmAvBi@jA]~@Uf@MrEiAf@MdAYb@MVG\\K|A]f@M|J{Bv@UfCeAh@[\\Y\\WfCcCDEpAwAjBoBbAeAt@u@VWl@m@r@s@JKLMxA_B`AaARSLKJMLOzAsAxA{A`A}@ZUpA}@`@U^SXQt@]rCaAxDqANGrBw@f@On@Sn@OTIb@KhBU@?bAEN?l@?J?rBN~BZb@DPBj@Fb@FdAJt@Dh@@jB?tH[xFSzEITAH?n@AvFGjC?hAHxAZTF\\LXNl@XxAx@lAp@`Az@`@h@JTLX`@tAH\\Nn@n@`Bf@`AZb@p@x@nAhAn@^p@\\z@Xl@F`DD`@@^JRLNPN\\`AlCbDzIZt@TXVPb@P`Cj@p@N~AXvAHpACXCj@I\\Gp@Sf@Q`NiGlAi@`Ai@vCoA|FoC|FmCxJqErAc@^Il@Ij@Cr@?`@BhBPzGn@~@RbBPrBPrATPDDB|@XXLd@Xl@`@l@d@tC|Bx@r@zCxBdAr@@?nBdAVJd@R\\LjA\\b@Lh@LnAVlAP~CTnCHzEFZ?~CLdDThBPjBLD?jDFrD@jAB~AFbBTbANjEhA\\H~A^p@R~A^l@NjA\\p@PZHh@LVFhCn@RDx@Rn@JNBz@@tBQ^Gx@IvJiA|FUlGMTC|FUnEc@@?xDk@nDk@HCxDw@fDaA|Bg@f@OdBa@hDk@pBWnBQfBQhC[jC_@tDc@v@ItE[`AI|AEnAA~A@F?~AFr@FLH`@BTB~BN\\?bBY`Dy@vBi@l@QNEJCPCh@Ox@UREVCREXCPAt@GJ?`ACR?@?~@?jA?\\?`B?`HBlH@z@?b@@`@@F@PAvA@nI@tB@hHDdB@fC@`FAn@?N?rA@lBJjC`@vCf@rEz@fB\\|AXTBfBPZ@`@@`@?hAG|DY|BWbAIjFg@nDc@|Bc@fBc@tAe@jD_AzBg@n@Ep@CrBJdS~AlHj@jGh@h@DfK|@~L`A`E\\x@F~BR`DTxDXxEZV@lG`@~@FZBL@lAFdAFjAF|BJN@d@Bv@DXBZ@^BH?f@BrFZzH^vJf@vETbSbAxFVhAHdBXZFpCx@n@Pd@LfF|ApFfBbWnHt@N|@HdFFtB?~C@rO`@j@BjGPxBA`EOtTm@xO_@fJSrBIdRg@n@@xA?`ADhI`AxBVnEd@`G`@v@@jA?rDKxFQfJWpDKvBEdKc@bBGrDOtHW~@ChBE|FKrJObFIlFMvEM~LSrDMz@A|GGnHGxGCdF?xLIhD?xPIrCD|ADB@|BNj@@pBLb@@vDDtCCb@ApMIjFKnAC|A?RAlEEjIKzOa@bGSt@CL?r@C`@AR?hB@pAFdALx@HdAHrALzCP`ENhDTjEZj@BfADz@FxAHbBJlBLhLn@bBDr@@nBEfCIdCOPArCWrAQnFu@pG{@xDc@bBQhDa@fHs@tDc@hLiAvDa@rFm@`BGjJTjAFfEHzCPn@@tCJtBVt@Td@Nt@ZjAp@pExELNvB`CrGxFxI|HhAbAbAv@^TzAn@f@Nd@Fv@FzCLfDHjBF|GN|FLrDHhHNrIHlOIvCH`DJ`BA`@CdAIvEe@dGm@~I]jCK`AIfBUj@IlIiB`@IdN{CfDm@lC]`BSjIu@xIqAhDi@xAWxAU|A[`De@pEs@~VmD^EdGw@tC[lC_@rAQf@IpDi@bAOdCYrCc@pDe@XEdI_A\\ElBQVGJCdD]bEe@bHo@lEm@tCg@zD{@rDkAnGqB@AtBm@xBq@d@OhIeClEyAf@Q|@]pC_AdDcAzDqAlA_@lAa@f@OdA_@tAa@|Bw@rAa@pA_@RGzAg@fCw@r@UzBu@|Bu@rAa@zBu@xAe@pAc@fCy@RE\\K`@Mn@SNExBq@zAe@dBi@DAbA[b@QrAa@dAa@tB}@z@g@dDgBb@Yv@c@`Ag@ZQXUhAo@bAe@HClAg@jAi@~@a@nAi@p@SRId@Q|@[dCw@pB{@XM"}, "startLocation": {"latLng": {"latitude": 12.2618075, "longitude": 76.657258}}, "endLocation": {"latLng": {"latitude": 11.812721400000001, "longitude": 76.6867549}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left onto NH-150A/NH 766\nContinue to follow NH 766\nToll road\nPass by the lake (on the right in 1 km)\nDestination will be on the left"}, "localizedValues": {"distance": {"text": "52.3 km"}, "staticDuration": {"text": "58 mins"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "62.3 km"}, "duration": {"text": "1 hour 11 mins"}, "staticDuration": {"text": "1 hour 11 mins"}}}, {"distanceMeters": 90900, "duration": "8796s", "staticDuration": "8796s", "polyline": {"encodedPolyline": "odbgAe{`sMdD{ABAdAm@nAk@NI|@e@t@[XGfACjCCzC?hDAJ?~@AbB?|EEN?rD?R?@P@n@DrBH~CHfADjBLlBHfABXLjBNpD@l@EXBlADhB@n@JzE@h@FlE@ZB|ALhEZzD^fCPbAXhAt@|B\\`AvFnLh@jA|B`FdAbCbArCvBnHlAnEXnAVjBBd@Bb@CdBOlCSpBWlEErB?~@BbA^`KNnG@dAHtC@h@?rAElAAPCdCAp@L|A@@|AlHFZZjAhAzD\\bBx@~E\\zBj@pDh@fC\\bAh@bAVZBBj@l@~@p@tAp@`Br@jAv@l@h@r@~@fBrDx@`B|@dB^|@\\dAPfAFdA?fACbDQ|I?l@Bz@PlAXdAd@`Ab@d@^\\ZVrA|@vBnATPPJbAj@\\Rb@XnA|@|@t@fDpDnGdHjArAd@|@x@vB|BfGhDrIN^bDhI\\v@fCzG~BjHbBtFb@|AnD~M`@`BFX`@nBPlARdDBlDDHApEGvD@`BB`BGd@BtEF`AVvBXfCbBtLb@rDJ`DHzELtFB|@HnEHpLBjE@`@AlDKlDMpA_@pCCNk@zECRCNa@vEG|@I~ACp@IdD@vAAp@@f@D`@^|FNpBZhDVtBThBZfCBtBGx@Eh@sA~Jc@xCKlAG|@ATCZAb@@~A@|@JhGBzD?|CCpAIhG?v@D`BT|B`@fB\\tAb@dB`@|APl@|@jDl@xBz@fFn@bFBZp@zHh@rFh@jG|AjMRrBH`B^jJHrCDf@Db@BPJj@Rn@Z~@r@xBjAnDjA~CjApBd@n@nAnAtAdAlBlA`BlAt@`Az@tA|AvCJRfAjBpDbFdAfBdAnCHXf@dBHd@Cx@QrBo@nFQdBE|@AVGxDD`AJj@f@bCd@|CV~@^hAx@pClA~E\\`Bt@jG|@lHH\\^p@d@t@\\p@bB`E`@|ADb@?`@KvEDdAPpBF`ADrBGnBWbCMpA?fAJv@n@zB`@nCHz@`A~HPzBBxA?j@Lb@b@|@Zd@j@h@TLjCt@vCn@`@Bz@MhACpAFx@Hb@NNH`@f@bBfDrA~CjDrHdDnGj@`AfDfFvAjCdDfFr@hAvAdCl@fBlDtJpC|HnCdH\\lAH~BB|A@VPn@dAdB`ApB|@fCx@rB`BbDh@fA\\bAjA~EHj@?bBE~CB~AFv@DNRt@fAdDl@nAt@rA~@lAtB`DVh@Nn@@J?xAm@jIIf@Kf@m@jBGTYjAW~A@b@DPHPNR~@l@t@d@l@p@N^L|@NnBJ`@HLTZRNnAt@LPP^T~@Rx@n@~C^pEj@~PVjM?~@B|BLt@^bAtApD^jAlAvF\\`Aj@fAFHhCrDV\\b@ZxAr@NDb@DjAGbCO`@?r@Ft@Tj@TlC~A`@f@N^FXPp@r@pEHRRX^NxC`AXVb@j@b@v@L^HX?BHhAEn@GN[b@y@|@ILGb@Bb@DNP\\HL`@`@d@Pj@FbBDVB`@HvBx@nAZnDd@d@JPF\\^XZ`DtDd@\\r@d@`@X`@\\RXfAvBXt@r@hERrCJzCE`AW`Ay@vBgB|Dc@nAMv@AhABz@Fv@p@`E^vBJ\\^j@nAhAZVtDrBdBdAlB`B`@h@HLh@x@z@lBDXLhBh@tM@b@Af@IzBSxBgA|FWfBSlBy@rEgAxEO~@e@xDa@jEi@tCo@dCCNEX@b@Nt@l@jBLr@B\\Ep@a@xASx@IbB@p@J\\LRLNPN|Ax@~An@^HhADnCHtA@nD?zAN`@Fx@Lh@PXLt@p@\\n@`@pAf@bCh@vBRf@p@jAhApAt@dAPZLf@\\bCPt@n@zBLr@VhCLr@Nr@Lh@PPNJ\\NXDdADpA?p@Df@RzA`APPTPNRNTlBfELPBBh@f@jAj@t@R|Cf@THZN^VVVbBpCrDbFT^tAvBX\\rAhAbDlC?@lClBnAbAr@x@j@x@v@|AVf@Xd@fAhAvA`AlD~B~AbAtB~A`Ax@n@p@\\f@HLNf@Pn@PdAv@rENbAJXn@fA|AjBrC`DDFrAxAb@h@Pf@@R]zBQlBANGrAFr@DTnAjDbCtGFZHb@Dd@HjEBv@H|CDn@Hl@Vz@Zf@NRx@z@pAbB\\j@z@rAhAvAt@|@dEfEdBdB~B~BHFp@b@b@X|@^b@P|Bl@fB\\dANz@Dn@?dGG`@@h@Db@Jn@XtCzAvDhCrCjBd@PZFnBL`AL^NTH~@n@b@Zt@^|Aj@dFfATHh@TXRHJNRb@p@nAhBd@d@jCrBvJ`Kn@r@bBnB\\h@f@rA|@`D`AhDpAlEfBdFbDbIpB`F\\p@|@jB^|@b@`AFVd@lAn@zBb@rAn@vAj@v@VVrA|@HDf@p@FT`@bANn@RzADb@Hr@FdAFjBN`GBz@Db@Nr@f@`BPd@PV`@b@bBrC`A`ALR~@x@rAbAj@\\dAd@x@\\b@TJBd@EPGtC}@lAUb@CbADb@DhAH\\Jd@\\`@p@HRJj@@ZAVCVqAjGc@~ASd@e@h@KLeB`B_@^KLw@v@Yb@KXGnABx@Nn@^t@|@hAb@\\r@j@jBhAbAx@l@n@vB~Cj@~@|@rBR`ADn@JtC?@CfDA^cAxNG~@s@zKWtDAd@@b@L`AXpAN^hAvC\\`@XVzBlBFJxArBb@p@dCdFd@d@`A~@XRh@\\~@l@^Xh@z@p@zAv@dCZhAx@fDHb@F`@z@nFVhCFvB?^@rBRdGBdADpAPjAb@`CnArGf@zBNfARtA@b@AdAE~ABfBHh@^t@b@f@RXRRb@r@Vb@Nv@B~AC|AUtA_@rBEbCAzBMdCOpC?BAv@HzAv@bGDZHj@J`@Nh@d@xANd@Lz@Cl@On@INIHMHcA`@[PYN_@Xe@`@c@h@Sp@Kb@SdAQfASn@Ob@Yf@e@^_Ad@oCfAe@PeA\\mCv@oAd@mBh@E@yAn@WNMJW\\Yd@Q\\Wf@[r@M^ELYvAMzAA\\AXA|@CvBAXc@zBKh@Ab@S|ACHO\\MXGLc@z@Wd@Y`@{@`AoAlAcAdAqAlAOPuA`Ak@\\iAx@i@f@KL_@h@o@t@eBfB{@x@_@`@o@t@OPYh@MVqAtCc@lAOx@Kx@Gp@OvCCpA@bAFz@X`DD`@RbALl@PfAVjBPpAHl@N`DJjBFhABf@D|AAb@Mh@Y|@O^O`@Uv@Mz@M|@En@UlAIVKhACh@FdAFZBXXfAf@pBJVz@bCR`@l@dBP`AD\\FlC?t@Br@NdAd@hD?N@RBbALj@v@~BH^Ln@XfBTxA`@bBFNPfA@jACPMdAIz@CPBXL\\dCjDZh@^p@Z\\v@h@^TJPDHHRR\\RXRV`@^h@\\l@PfBVB@RJPJLLTXXp@l@hBVt@b@rAH^b@fAX^j@^\\NjAXf@NLHHHX`@Nf@BFBxA?JCz@CfACz@CXSv@_AxBm@xAc@pA_@hAEJo@~AYj@S`@e@|AMjAAZ?FBf@RbBZbDBp@B~@@`@Bb@BTFb@lAzDZtAD`@b@xCTfAF\\Rt@f@dAd@l@PPRLt@\\~@P~@HlCRr@Pz@Nd@P^LNZ\\h@DVAr@ADQbAYtACV?^F~@DXB\\JfADZXjAl@zAHPXh@Rf@R~@Pz@`@pA?XL@`@B`AHbGt@ZBAd@Gf@_@rAGTuAnE]lAe@hBSXq@l@rBzHz@tBfA~BXf@V\\d@b@TPJHt@h@hA`ALFbCxATVd@n@FNHVDVH^Ff@JhBBb@Bl@?PAXKx@Y~@Up@M\\k@xAAFIRKTm@dAKPGHmBtCS\\eAxBi@lAOn@EpABpCC`@u@zDOr@?DMf@e@fAi@fAEL_@hAYrAGd@Gb@Gv@C~@ALDjC?d@?P@~D@VHfCBzAJt@Nd@f@nADHNh@DVBn@@f@@Z?BLvA`@rB\\hA`AjBFTDj@KzBFj@Rp@T`@\\^\\Tf@VxAr@bAb@|@f@DBJHZ\\v@lAj@`@b@Nl@Nf@J~AXt@Xf@`@~A~AbBlAHFJLJLPh@Jf@D^`B|DFl@BZ@z@@JFj@j@|AT|@NhARlAFf@@\\M|BC`AIr@a@tDIlB?zAHhBDv@p@_@h@a@b@W`@Kd@EZ?X@\\HZBfDQTB`@LNNFR?B@`@EvBBLRZ\\Jn@Jd@XRLJDLAJEHONe@j@oBPc@Ta@NSNK\\QzAa@p@UjBgA\\WlCkBZUf@]RERBLLfAlBr@hAfAbBDHbAz@hAv@f@VDD^VNZFXBd@Ar@E\\@`@Rf@|@zA|@l@^Zj@n@l@ZRF`@D`@DnCFlBZZBPEHCn@{@XUb@O`@Qh@YJKXYVg@Vy@Pq@J_APG|@Lr@Pz@^p@zAM|@CN?h@HbANvADVNPTFv@A~@Gx@GbAE`@DZHlBv@`@JPDZBTCx@_@n@_@PQAWOKQ?SFe@XY@UGWg@C[AMFc@p@]PIZMRAJ?X@bAd@XHDCDGBKCMSc@BYDGH?HFD\\XTB?Z@b@Ol@Wp@WNEREf@AZIBYYOaAOk@M]MUYPa@ZDXNNP`@DTAPChAm@b@KrAEVBF@JDTJ~AjA\\V\\|@DJHZTd@h@n@rAz@vA~@rCpBbBhA^Rd@^^b@HLP^b@dAb@`BZn@LRNN\\NRBl@@v@ElABv@Fn@?f@GZAfAU@AdBo@XKdA_@d@ORGNELEr@MdBMZ?x@QVGDAf@Ij@Kp@Kt@@|ALtAJxDJX?n@?v@BpB@VPlBPXEh@Ox@ORGrCy@l@KZEp@Eh@?nD`@zAPnBJ`CCl@Fp@TfAb@rAb@LDjAd@|A`@pBRFBhAj@PJ`@`@X^@@ZlALb@^lAPXf@l@@@t@dABDr@|@^Vl@?NEjBu@JGl@Y|@YTC@?d@EVI`BeAb@[xC{BhAq@n@m@TJjDlEpB|BDRC\\EV[x@GLg@z@yAfCYZmDxBY\\WZUZ_AxAEd@Bh@_@l@CHCJDLHRp@b@LFp@Pl@JTBDRp@n@f@`@\\n@d@n@PLx@n@RZZd@^x@FTFZFl@BdAAd@MbAIZa@~@?VBDXV\\R~@^x@TLFVVv@zAd@dBJNNRN^j@vBn@xAVn@^l@TZZ`@t@`@FFBJNXJLFNPtABNv@pBjA|AX^TXJXCTGPi@`BKn@GNGZSp@E`AG|@EZGLa@Vk@LgAAg@@w@Na@LEBC@KJIJ?f@DL@D\\\\FDdAv@nAl@j@`@PXJRh@~@TXNX\\`@@@`ArAn@|@jA|A\\b@NTZPHDTBt@H`Ab@j@\\l@VrA|@KTmCdDGR?P?TGJ_@Vk@tAWr@Yf@EDSPQHkAb@KF_Az@cCvCCDc@t@_ApALHdAh@dAf@t@Rt@BB?r@BZFt@TJD|@b@JJDL@LMxAOzD@XBPNV~@fALTBBFNDPLfAPdATtAVzAFLPL@?T?~BYf@K\\Cv@MfBYb@Av@FLFr@p@JHZVz@j@vAh@F@|Al@b@NhA^|@b@jAx@b@b@RPTD|@ERAN@RJXVZb@\\ZVZd@^ZLVBhAPxA^f@ZRLl@TnATPFP@b@AbAAFA\\Av@Bl@P|@j@NFLLX^DPDd@LbBBPFRDHFBPH|@R\\NRRVTf@dAPl@Av@KVEVKRQXOHEVAJ@DLZ|@xAF^AH[j@IR@N\\t@FXPPPR\\V\\\\NJLZ?@^fB?BJXXr@Np@v@hB?@^hAJb@BF^dCBT@p@FrFJvFD|ATlDF|@@ZF\\@BJ^@HRZdAbAFH@NCZC|@EVId@IXY^UNEJ?TJLh@hABHCv@OrCA`ACRAJBFVDZVDbAAdAGt@?Fw@hC[|@b@v@x@^VHR@xBM^BR\\Rf@?JFTAPDNLTd@f@?JRxAFN@BX\\d@`@hAj@XXDJ`@|@`@b@TTVPLRNh@F^H\\j@pAFLd@dAXZjA^ZJz@d@`BfANH~@b@HDbA^THBF?BV~@v@pAFHBH?NAFa@NERD^DJTl@Jt@JjAEp@@D@F`@Xb@^JNL^Np@n@nAv@MT@`@Dz@EFF\\bCPb@\\j@RNDBVLB@ZFjCC`@Bd@HNHL@H@TKZQNGZE~@G`@QT[\\i@JMJG|@]r@a@f@{@XWJMHSd@kAZa@TG\\?nBf@fAXNDR?XAnAIf@AF@j@Gl@M@?ZE\\D@?nA`@dAj@r@j@h@j@XTv@PJB^AnASfA]XSHQJaAFOJQRINARDHBhAxAJDJ?FIEeA?WN]DCNKXC`@FVFr@b@XH\\FV?TGPMFQL}@HKVQFAh@Gv@AJ@NNPn@JRNFd@@ZDXPR@f@?pAZPH`@j@d@RNNFFR^F`@?v@DJCPKh@ABSt@Ob@KZ?VBJL^NPl@j@LLPPP`@Pn@TVZRTJ`AVh@X`@`@R^DH`@p@h@t@T\\lAxBHPJN`@z@TdALp@Bd@B|@?D?LEb@[jBCn@?BF\\L\\^n@v@pAZr@FV@@H`@@H?n@?@E\\G~@?lAOjBCRADINQNIDE@_@PUFA@aB^}@VWNUXa@v@t@SZCh@A^DNDJNF\\CTIHe@d@MXIV?\\DTTd@?JEXB^HJVJv@P`@R\\p@DR?bABb@@PETs@zAYj@QdAWf@[ZP`BHZd@x@^vAVv@HLz@\\LJJRDPA^@THPX^BDT`@\\VTJb@DNFHPLZFL@NEr@@LFJPJHLBt@DX\\z@?dAUTMHa@`AELDZd@v@FZKVOb@UTc@RGL?LHNHp@@LFn@j@r@PNXJHJP^\\FH?d@GH@PVHRt@x@FTCJKFQDKJAPNZTh@DV?n@FN^FPJDBRNJXZjANv@M@[EO@KPQ|@EpA@NJj@?ZYjCAFDLZ^b@RPAPK^c@VMVCv@@n@HJHAJi@BQFMH]NMLOVM`@e@ZKRARIV[T[PKR?N?@Rl@HTHLJp@?l@_@`@k@|@_@LM?YSu@k@QFUTKPEZET?b@@r@I\\Kn@CLQb@_@^QXGXe@|@[^]b@m@hB_@HYBaABWFKR?NFf@Gf@BL\\b@J?@K[WEI?QTo@VGNGb@CJ@@JGFUD[PKLBf@JHHJDL?PS|@Mx@C^Pr@ExAWbAA^Bb@LhA?ZEl@Qv@?f@Ln@?TUz@o@Z_@JyAfASHYHEBAFDHnDkA`@GB?AHGDYFWHs@d@U^GRTl@NVRN`A^f@b@LHH@RKDOHKTMNAL@^DdA^HJ^l@DP?RKPOFeARYLY\\EHUP_@V[LOP@r@IRQLER@XDH?TSBMAe@]QAODINKp@Sz@MjAUj@U^i@p@ARBZ?f@Wz@c@jA]b@cATQNCLDVNLl@LTNn@nAHNC^If@QZCVLp@Cr@KR?VFXLVJ^DZ?b@Kd@@j@Jb@PT@HEN]@MF?NTRC^WP@XBFj@bB?j@KPMh@A\\L^\\^FHJl@DFNR`ATNFKh@SHoANgAAYCMASEIBALJL\\XL\\H\\PXhBr@\\Pj@DVCb@FF@HJ"}, "startLocation": {"latLng": {"latitude": 11.812721400000001, "longitude": 76.6867549}}, "endLocation": {"latLng": {"latitude": 11.5532866, "longitude": 76.0879516}}, "steps": [{"distanceMeters": 933, "staticDuration": "135s", "polyline": {"encodedPolyline": "odbgAe{`sMdD{ABAdAm@nAk@NI|@e@t@[XGfACjCCzC?hDAJ?~@AbB?|EEN?rD?"}, "startLocation": {"latLng": {"latitude": 11.812721400000001, "longitude": 76.6867549}}, "endLocation": {"latLng": {"latitude": 11.8046685, "longitude": 76.6881757}}, "navigationInstruction": {"maneuver": "DEPART", "instructions": "Head southeast on NH 766\nPass by the pharmacy (on the left)"}, "localizedValues": {"distance": {"text": "0.9 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 55650, "staticDuration": "4530s", "polyline": {"encodedPolyline": "er`gAcdasMR?@P@n@DrBH~CHfADjBLlBHfABXLjBNpD@l@EXBlADhB@n@JzE@h@FlE@ZB|ALhEZzD^fCPbAXhAt@|B\\`AvFnLh@jA|B`FdAbCbArCvBnHlAnEXnAVjBBd@Bb@CdBOlCSpBWlEErB?~@BbA^`KNnG@dAHtC@h@?rAElAAPCdCAp@L|A@@|AlHFZZjAhAzD\\bBx@~E\\zBj@pDh@fC\\bAh@bAVZBBj@l@~@p@tAp@`Br@jAv@l@h@r@~@fBrDx@`B|@dB^|@\\dAPfAFdA?fACbDQ|I?l@Bz@PlAXdAd@`Ab@d@^\\ZVrA|@vBnATPPJbAj@\\Rb@XnA|@|@t@fDpDnGdHjArAd@|@x@vB|BfGhDrIN^bDhI\\v@fCzG~BjHbBtFb@|AnD~M`@`BFX`@nBPlARdDBlDDHApEGvD@`BB`BGd@BtEF`AVvBXfCbBtLb@rDJ`DHzELtFB|@HnEHpLBjE@`@AlDKlDMpA_@pCCNk@zECRCNa@vEG|@I~ACp@IdD@vAAp@@f@D`@^|FNpBZhDVtBThBZfCBtBGx@Eh@sA~Jc@xCKlAG|@ATCZAb@@~A@|@JhGBzD?|CCpAIhG?v@D`BT|B`@fB\\tAb@dB`@|APl@|@jDl@xBz@fFn@bFBZp@zHh@rFh@jG|AjMRrBH`B^jJHrCDf@Db@BPJj@Rn@Z~@r@xBjAnDjA~CjApBd@n@nAnAtAdAlBlA`BlAt@`Az@tA|AvCJRfAjBpDbFdAfBdAnCHXf@dBHd@Cx@QrBo@nFQdBE|@AVGxDD`AJj@f@bCd@|CV~@^hAx@pClA~E\\`Bt@jG|@lHH\\^p@d@t@\\p@bB`E`@|ADb@?`@KvEDdAPpBF`ADrBGnBWbCMpA?fAJv@n@zB`@nCHz@`A~HPzBBxA?j@Lb@b@|@Zd@j@h@TLjCt@vCn@`@Bz@MhACpAFx@Hb@NNH`@f@bBfDrA~CjDrHdDnGj@`AfDfFvAjCdDfFr@hAvAdCl@fBlDtJpC|HnCdH\\lAH~BB|A@VPn@dAdB`ApB|@fCx@rB`BbDh@fA\\bAjA~EHj@?bBE~CB~AFv@DNRt@fAdDl@nAt@rA~@lAtB`DVh@Nn@@J?xAm@jIIf@Kf@m@jBGTYjAW~A@b@DPHPNR~@l@t@d@l@p@N^L|@NnBJ`@HLTZRNnAt@LPP^T~@Rx@n@~C^pEj@~PVjM?~@B|BLt@^bAtApD^jAlAvF\\`Aj@fAFHhCrDV\\b@ZxAr@NDb@DjAGbCO`@?r@Ft@Tj@TlC~A`@f@N^FXPp@r@pEHRRX^NxC`AXVb@j@b@v@L^HX?BHhAEn@GN[b@y@|@ILGb@Bb@DNP\\HL`@`@d@Pj@FbBDVB`@HvBx@nAZnDd@d@JPF\\^XZ`DtDd@\\r@d@`@X`@\\RXfAvBXt@r@hERrCJzCE`AW`Ay@vBgB|Dc@nAMv@AhABz@Fv@p@`E^vBJ\\^j@nAhAZVtDrBdBdAlB`B`@h@HLh@x@z@lBDXLhBh@tM@b@Af@IzBSxBgA|FWfBSlBy@rEgAxEO~@e@xDa@jEi@tCo@dCCNEX@b@Nt@l@jBLr@B\\Ep@a@xASx@IbB@p@J\\LRLNPN|Ax@~An@^HhADnCHtA@nD?zAN`@Fx@Lh@PXLt@p@\\n@`@pAf@bCh@vBRf@p@jAhApAt@dAPZLf@\\bCPt@n@zBLr@VhCLr@Nr@Lh@PPNJ\\NXDdADpA?p@Df@RzA`APPTPNRNTlBfELPBBh@f@jAj@t@R|Cf@THZN^VVVbBpCrDbFT^tAvBX\\rAhAbDlC?@lClBnAbAr@x@j@x@v@|AVf@Xd@fAhAvA`AlD~B~AbAtB~A`Ax@n@p@\\f@HLNf@Pn@PdAv@rENbAJXn@fA|AjBrC`DDFrAxAb@h@Pf@@R]zBQlBANGrAFr@DTnAjDbCtGFZHb@Dd@HjEBv@H|CDn@Hl@Vz@Zf@NRx@z@pAbB\\j@z@rAhAvAt@|@dEfEdBdB~B~BHFp@b@b@X|@^b@P|Bl@fB\\dANz@Dn@?dGG`@@h@Db@Jn@XtCzAvDhCrCjBd@PZFnBL`AL^NTH~@n@b@Zt@^|Aj@dFfATHh@TXRHJNRb@p@nAhBd@d@jCrBvJ`Kn@r@bBnB\\h@f@rA|@`D`AhDpAlEfBdFbDbIpB`F\\p@|@jB^|@b@`AFVd@lAn@zBb@rAn@vAj@v@VVrA|@HDf@p@FT`@bANn@RzADb@Hr@FdAFjBN`GBz@Db@Nr@f@`BPd@PV`@b@bBrC`A`ALR~@x@rAbAj@\\dAd@x@\\b@TJBd@EPGtC}@lAUb@CbADb@DhAH\\Jd@\\`@p@HRJj@@ZAVCVqAjGc@~ASd@e@h@KLeB`B_@^KLw@v@Yb@KXGnABx@Nn@^t@|@hAb@\\r@j@jBhAbAx@l@n@vB~Cj@~@|@rBR`ADn@JtC?@CfDA^cAxNG~@s@zKWtDAd@@b@L`AXpAN^hAvC\\`@XVzBlBFJxArBb@p@dCdFd@d@`A~@XRh@\\~@l@^Xh@z@p@zAv@dCZhAx@fDHb@F`@z@nFVhCFvB?^@rBRdGBdADpAPjAb@`CnArGf@zBNfARtA@b@AdAE~ABfBHh@^t@b@f@RXRRb@r@Vb@Nv@B~AC|AUtA_@rBEbCAzBMdCOpC?BAv@HzAv@bGDZHj@J`@Nh@d@xANd@Lz@Cl@On@INIHMHcA`@[PYN_@Xe@`@c@h@Sp@Kb@SdAQfASn@Ob@Yf@e@^_Ad@oCfAe@PeA\\mCv@oAd@mBh@E@yAn@WNMJW\\Yd@Q\\Wf@[r@M^ELYvAMzAA\\AXA|@CvBAXc@zBKh@Ab@S|ACHO\\MXGLc@z@Wd@Y`@{@`AoAlAcAdAqAlAOPuA`Ak@\\iAx@i@f@KL_@h@o@t@eBfB{@x@_@`@o@t@OPYh@MVqAtCc@lAOx@Kx@Gp@OvCCpA@bAFz@X`DD`@RbALl@PfAVjBPpAHl@N`DJjBFhABf@D|AAb@Mh@Y|@O^O`@Uv@Mz@M|@En@UlAIVKhACh@FdAFZBXXfAf@pBJVz@bCR`@l@dBP`AD\\FlC?t@Br@NdAd@hD?N@RBbALj@v@~BH^Ln@XfBTxA`@bBFNPfA@jACPMdAIz@CPBXL\\dCjDZh@^p@Z\\v@h@^TJPDHHRR\\RXRV`@^h@\\l@PfBVB@RJPJLLTXXp@l@hBVt@b@rAH^b@fAX^j@^\\NjAXf@NLHHHX`@Nf@BFBxA?JCz@CfACz@CXSv@_AxBm@xAc@pA_@hAEJo@~AYj@S`@e@|AMjAAZ?FBf@RbBZbDBp@B~@@`@Bb@BTFb@lAzDZtAD`@b@xCTfAF\\Rt@f@dAd@l@PPRLt@\\~@P~@HlCRr@Pz@Nd@P^LNZ\\h@DVAr@ADQbAYtACV?^F~@DXB\\JfADZXjAl@zAHPXh@Rf@R~@Pz@`@pA?X"}, "startLocation": {"latLng": {"latitude": 11.8046685, "longitude": 76.6881757}}, "endLocation": {"latLng": {"latitude": 11.6629239, "longitude": 76.2570537}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right to stay on NH 766\nToll road\nParts of this road are closed 9:00 PM – 6:00 AM\nPass by Kerala bus stop (on the left)"}, "localizedValues": {"distance": {"text": "55.6 km"}, "staticDuration": {"text": "1 hour 16 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 227, "staticDuration": "61s", "polyline": {"encodedPolyline": "g|dfAq}lpML@`@B`AHbGt@ZB"}, "startLocation": {"latLng": {"latitude": 11.6629239, "longitude": 76.2570537}}, "endLocation": {"latLng": {"latitude": 11.6609118, "longitude": 76.25667899999999}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left at Muthoot FinCorp Gold Loan to stay on NH 766\nPass by The Cake Shop (on the left)"}, "localizedValues": {"distance": {"text": "0.2 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 390, "staticDuration": "89s", "polyline": {"encodedPolyline": "uodfAg{lpMAd@Gf@_@rAGTuAnE]lAe@hBSXq@l@"}, "startLocation": {"latLng": {"latitude": 11.6609118, "longitude": 76.25667899999999}}, "endLocation": {"latLng": {"latitude": 11.662277300000001, "longitude": 76.2534396}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at Gandhi Jct to stay on NH 766\nPass by Musthafas Nuts Shop (on the left)"}, "localizedValues": {"distance": {"text": "0.4 km"}, "staticDuration": {"text": "1 min"}}, "travelMode": "DRIVE"}, {"distanceMeters": 4634, "staticDuration": "444s", "polyline": {"encodedPolyline": "gxdfA_glpMrBzHz@tBfA~BXf@V\\d@b@TPJHt@h@hA`ALFbCxATVd@n@FNHVDVH^Ff@JhBBb@Bl@?PAXKx@Y~@Up@M\\k@xAAFIRKTm@dAKPGHmBtCS\\eAxBi@lAOn@EpABpCC`@u@zDOr@?DMf@e@fAi@fAEL_@hAYrAGd@Gb@Gv@C~@ALDjC?d@?P@~D@VHfCBzAJt@Nd@f@nADHNh@DVBn@@f@@Z?BLvA`@rB\\hA`AjBFTDj@KzBFj@Rp@T`@\\^\\Tf@VxAr@bAb@|@f@DBJHZ\\v@lAj@`@b@Nl@Nf@J~AXt@Xf@`@~A~AbBlAHFJLJLPh@Jf@D^`B|DFl@BZ@z@@JFj@j@|AT|@NhARlAFf@@\\M|BC`AIr@a@tDIlB?zAHhBDv@"}, "startLocation": {"latLng": {"latitude": 11.662277300000001, "longitude": 76.2534396}}, "endLocation": {"latLng": {"latitude": 11.654380399999999, "longitude": 76.2166515}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left at Nice Bakery to stay on NH 766\nPass by <PERSON> Flower Collections (on the left)"}, "localizedValues": {"distance": {"text": "4.6 km"}, "staticDuration": {"text": "7 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 7713, "staticDuration": "766s", "polyline": {"encodedPolyline": "{fcfAaaepMp@_@h@a@b@W`@Kd@EZ?X@\\HZBfDQTB`@LNNFR?B@`@EvBBLRZ\\Jn@Jd@XRLJDLAJEHONe@j@oBPc@Ta@NSNK\\QzAa@p@UjBgA\\WlCkBZUf@]RERBLLfAlBr@hAfAbBDHbAz@hAv@f@VDD^VNZFXBd@Ar@E\\@`@Rf@|@zA|@l@^Zj@n@l@ZRF`@D`@DnCFlBZZBPEHCn@{@XUb@O`@Qh@YJKXYVg@Vy@Pq@J_APG|@Lr@Pz@^p@zAM|@CN?h@HbANvADVNPTFv@A~@Gx@GbAE`@DZHlBv@`@JPDZBTCx@_@n@_@PQAWOKQ?SFe@XY@UGWg@C[AMFc@p@]PIZMRAJ?X@bAd@XHDCDGBKCMSc@BYDGH?HFD\\XTB?Z@b@Ol@Wp@WNEREf@AZIBYYOaAOk@M]MUYPa@ZDXNNP`@DTAPChAm@b@KrAEVBF@JDTJ~AjA\\V\\|@DJHZTd@h@n@rAz@vA~@rCpBbBhA^Rd@^^b@HLP^b@dAb@`BZn@LRNN\\NRBl@@v@ElABv@Fn@?f@GZAfAU@AdBo@XKdA_@d@ORGNELEr@MdBMZ?x@QVGDAf@Ij@Kp@Kt@@|ALtAJxDJX?n@?v@BpB@VPlBPXEh@Ox@ORGrCy@l@KZEp@Eh@?nD`@zAPnBJ`CCl@Fp@TfAb@rAb@LDjAd@|A`@pBRFBhAj@PJ`@`@X^@@ZlALb@^lAPXf@l@@@t@dABDr@|@^Vl@?NEjBu@JGl@Y|@YTC@?d@EVI`BeAb@[xC{BhAq@n@m@"}, "startLocation": {"latLng": {"latitude": 11.654380399999999, "longitude": 76.2166515}}, "endLocation": {"latLng": {"latitude": 11.6039455, "longitude": 76.2098944}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left at Kolagappara Junction (Kavala) onto Kolagappara-Ambalavayal Rd/Vaduvanchal - Kolagappara Rd\nContinue to follow Vaduvanchal - Kolagappara Rd\nPass by Kolagappara (on the right)"}, "localizedValues": {"distance": {"text": "7.7 km"}, "staticDuration": {"text": "13 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 949, "staticDuration": "100s", "polyline": {"encodedPolyline": "ukyeAyvcpMTJjDlEpB|BDRC\\EV[x@GLg@z@yAfCYZmDxBY\\WZUZ_AxAEd@Bh@_@l@CHCJDLHRp@b@LFp@Pl@JTB"}, "startLocation": {"latLng": {"latitude": 11.6039455, "longitude": 76.2098944}}, "endLocation": {"latLng": {"latitude": 11.6041559, "longitude": 76.20352849999999}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at Manjappara Stores"}, "localizedValues": {"distance": {"text": "0.9 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 1111, "staticDuration": "101s", "polyline": {"encodedPolyline": "_myeAaobpMDRp@n@f@`@\\n@d@n@PLx@n@RZZd@^x@FTFZFl@BdAAd@MbAIZa@~@?VBDXV\\R~@^x@TLFVVv@zAd@dBJNNRN^j@vBn@xAVn@^l@TZZ`@t@`@FFBJNXJL"}, "startLocation": {"latLng": {"latitude": 11.6041559, "longitude": 76.20352849999999}}, "endLocation": {"latLng": {"latitude": 11.5994691, "longitude": 76.19538159999999}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right at Preesha jibi onto Manjappara - Nellarachal Rd\nPass by <PERSON><PERSON><PERSON> veed (on the left in 350m)"}, "localizedValues": {"distance": {"text": "1.1 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 1468, "staticDuration": "134s", "polyline": {"encodedPolyline": "uoxeAc|`pMFNPtABNv@pBjA|AX^TXJXCTGPi@`BKn@GNGZSp@E`AG|@EZGLa@Vk@LgAAg@@w@Na@LEBC@KJIJ?f@DL@D\\\\FDdAv@nAl@j@`@PXJRh@~@TXNX\\`@@@`ArAn@|@jA|A\\b@NTZPHDTBt@H`Ab@j@\\l@VrA|@"}, "startLocation": {"latLng": {"latitude": 11.5994691, "longitude": 76.19538159999999}}, "endLocation": {"latLng": {"latitude": 11.5957096, "longitude": 76.185997}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_RIGHT", "instructions": "Slight right onto Manjappara-Karapuzha Road\nPass by Kottampilakkal (house )nellarachal (on the left in 900m)"}, "localizedValues": {"distance": {"text": "1.5 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 5183, "staticDuration": "519s", "polyline": {"encodedPolyline": "exweAoa_pMKTmCdDGR?P?TGJ_@Vk@tAWr@Yf@EDSPQHkAb@KF_Az@cCvCCDc@t@_ApALHdAh@dAf@t@Rt@BB?r@BZFt@TJD|@b@JJDL@LMxAOzD@XBPNV~@fALTBBFNDPLfAPdATtAVzAFLPL@?T?~BYf@K\\Cv@MfBYb@Av@FLFr@p@JHZVz@j@vAh@F@|Al@b@NhA^|@b@jAx@b@b@RPTD|@ERAN@RJXVZb@\\ZVZd@^ZLVBhAPxA^f@ZRLl@TnATPFP@b@AbAAFA\\Av@Bl@P|@j@NFLLX^DPDd@LbBBPFRDHFBPH|@R\\NRRVTf@dAPl@Av@KVEVKRQXOHEVAJ@DLZ|@xAF^AH[j@IR@N\\t@FXPPPR\\V\\\\NJLZ?@^fB?BJXXr@Np@v@hB?@^hAJb@BF^dCBT@p@FrFJvFD|ATlDF|@@ZF\\@BJ^@HRZdAbAFH@NCZC|@EVId@IXY^UNEJ?TJLh@hABHCv@OrCA`ACRAJBFVDZVDbAAdAGt@?Fw@hC[|@"}, "startLocation": {"latLng": {"latitude": 11.5957096, "longitude": 76.185997}}, "endLocation": {"latLng": {"latitude": 11.581208199999999, "longitude": 76.15292160000001}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right\nPass by <PERSON><PERSON><PERSON> (on the left in 1.3 km)"}, "localizedValues": {"distance": {"text": "5.2 km"}, "staticDuration": {"text": "9 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 1564, "staticDuration": "267s", "polyline": {"encodedPolyline": "q}teAwrxoMb@v@x@^VHR@xBM^BR\\Rf@?JFTAPDNLTd@f@?JRxAFN@BX\\d@`@hAj@XXDJ`@|@`@b@TTVPLRNh@F^H\\j@pAFLd@dAXZjA^ZJz@d@`BfANH~@b@HDbA^THBF?BV~@v@pAFHBH?NAFa@NERD^DJTl@Jt@JjAEp@@D@F`@Xb@^JNL^Np@n@nA"}, "startLocation": {"latLng": {"latitude": 11.581208199999999, "longitude": 76.15292160000001}}, "endLocation": {"latLng": {"latitude": 11.5732769, "longitude": 76.1428183}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left at പത്രോസിന്റെ ബേക്കറി\nPass by Meppadi Tea Estates (on the right in 600m)"}, "localizedValues": {"distance": {"text": "1.6 km"}, "staticDuration": {"text": "4 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 2442, "staticDuration": "277s", "polyline": {"encodedPolyline": "_lseAssvoMv@MT@`@Dz@EFF\\bCPb@\\j@RNDBVLB@ZFjCC`@Bd@HNHL@H@TKZQNGZE~@G`@QT[\\i@JMJG|@]r@a@f@{@XWJMHSd@kAZa@TG\\?nBf@fAXNDR?XAnAIf@AF@j@Gl@M@?ZE\\D@?nA`@dAj@r@j@h@j@XTv@PJB^AnASfA]XSHQJaAFOJQRINARDHBhAxAJDJ?FIEeA?WN]DCNKXC`@FVFr@b@XH\\FV?TGPMFQL}@HKVQFAh@Gv@AJ@NNPn@JRNFd@@ZDXPR@f@?pAZPH`@j@d@RNNFFR^F`@?v@DJ"}, "startLocation": {"latLng": {"latitude": 11.5732769, "longitude": 76.1428183}}, "endLocation": {"latLng": {"latitude": 11.556413599999999, "longitude": 76.1422217}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left at Yoonas tea stall nedumbala onto Nedumbala-Meppadi Rd\nPass by GOOD LINE ASSOCIATES (on the right)"}, "localizedValues": {"distance": {"text": "2.4 km"}, "staticDuration": {"text": "5 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 1534, "staticDuration": "203s", "polyline": {"encodedPolyline": "qbpe<PERSON>{ovoMCPKh@ABSt@Ob@KZ?VBJL^NPl@j@LLPPP`@Pn@TVZRTJ`AVh@X`@`@R^DH`@p@h@t@T\\lAxBHPJN`@z@TdALp@Bd@B|@?D?LEb@[jBCn@?BF\\L\\^n@v@pAZr@FV@@H`@@H?n@?@E\\G~@?lAOjBCRADINQNIDE@_@PUFA@aB^}@VWNUXa@v@"}, "startLocation": {"latLng": {"latitude": 11.556413599999999, "longitude": 76.1422217}}, "endLocation": {"latLng": {"latitude": 11.5546945, "longitude": 76.130797}}, "navigationInstruction": {"maneuver": "TURN_SLIGHT_RIGHT", "instructions": "Slight right at Moopanad Bus Stop onto SH29\nPass by Kk Fire Works Dealer (on the left)"}, "localizedValues": {"distance": {"text": "1.5 km"}, "staticDuration": {"text": "3 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 671, "staticDuration": "125s", "polyline": {"encodedPolyline": "ywoeAohtoMt@SZCh@A^DNDJNF\\CTIHe@d@MXIV?\\DTTd@?JEXB^HJVJv@P`@R\\p@DR?bABb@@PETs@zAYj@QdAWf@[Z"}, "startLocation": {"latLng": {"latitude": 11.5546945, "longitude": 76.130797}}, "endLocation": {"latLng": {"latitude": 11.5539206, "longitude": 76.12649379999999}}, "navigationInstruction": {"maneuver": "TURN_SHARP_LEFT", "instructions": "<PERSON> left at <PERSON><PERSON><PERSON> son's bakery onto Chembra Peak Rd\nPass by M.S. FLOUR MILL (on the left)"}, "localizedValues": {"distance": {"text": "0.7 km"}, "staticDuration": {"text": "2 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 1475, "staticDuration": "206s", "polyline": {"encodedPolyline": "_soeAqms<PERSON><PERSON>`BHZd@x@^vAVv@HLz@\\LJJRDPA^@THPX^BDT`@\\VTJb@DNFHPLZFL@NEr@@LFJPJHLBt@DX\\z@?dAUTMHa@`AELDZd@v@FZKVOb@UTc@RGL?LHNHp@@LFn@j@r@PNXJHJP^\\FH?d@GH@PVHRt@x@FTCJKFQDKJAPNZTh@DV?n@FN^FPJDBRNJXZjANv@"}, "startLocation": {"latLng": {"latitude": 11.5539206, "longitude": 76.12649379999999}}, "endLocation": {"latLng": {"latitude": 11.549734299999999, "longitude": 76.1157449}}, "navigationInstruction": {"maneuver": "TURN_LEFT", "instructions": "Turn left at The Winds Homestay to stay on Chembra Peak Rd"}, "localizedValues": {"distance": {"text": "1.5 km"}, "staticDuration": {"text": "3 mins"}}, "travelMode": "DRIVE"}, {"distanceMeters": 4956, "staticDuration": "839s", "polyline": {"encodedPolyline": "yxneAk<PERSON><PERSON><PERSON><PERSON>@[EO@KPQ|@EpA@NJj@?ZYjCAFDLZ^b@RPAPK^c@VMVCv@@n@HJHAJi@BQFMH]NMLOVM`@e@ZKRARIV[T[PKR?N?@Rl@HTHLJp@?l@_@`@k@|@_@LM?YSu@k@QFUTKPEZET?b@@r@I\\Kn@CLQb@_@^QXGXe@|@[^]b@m@hB_@HYBaABWFKR?NFf@Gf@BL\\b@J?@K[WEI?QTo@VGNGb@CJ@@JGFUD[PKLBf@JHHJDL?PS|@Mx@C^Pr@ExAWbAA^Bb@LhA?ZEl@Qv@?f@Ln@?TUz@o@Z_@JyAfASHYHEBAFDHnDkA`@GB?AHGDYFWHs@d@U^GRTl@NVRN`A^f@b@LHH@RKD<PERSON>KTMNAL@^DdA^HJ^l@DP?RKPOFeARYLY\\EHUP_@V[LOP@r@IRQLER@XDH?TSBMAe@]QAODINKp@Sz@MjAUj@U^i@p@ARBZ?f@Wz@c@jA]b@cATQNCLDVNLl@LTNn@nAHNC^If@QZCVLp@Cr@KR?VFXLVJ^DZ?b@Kd@@j@Jb@PT@HEN]@MF?NTRC^WP@XBFj@bB?j@KPMh@A\\L^\\^FHJl@DFNR`ATNFKh@SHoANgAAYCMASEIBALJL\\XL\\H\\PXhBr@\\Pj@DVCb@FF@HJ"}, "startLocation": {"latLng": {"latitude": 11.549734299999999, "longitude": 76.1157449}}, "endLocation": {"latLng": {"latitude": 11.5532866, "longitude": 76.0879516}}, "navigationInstruction": {"maneuver": "TURN_RIGHT", "instructions": "Turn right to stay on Chembra Peak Rd\nPass by the playground (on the left)\nDestina<PERSON> will be on the right"}, "localizedValues": {"distance": {"text": "5.0 km"}, "staticDuration": {"text": "14 mins"}}, "travelMode": "DRIVE"}], "localizedValues": {"distance": {"text": "90.9 km"}, "duration": {"text": "2 hours 27 mins"}, "staticDuration": {"text": "2 hours 27 mins"}}}], "distanceMeters": 323781, "duration": "24445s", "staticDuration": "24445s", "polyline": {"encodedPolyline": "sx|mAe`oyMKPb@Zn@d@l@`@LHp@h@d@Z|@j@h@`@h@\\fAx@B@LHf@\\PLn@b@VPRN`@Xl@`@PLDBp@d@XRh@^RNf@\\^V\\V~@l@PNJHPJjAn@FB\\NXLd@Tz@`@n@XRJFBLFn@\\ZNx@\\TJPDt@RNBn@DX@V@V?bDDT?rCBL?p@@pA?j@?h@Ar@AfAAtAEd@G@FGdCAj@@R@JXp@DJX\\BDBFLZFT@N@FDp@LjBFr@B\\BRDr@JpA@R@JNzBRbC@ZJ|AFt@?T@LBr@HfD@XPvED|@Fl@Ft@Hp@Fl@Lr@DPHTLVBFDHLPRTLLZXFBPLv@\\ZLhA`@h@Vd@XBBHFLl@BZFx@FpADh@P~AAfACZ?HE\\MnAC\\RrAPrA@l@QbBKl@c@rCGpA?b@Pv@b@l@PVb@r@BHNVP^DHVl@HNFHn@bAHLb@l@LLl@x@J^FHXTHHf@VXL`@PNF\\PLFTHNHNJXLx@`@BDFJ@DCNO`@q@vAKRIVCDGLIPSh@Id@Bj@t@?\\@z@D`BExAE~FMVDlAj@dCtAd@^\\RVHj@Tv@`@\\RXNlAp@LHNFfAh@XN`Ab@^Jj@TnAd@|@ZdC~@tEjBlFjBhBh@LAb@H\\?FSTo@J[Nc@HWLDsBzFEL{AhEMZMXGNABWj@Yr@MXSb@gBbEMXOXO\\OZO\\MXWj@Yt@Sh@[n@GP]n@}@pBYl@q@zA]t@e@hA_ArBq@xAUd@GNIRIRKZGRWz@YnAMp@I`@G\\Kh@O~@Mr@Ov@[xAa@vB[nAIf@Ed@CNE^_@|BIh@AJGh@ShAG^WbBKj@u@zDOt@CPOt@Kj@EZQz@Gb@SfAENQv@IV]nAGR]nAGPEPELEPMf@Y`AEP[l@KXUz@Ut@GPc@tAELM`@M`@Od@CLKZWdA]dA]nAGRy@nC[z@O\\m@zAIRGNEJu@fB_AzBWn@GNOZELoBxESp@q@xBi@hBt@BHFF\\Px@HP^Rp@VF@p@J`@H`@Fz@DR?v@B~@BdB@^Ab@Af@LLDr@Xl@TVF`@?TGp@S`@?XHr@T|@^JDd@R|@XTDt@HVBP?P?~@CL?fBAP?L?l@GHA|@[JEf@OZEtAJ\\@r@DVBhADx@Fz@FZBP@p@D~BLtAJb@?TBl@Dp@Dd@B`@BVBf@@J?d@?jAAZ@jA?lC@~A@RB`@Dv@R`AR|EpAl@N|AZlFtAfCr@J@LDL@h@JVB^Dj@HN@D@F?~ANv@Fp@JVD`ALF?f@Fd@FZDF@n@F~@LrAJRBpALdC^RB~@NF@TDn@FJBl@FrBTfAJL@f@Bz@Dz@Bv@Bd@BjBDR?T?v@?l@@V?TAVA^?`B@fBC`ADhBDlAHN@hDRR?^D`@Lx@Hl@FH@n@JbABT?p@HF?t@Nd@Fr@FV@f@@r@NzAZDNJz@DTBHNT\\RDBFFBFTh@HZLj@HXRt@DRdA`E\\bAFLHXL\\HXLb@^nA^`BLRHV`@~@d@hAlClHdClGNd@h@xBZpAvAdFh@hB^tA`AtDF\\GxBU~CQ`DG~@Ex@Ex@E|@C~@Bf@FZFRPZHN|@hAPHTHFFZ^MJ_FpD{B`B_Al@RTNId@[j@a@BAJ?DC`BqAb@]\\Un@e@FElAcAb@[rCqBbA}@pB_BtB}A|@o@^Dt@Hd@@hALh@Hd@Lf@Hj@FnALl@Hd@Hd@Ft@JhAPb@Fp@H^D\\Dx@IXBn@Fj@F`AJp@HVBzABn@DX@R@p@FfB^TDNKP}@\\y@r@gAXo@NMOLk@jAk@|@Sf@Q|@OJUEgB_@q@GSAYAo@Ee@AgACEAq@IaAKk@Go@GYCI?o@H}@Kq@Ic@GiAQu@Ke@Ge@Im@IoAMk@Gg@Ie@Mi@IiAMe@Au@I_@EUQIFs@f@eE|CgE~CkA|@MJ[_@LKfFyDvAeAt@i@x@k@d@_@l@c@pByA`CiBzGcF`BmAr@g@~@o@jBsAb@_@bCiBx@k@x@k@|AkAzDwCjDgCxBcBCGJKlGsEKeAGUS[QIa@Gc@BSHGFSTG^@\\X~AFb@Lp@Fb@Hb@DZN`ABRFj@HpBHTIdC_@xDe@tDYjBQnAk@tDI^m@vB[`A]z@gAhDe@tA}HzTaAnCaDbI{@vBm@xAi@jAu@lBk@xAaD|Hk@|Aq@lB_BlFoAdFsAzHa@`DMjAYxCMlBQhEGz@AtD@jAHvDLxBDr@V~Bn@rE\\jBf@zBTx@~AbFd@nAp@~AjBzDfB~CzIjPvClFf@|@lBvCnBnC~ApBvEjG|@nAtBxClAjB~AvCrBxEhC~HNn@j@rCl@|DFb@RlBHfAVbDNnBJ~@\\zBX~A^~ALn@XdBPrAHjALrCB`AD|B@bADfDHzG@v@@j@@v@PjL@x@@f@@j@DvCH~AT`B\\nAl@`B\\r@\\n@\\f@fCpC^`@lAtAvA|Bj@pANb@Nd@FP^dBHh@DZDb@DxA?t@IbDGdCCv@IpBQvHGvBA|@KzDIjDQbHAnDH`CFx@PrBZjBnAnFfBtH|@rEf@`DF\\p@lFDr@XlD^rFNfFDvD?tC?n@ArBKlE[|FYrDKlAy@tG{@pFaA`FADGRCL{@nDiArEs@zC{@xEi@vD[dDc@dFc@~Fi@|Fa@tD}@rF{@zEmA|Go@nDi@rCo@hDs@bEiAbGuAtGKl@oCtMm@rCoCnMkAtFq@dDKd@Op@iBdHe@~Bw@fDm@zB[jAaBjFMb@cChJk@hBi@`Bk@jBmDrKaA~CeAdDiAzD}@|CcCvIiFnQ]dAkA~Ci@jAMVoAdCgApBmApBaBpC{CdFgLpRiJpOcDpF}@xAKNaAtAW\\UVmBbBeAp@yBhAi@Ro@TODmCx@eB\\yEzAeA^mGxBQJa@PiAp@mAbAs@n@{@dACHqA~AcAjB_DfEc@l@yBvCaBzBcI~JwAfBaAjAwCvCiChC]\\m@l@sCpCKJKHQT{@v@cDlDcC|CgEjFIJuDvEc@j@mErFmIfKSVu@`AKJmB`C{D`FkC`D_AbAkAfAyApAo@d@]f@g@lAS|@A^Cl@K^GJEH_@T[Fe@Ce@IOCa@?OB}@Pm@HuATC?w@\\]\\KTCd@DTDNXXRTLJTXDFJNj@q@k@p@~@xAb@t@zAlCtBfE`@t@|AlCT\\v@vAjBzCdAfBPZd@t@jCnE^h@h@t@~@dAXVt@l@\\VrDfC`@XnDdCp@d@LH`Ar@lBvAl@h@j@h@h@n@PXp@jAPXl@hAbBxC~ArCj@~@T\\BBvCdEp@z@\\\\b@b@RRb@^vDtC`D`Ch@^fAv@jHnFXRh@`@jDfCnBhA`Af@~@b@lG|CdAh@dAh@bCrAl@\\lBdA`Af@d@Vf@X`@T|@f@zDtBpAv@^VjA`Ab@^z@x@zBjCjBjCPVX`@`AxAX`@RVjBnCNRb@d@\\^z@n@j@^|Ar@~Aj@hF`B`DbAlDjAxDrAr@ZfAh@VNpCpBZXrB`Bz@p@dDfCj@d@fBrA`@\\XTZTPLj@b@xCzBpCxBzFxEn@d@^Vv@z@v@z@~@bAnCjDRXT^dAlALL~AxArDhC`B~@~@f@dAd@fC`AtAd@~@XnBj@NDxErBhB`ApBdAnAj@rBx@bA`@p@RbBh@bCv@hC~@bElA~Af@zErAdBf@PFXJb@RdA`@j@Z^Tt@d@xC`Cj@n@t@l@|@x@hB~AzClCrAlAbCzBrAnA\\ZhAbAXRZV^VNJ|Ax@fChAvBz@vBz@d@Rl@Tx@Zp@Xp@Xx@\\bBr@zB|@v@XnCp@vB^xCX~AJh@Bt@HnC^hARhARrATfARxCj@|FdAfEr@nAR|@L^DXDhCZbCZfB`@d@NjAb@|Av@`Al@fAx@XV`AbAXZTZh@t@r@rAP^N^j@dBT`A`@hCFpA@v@B|@DlF@x@BrABnA\\fTFfD@XBtBLhJD~BF~B@l@B|BHvENvFRlDP|ADd@Hf@n@~Dp@tC^pA|@nCr@lBpBdEz@~A`ChD`ApAdCnCdGnFzDhDdI`HTTtCfCr@l@jKdJhDfCrA~@fCtAnClAbC`AfElAjDv@`El@xD^fBF\\BhBDrCBnJ@dC@V?hA@tACrGCjC@`@?t@@~@@dA@zC@z@@pA@`FFnADlBRf@Hb@Hp@NpAb@~@^dB|@zAhAv@l@dJnHxApATPzBbC`AvAf@z@fAbC^v@JVXp@zF|MXn@lB`EP^N^hApCXv@HPxBnGvApDf@jA`AhBR\\n@lAjGrKfFjJXh@l@rAn@`B|@hCPh@`AvDv@zDp@rF^|DdAtOb@zGFfAD`@RtBnAjRHvAL~ADb@Db@Z|Dd@tCxAtFf@vAr@tCl@~AVl@^r@BDfA|Ap@x@`BlBhE`Fj@l@|AhB~AbBdD|CpG`G^ZdHrGhEzDlBfBd@`@LLfClCf@p@zAtB~@vAbClDl@x@NVPVhEjGZ^pArAnBzA\\TrHfFdC~AJFzJxGl@`@bAr@zAjAnBfBpBvBhB~B`AxAnBnD~B`G^hA`@~AtA|GX`Bj@bDrAlIxA~Ht@~EjApGj@`DdBxJBJJn@Hd@`ArFxArI|BpMVrBJbBFbCC~COhCOnAYzAk@zBe@`BSt@aAbDM^Ur@iAvDu@fCWz@yGzTUx@g@`BuB`H{@|CM`@wFdRgDvK}CnK{BvHuB`HwDhMcAvD]tB[dCQrCGzBJbDb@fFr@zDrAvEN^|AbDr@hAjBjClBrBXXf@b@nEdDzEdDFB`IvFvBzAnIdGbKdHnDjCfD|B\\V`CtBnBvBzBnCbBlBfAnA~@fAfNfPvRlUdCtCjC`DlCtCx@n@`BjAdEhClEdCjAr@~A`A`CrApDzBlEjC~@j@hLxGnBlArBlAzAz@jI|ElFbDzRlLxQrKxDtBtI`EfF`C|At@rB|@l@ZjHhDpBbA^RhD|AvAn@jAn@VRn@j@tCtCXXdLvKrIjIpDrD`AhAbCpDl@`AT^|@~A^n@T\\pGhKjBtCr@~@pBvB|@v@`@\\dAv@lAp@TNxC`BlKzFXNTLdAl@`C~An@b@j@`@`At@`@\\x@r@jDdD`H~G~A|AdFrF|@dAt@~@|BhCjCrCfCdCh@h@rEvElAlARPrDhDpB`B|AdAz@f@zCnApA`@tCp@dC`@zDh@hARfB^|@N|AXhAZjAZfDlArAp@^RvAx@xA`AdAv@jAbAf@b@j@d@~BrCb@t@vA`CfAzBz@vB~AnEd@rAz@~B~DxKp@xAP^Vb@TZh@p@vAvArBrAfAh@~Af@zAXhBTbGZhCNb@BjBL~Hb@h@Dz@Lz@NjAVjA\\|Ap@~A`Aj@`@l@h@`A`ANNr@|@dAhBJTv@fB\\dA^hBRdB@H^zDDd@XrCVlC`@nEJdANdBBf@JtAPbF@j@BvBDtEFdFFxAJbAj@vCfA~El@dCJ\\^z@rBxFt@lCNt@lBfK\\nCPvCTjNR`D|@hIz@`Id@rCxDpQhArF^vCNlCFtJ@z@FpABl@r@zGv@hGHn@@LFtAZ`DPxAXtBD`@dCpRrApIh@~B|@`E`@`BZfAZ|@d@bAjC~EtHpN|AtCdCrEhHvMvApCh@nAj@hBJ`@\\|BNhCNxGHdBFz@Hz@PvBHr@Jf@Hd@RnAl@tC|@zCXz@hArCdAbC|@hBx@fBf@bAN`@Rb@`ApBbBhDXj@lCdG|B`Fx@xAdAjBJP~A~Bj@v@pCjDNRr@bAVZbCxCJLDFRVjAbBp@|@jAdBrAdCdDjHvA~CR`@R\\dAhBxArB|AbBl@j@jB~Af@`@XTtBhBzAlAj@f@|ApAPLnGnFvRdPtLzJ~DfDfI~GXVzAtAxA|Ax@bA^l@hAfBNXR`@JVh@nAv@vBz@|CNp@nBxJvIdc@Np@vDvRbCvLz@hEt@~DPv@vAjHZ|AxAlHh@|BRr@Xt@\\p@bA`B`DrERXz@lA|CdE`E`GvDbFT\\lDvEV^z@hA\\b@xH|Jt@|@TXTVh@p@pHxI`EzEdD`Ez@nAn@pAl@pA`@jAlB|Gt@rD~@fFJl@PfAd@lCb@|Bx@nCp@jBxBdGzA`F\\rAv@bDl@dCb@lBn@fCfAfDdBjFv@bCXz@hAnDrAfEzAnEdBtEPd@dAnCfAfC@BxExLXt@j@xAfCvGXr@`EtKbB~EzCtIh@tBXrAJd@ZzBXtDBPNnK@h@BpAJ`Bb@lC`@lAh@bBzBvF|JpVRf@vDfJ`FpMdBfEJTL^Xl@jA`DzA~DnA~CRh@bAjCb@jA`BbETh@Th@tF|Mz@pBb@~@rGbOx@jBt@dBrAzCLVZp@lDfHfAtBXf@r@vAbB`DhAvBVf@fDrGtB`EbDfGvAbCnAbCNVzA|CdA~BPb@jAdDjCvHLZlBxFHXJZb@`BNh@fAnFRz@fBnIJr@Ft@B`CQfPE`GGxGEv@Gx@G~AQxA_BpGuAnGWjAKj@M~@SnCEh@KzE@dABfAJ|B^nFLfArAvMLfATxBn@|Fd@hE@NHl@PzBPxDAjD?TCj@GtAYlCQnAc@rCu@~EiAfH]lCOvAIvB@hB?j@JrBLzA|@rFH`@nCvOl@tEDf@TvBPzBTbEHtA~Ar[x@tOdAhSv@vOLvB`AlRDt@VjFJ`D@~DEnDI~BE~@[nEc@hEs@jEKj@}BnKk@fDMfAUdBY|CM|BI|AEhDAxEDpCBp@Bn@FtATlCd@tEn@jEh@nCzA~FnAvDbBfEfCdFrBfDV^^h@fB`C~@jAxC`DpBlB|ChCzDpCZPpC`BrBbAlEjBlBt@`Cv@nBj@lDx@rEx@tC^fAJv@HbBNjJn@lN~@hBRxANxBTd@HxF~@d@HhB\\pHpAh@LnEvA^Nj@\\zBpAjAz@|@z@z@x@Z\\`ApADFtAbCfAxBZx@dA`CBHfBlEDJxB|F`@nAf@|ALZL\\Tn@\\`AdBvFXbAhCtJXjAhApEnAdFdBpGdC|JnBxHxAxENf@Rn@z@nCZ`ANd@N`@^lA~@vClAxDv@dCZ`Av@~BDHHVl@bBj@xAz@rB`@`AP^r@~A\\v@b@hAh@lA\\z@~@xBd@fA`A`CrBzERh@N^HNN^^|@r@~Al@vATf@Td@^z@^|@d@hAt@fBtE`Lp@`Bn@~Ad@dBd@bCNpAJrABx@Dv@DbATrGPlBd@bCZjAr@pB|@jBNVvAbDnA~D^dCPzBJhBR~ERlFJzBHhDLtAHhCNpCZlDBb@r@~H~@lLRxBFt@TjCFl@HdAp@dIHp@RfCNhBDd@XpCXzCBPT`C^pBLv@H\\Nh@f@fBRf@b@lAb@lAh@hAbBfElDtIp@`BjAnCZr@rBdFz@dBtB`DdBrBh@h@rBdBr@f@hCbBz@h@bC`BdAz@p@p@lA~Ab@v@nDhIbEfJ`AtBn@lAnAbCb@x@b@v@FLdAlB^r@h@|@hA|BNTVb@rA`CjG`KrAtBjBrCpCrE`@p@R^RXXb@NTPZ^j@T\\hAjBd@t@v@dAx@fAbAjAxAvArAjApElF\\\\\\\\|@pAlAtBpEdG~B`DvC|DxC|Eh@hAZx@Zt@dBjFp@lBr@dBh@fAn@pAdApB~ClGr@nA`@v@NV`@n@r@jApBdCd@d@jCrBVPp@`@`Bx@|An@dDlA~HrCbCz@zAh@vEnBpAj@dCtAvA~@n@h@x@z@`ApAxAvB|BdDlAtAf@b@bBjAhGzCfAj@~CfBtA~@lAdApAdAZVn@h@@@~DfDTTVT~@|@lB|AZVLHNN~@`ANPt@`Ad@n@n@dApChEn@~@|@xA~AbCpB~Cd@p@~@bB^x@v@`CRz@`@`CbAnFBFf@dBp@`BJTh@z@NVNPVZhB`Br@l@lA~@x@r@LHv@n@vAlAnJxHdBrA|@r@PNLJ`DpCv@n@zEzDjCxBzDdD`@ZbCnBdB`BnCzCd@j@nB`C~@hAXXRVLL|AdB~AjB~DpEhDzDRR`@d@bBjBlCxCfArAfAnARTjCrCvAdBRRnAxAdAnAzBhCVZp@n@RR^^pA|A~AxApA|@\\R`Ab@vGlClBr@`Bj@l@V|@`@rD|A`A^^Nr@X~@ZTHtCnAbCvAh@^^Xv@n@rAvA~A|B|@dBv@lBh@~An@lCNp@XvAv@jEzBrLz@|ELn@lBlKj@nDh@lCLn@R`AXdBZbBfBdJh@rCXdBDX^nBj@~Cv@~DLh@n@dCj@nBp@`B`CzEfCvEP^d@`AvAtCtAfC|A~Cp@jAl@jAn@nAt@|Aj@`Aj@v@`@d@`@^HHt@n@VN`@Vv@^TJ^Lp@P~Bl@`@H`KvBTDtCl@VF`B`@pCh@hBTP@x@DnADb@?h@@lFDlIJxA@|@@^@b@?`CBz@?dDAzAGhCQhD[|@IbBKvDQrC?fA?nBBfBLhBNrBZp@L^F\\HlAZ`AV|Bp@hD~@XHXJh@N|Br@|An@b@Tr@f@z@t@tArAlAtAvBhCl@l@hAx@l@\\`Ah@`@Pr@P`@F^Fx@JxCJfADfCFP?`DJr@BN?N?jBF^Bz@BtEN`BHnADb@@b@@T?nCGhBQd@GjEm@rAKr@CpB?nBHV@|BPzRpAp@@dDLhDLhCFnBNr@Hh@EJGNOPSFKhA{Fd@}A|@qDTmCHu@XeBd@wAjAgDXmAXgDj@yFNiANw@Vy@^u@f@q@DEn@q@lA}AjAiBtCeHtAiDbEmKtGuPTk@nBoElBmEn@uAzCaHfJkQ|CyFrP_Y`DaFbFoHT]JMVa@HM`As@vBoA@AhBkAd@YvAk@dAS|Ew@fAQhAWxCkA`E}A~DeBzCoApKsEb@KbHoAjEy@|IwAdHwA|ASxFYvBIrCKBSvD]jFm@lAMn@GhBOhAK~AOzAO|A[g@SEe@cBHbBIDd@f@Rt@ShD_AjBa@|AWfAQ|Dm@@HtAUl@G^I^ERE\\C`AIbBUdIeApEo@xAMjBSjFo@vASfEk@bAIdBKnK[vDKb@?L?^A~LHpKLhA?zEB\\ArEa@xEc@\\ErCSZAzDNfL^rDL|@HpDb@|Dd@`FjAx@RbAX|B|@vB`Ab@RdCpA~@j@nA|@@@jBbAtC~AfBjAxCdCdCpC|@`AbCxChCzClCvDhBhD`A`CbFbMpBdFJRzApDdB`EtGjMj@fAh@bAlChFvDjHd@|@t@vARn@bArGh@dDFZ\\xAd@hBpB~GtAvEhAdExAlFDNlDzK`A`DJdAFvADz@A|AKhAMv@g@zBe@`B[l@oEfHq@|@e@l@g@h@aCnC_DlDqDrDQRGJWfAqAfJOxAGv@[pEIdCEp@?FGfA?h@DlDHpDBl@Rh@JJRH|CJrBH\\?bAAlIi@^C\\Cn@Gd@K`@IfA]x@_@vAaAx@s@n@g@HEVS~@e@fAa@pB_@tAMLA\\?J?NAl@ApEXzE^`BHvAAjBQjFu@h@GdBGh@C|BIn@AdCIRAXCz@Cr@A^ApAEp@ALA|AGpAOxCi@XIpHgBfAQ~CUVC^IfBs@t@]JIXSxBcBdAi@r@S`@KdAKt@ChAA|EC~AAjKG~ABfKMlACdAARArACjBCjBE~EE`AEnBITAdAGvJi@`AGnAAdCBD?vBH`FDvA?xI@dEAdB@|A@~@?hE?R?\\?dAG`@Gj@K@?`A]h@K`ASfCk@vGuA`Cm@fBa@zBa@`AMxBQv@Ix@It@EjAI|AM~EMnA@hACzCCp@AX?vGExHEtAEl@ExAWRIfA]rAm@ZMj@Wt@]xBeA^MlA[dDi@vASb@AN?h@@hAJfATb@LPJf@Rl@V|@^h@L`@Bb@?hEMN?NAdBCr@GrBc@dCo@zBq@fCo@hBg@VGx@QdBa@~Ac@fCu@vGcBhFqAfCu@XIZItA_@xDaAhDw@h@OFAbBc@b@KVGfEaApEmAvBi@jA]~@Uf@MrEiAf@MdAYb@MVG\\K|A]f@M|J{Bv@UfCeAh@[\\Y\\WfCcCDEpAwAjBoBbAeAt@u@VWl@m@r@s@JKLMxA_B`AaARSLKJMLOzAsAxA{A`A}@ZUpA}@`@U^SXQt@]rCaAxDqANGrBw@f@On@Sn@OTIb@KhBU@?bAEN?l@?J?rBN~BZb@DPBj@Fb@FdAJt@Dh@@jB?tH[xFSzEITAH?n@AvFGjC?hAHxAZTF\\LXNl@XxAx@lAp@`Az@`@h@JTLX`@tAH\\Nn@n@`Bf@`AZb@p@x@nAhAn@^p@\\z@Xl@F`DD`@@^JRLNPN\\`AlCbDzIZt@TXVPb@P`Cj@p@N~AXvAHpACXCj@I\\Gp@Sf@Q`NiGlAi@`Ai@vCoA|FoC|FmCxJqErAc@^Il@Ij@Cr@?`@BhBPzGn@~@RbBPrBPrATPDDB|@XXLd@Xl@`@l@d@tC|Bx@r@zCxBdAr@@?nBdAVJd@R\\LjA\\b@Lh@LnAVlAP~CTnCHzEFZ?~CLdDThBPjBLD?jDFrD@jAB~AFbBTbANjEhA\\H~A^p@R~A^l@NjA\\p@PZHh@LVFhCn@RDx@Rn@JNBz@@tBQ^Gx@IvJiA|FUlGMTC|FUnEc@@?xDk@nDk@HCxDw@fDaA|Bg@f@OdBa@hDk@pBWnBQfBQhC[jC_@tDc@v@ItE[`AI|AEnAA~A@F?~AFr@FLH`@BTB~BN\\?bBY`Dy@vBi@l@QNEJCPCh@Ox@UREVCREXCPAt@GJ?`ACR?@?~@?jA?\\?`B?`HBlH@z@?b@@`@@F@PAvA@nI@tB@hHDdB@fC@`FAn@?N?rA@lBJjC`@vCf@rEz@fB\\|AXTBfBPZ@`@@`@?hAG|DY|BWbAIjFg@nDc@|Bc@fBc@tAe@jD_AzBg@n@Ep@CrBJdS~AlHj@jGh@h@DfK|@~L`A`E\\x@F~BR`DTxDXxEZV@lG`@~@FZBL@lAFdAFjAF|BJN@d@Bv@DXBZ@^BH?f@BrFZzH^vJf@vETbSbAxFVhAHdBXZFpCx@n@Pd@LfF|ApFfBbWnHt@N|@HdFFtB?~C@rO`@j@BjGPxBA`EOtTm@xO_@fJSrBIdRg@n@@xA?`ADhI`AxBVnEd@`G`@v@@jA?rDKxFQfJWpDKvBEdKc@bBGrDOtHW~@ChBE|FKrJObFIlFMvEM~LSrDMz@A|GGnHGxGCdF?xLIhD?xPIrCD|ADB@|BNj@@pBLb@@vDDtCCb@ApMIjFKnAC|A?RAlEEjIKzOa@bGSt@CL?r@C`@AR?hB@pAFdALx@HdAHrALzCP`ENhDTjEZj@BfADz@FxAHbBJlBLhLn@bBDr@@nBEfCIdCOPArCWrAQnFu@pG{@xDc@bBQhDa@fHs@tDc@hLiAvDa@rFm@`BGjJTjAFfEHzCPn@@tCJtBVt@Td@Nt@ZjAp@pExELNvB`CrGxFxI|HhAbAbAv@^TzAn@f@Nd@Fv@FzCLfDHjBF|GN|FLrDHhHNrIHlOIvCH`DJ`BA`@CdAIvEe@dGm@~I]jCK`AIfBUj@IlIiB`@IdN{CfDm@lC]`BSjIu@xIqAhDi@xAWxAU|A[`De@pEs@~VmD^EdGw@tC[lC_@rAQf@IpDi@bAOdCYrCc@pDe@XEdI_A\\ElBQVGJCdD]bEe@bHo@lEm@tCg@zD{@rDkAnGqB@AtBm@xBq@d@OhIeClEyAf@Q|@]pC_AdDcAzDqAlA_@lAa@f@OdA_@tAa@|Bw@rAa@pA_@RGzAg@fCw@r@UzBu@|Bu@rAa@zBu@xAe@pAc@fCy@RE\\K`@Mn@SNExBq@zAe@dBi@DAbA[b@QrAa@dAa@tB}@z@g@dDgBb@Yv@c@`Ag@ZQXUhAo@bAe@HClAg@jAi@~@a@nAi@p@SRId@Q|@[dCw@pB{@XMdD{ABAdAm@nAk@NI|@e@t@[XGfACjCCzC?hDAJ?~@AbB?|EEN?rD?R?@P@n@DrBH~CHfADjBLlBHfABXLjBNpD@l@EXBlADhB@n@JzE@h@FlE@ZB|ALhEZzD^fCPbAXhAt@|B\\`AvFnLh@jA|B`FdAbCbArCvBnHlAnEXnAVjBBd@Bb@CdBOlCSpBWlEErB?~@BbA^`KNnG@dAHtC@h@?rAElAAPCdCAp@L|A@@|AlHFZZjAhAzD\\bBx@~E\\zBj@pDh@fC\\bAh@bAVZBBj@l@~@p@tAp@`Br@jAv@l@h@r@~@fBrDx@`B|@dB^|@\\dAPfAFdA?fACbDQ|I?l@Bz@PlAXdAd@`Ab@d@^\\ZVrA|@vBnATPPJbAj@\\Rb@XnA|@|@t@fDpDnGdHjArAd@|@x@vB|BfGhDrIN^bDhI\\v@fCzG~BjHbBtFb@|AnD~M`@`BFX`@nBPlARdDBlDDHApEGvD@`BB`BGd@BtEF`AVvBXfCbBtLb@rDJ`DHzELtFB|@HnEHpLBjE@`@AlDKlDMpA_@pCCNk@zECRCNa@vEG|@I~ACp@IdD@vAAp@@f@D`@^|FNpBZhDVtBThBZfCBtBGx@Eh@sA~Jc@xCKlAG|@ATCZAb@@~A@|@JhGBzD?|CCpAIhG?v@D`BT|B`@fB\\tAb@dB`@|APl@|@jDl@xBz@fFn@bFBZp@zHh@rFh@jG|AjMRrBH`B^jJHrCDf@Db@BPJj@Rn@Z~@r@xBjAnDjA~CjApBd@n@nAnAtAdAlBlA`BlAt@`Az@tA|AvCJRfAjBpDbFdAfBdAnCHXf@dBHd@Cx@QrBo@nFQdBE|@AVGxDD`AJj@f@bCd@|CV~@^hAx@pClA~E\\`Bt@jG|@lHH\\^p@d@t@\\p@bB`E`@|ADb@?`@KvEDdAPpBF`ADrBGnBWbCMpA?fAJv@n@zB`@nCHz@`A~HPzBBxA?j@Lb@b@|@Zd@j@h@TLjCt@vCn@`@Bz@MhACpAFx@Hb@NNH`@f@bBfDrA~CjDrHdDnGj@`AfDfFvAjCdDfFr@hAvAdCl@fBlDtJpC|HnCdH\\lAH~BB|A@VPn@dAdB`ApB|@fCx@rB`BbDh@fA\\bAjA~EHj@?bBE~CB~AFv@DNRt@fAdDl@nAt@rA~@lAtB`DVh@Nn@@J?xAm@jIIf@Kf@m@jBGTYjAW~A@b@DPHPNR~@l@t@d@l@p@N^L|@NnBJ`@HLTZRNnAt@LPP^T~@Rx@n@~C^pEj@~PVjM?~@B|BLt@^bAtApD^jAlAvF\\`Aj@fAFHhCrDV\\b@ZxAr@NDb@DjAGbCO`@?r@Ft@Tj@TlC~A`@f@N^FXPp@r@pEHRRX^NxC`AXVb@j@b@v@L^HX?BHhAEn@GN[b@y@|@ILGb@Bb@DNP\\HL`@`@d@Pj@FbBDVB`@HvBx@nAZnDd@d@JPF\\^XZ`DtDd@\\r@d@`@X`@\\RXfAvBXt@r@hERrCJzCE`AW`Ay@vBgB|Dc@nAMv@AhABz@Fv@p@`E^vBJ\\^j@nAhAZVtDrBdBdAlB`B`@h@HLh@x@z@lBDXLhBh@tM@b@Af@IzBSxBgA|FWfBSlBy@rEgAxEO~@e@xDa@jEi@tCo@dCCNEX@b@Nt@l@jBLr@B\\Ep@a@xASx@IbB@p@J\\LRLNPN|Ax@~An@^HhADnCHtA@nD?zAN`@Fx@Lh@PXLt@p@\\n@`@pAf@bCh@vBRf@p@jAhApAt@dAPZLf@\\bCPt@n@zBLr@VhCLr@Nr@Lh@PPNJ\\NXDdADpA?p@Df@RzA`APPTPNRNTlBfELPBBh@f@jAj@t@R|Cf@THZN^VVVbBpCrDbFT^tAvBX\\rAhAbDlC?@lClBnAbAr@x@j@x@v@|AVf@Xd@fAhAvA`AlD~B~AbAtB~A`Ax@n@p@\\f@HLNf@Pn@PdAv@rENbAJXn@fA|AjBrC`DDFrAxAb@h@Pf@@R]zBQlBANGrAFr@DTnAjDbCtGFZHb@Dd@HjEBv@H|CDn@Hl@Vz@Zf@NRx@z@pAbB\\j@z@rAhAvAt@|@dEfEdBdB~B~BHFp@b@b@X|@^b@P|Bl@fB\\dANz@Dn@?dGG`@@h@Db@Jn@XtCzAvDhCrCjBd@PZFnBL`AL^NTH~@n@b@Zt@^|Aj@dFfATHh@TXRHJNRb@p@nAhBd@d@jCrBvJ`Kn@r@bBnB\\h@f@rA|@`D`AhDpAlEfBdFbDbIpB`F\\p@|@jB^|@b@`AFVd@lAn@zBb@rAn@vAj@v@VVrA|@HDf@p@FT`@bANn@RzADb@Hr@FdAFjBN`GBz@Db@Nr@f@`BPd@PV`@b@bBrC`A`ALR~@x@rAbAj@\\dAd@x@\\b@TJBd@EPGtC}@lAUb@CbADb@DhAH\\Jd@\\`@p@HRJj@@ZAVCVqAjGc@~ASd@e@h@KLeB`B_@^KLw@v@Yb@KXGnABx@Nn@^t@|@hAb@\\r@j@jBhAbAx@l@n@vB~Cj@~@|@rBR`ADn@JtC?@CfDA^cAxNG~@s@zKWtDAd@@b@L`AXpAN^hAvC\\`@XVzBlBFJxArBb@p@dCdFd@d@`A~@XRh@\\~@l@^Xh@z@p@zAv@dCZhAx@fDHb@F`@z@nFVhCFvB?^@rBRdGBdADpAPjAb@`CnArGf@zBNfARtA@b@AdAE~ABfBHh@^t@b@f@RXRRb@r@Vb@Nv@B~AC|AUtA_@rBEbCAzBMdCOpC?BAv@HzAv@bGDZHj@J`@Nh@d@xANd@Lz@Cl@On@INIHMHcA`@[PYN_@Xe@`@c@h@Sp@Kb@SdAQfASn@Ob@Yf@e@^_Ad@oCfAe@PeA\\mCv@oAd@mBh@E@yAn@WNMJW\\Yd@Q\\Wf@[r@M^ELYvAMzAA\\AXA|@CvBAXc@zBKh@Ab@S|ACHO\\MXGLc@z@Wd@Y`@{@`AoAlAcAdAqAlAOPuA`Ak@\\iAx@i@f@KL_@h@o@t@eBfB{@x@_@`@o@t@OPYh@MVqAtCc@lAOx@Kx@Gp@OvCCpA@bAFz@X`DD`@RbALl@PfAVjBPpAHl@N`DJjBFhABf@D|AAb@Mh@Y|@O^O`@Uv@Mz@M|@En@UlAIVKhACh@FdAFZBXXfAf@pBJVz@bCR`@l@dBP`AD\\FlC?t@Br@NdAd@hD?N@RBbALj@v@~BH^Ln@XfBTxA`@bBFNPfA@jACPMdAIz@CPBXL\\dCjDZh@^p@Z\\v@h@^TJPDHHRR\\RXRV`@^h@\\l@PfBVB@RJPJLLTXXp@l@hBVt@b@rAH^b@fAX^j@^\\NjAXf@NLHHHX`@Nf@BFBxA?JCz@CfACz@CXSv@_AxBm@xAc@pA_@hAEJo@~AYj@S`@e@|AMjAAZ?FBf@RbBZbDBp@B~@@`@Bb@BTFb@lAzDZtAD`@b@xCTfAF\\Rt@f@dAd@l@PPRLt@\\~@P~@HlCRr@Pz@Nd@P^LNZ\\h@DVAr@ADQbAYtACV?^F~@DXB\\JfADZXjAl@zAHPXh@Rf@R~@Pz@`@pA?XL@`@B`AHbGt@ZBAd@Gf@_@rAGTuAnE]lAe@hBSXq@l@rBzHz@tBfA~BXf@V\\d@b@TPJHt@h@hA`ALFbCxATVd@n@FNHVDVH^Ff@JhBBb@Bl@?PAXKx@Y~@Up@M\\k@xAAFIRKTm@dAKPGHmBtCS\\eAxBi@lAOn@EpABpCC`@u@zDOr@?DMf@e@fAi@fAEL_@hAYrAGd@Gb@Gv@C~@ALDjC?d@?P@~D@VHfCBzAJt@Nd@f@nADHNh@DVBn@@f@@Z?BLvA`@rB\\hA`AjBFTDj@KzBFj@Rp@T`@\\^\\Tf@VxAr@bAb@|@f@DBJHZ\\v@lAj@`@b@Nl@Nf@J~AXt@Xf@`@~A~AbBlAHFJLJLPh@Jf@D^`B|DFl@BZ@z@@JFj@j@|AT|@NhARlAFf@@\\M|BC`AIr@a@tDIlB?zAHhBDv@p@_@h@a@b@W`@Kd@EZ?X@\\HZBfDQTB`@LNNFR?B@`@EvBBLRZ\\Jn@Jd@XRLJDLAJEHONe@j@oBPc@Ta@NSNK\\QzAa@p@UjBgA\\WlCkBZUf@]RERBLLfAlBr@hAfAbBDHbAz@hAv@f@VDD^VNZFXBd@Ar@E\\@`@Rf@|@zA|@l@^Zj@n@l@ZRF`@D`@DnCFlBZZBPEHCn@{@XUb@O`@Qh@YJKXYVg@Vy@Pq@J_APG|@Lr@Pz@^p@zAM|@CN?h@HbANvADVNPTFv@A~@Gx@GbAE`@DZHlBv@`@JPDZBTCx@_@n@_@PQAWOKQ?SFe@XY@UGWg@C[AMFc@p@]PIZMRAJ?X@bAd@XHDCDGBKCMSc@BYDGH?HFD\\XTB?Z@b@Ol@Wp@WNEREf@AZIBYYOaAOk@M]MUYPa@ZDXNNP`@DTAPChAm@b@KrAEVBF@JDTJ~AjA\\V\\|@DJHZTd@h@n@rAz@vA~@rCpBbBhA^Rd@^^b@HLP^b@dAb@`BZn@LRNN\\NRBl@@v@ElABv@Fn@?f@GZAfAU@AdBo@XKdA_@d@ORGNELEr@MdBMZ?x@QVGDAf@Ij@Kp@Kt@@|ALtAJxDJX?n@?v@BpB@VPlBPXEh@Ox@ORGrCy@l@KZEp@Eh@?nD`@zAPnBJ`CCl@Fp@TfAb@rAb@LDjAd@|A`@pBRFBhAj@PJ`@`@X^@@ZlALb@^lAPXf@l@@@t@dABDr@|@^Vl@?NEjBu@JGl@Y|@YTC@?d@EVI`BeAb@[xC{BhAq@n@m@TJjDlEpB|BDRC\\EV[x@GLg@z@yAfCYZmDxBY\\WZUZ_AxAEd@Bh@_@l@CHCJDLHRp@b@LFp@Pl@JTBDRp@n@f@`@\\n@d@n@PLx@n@RZZd@^x@FTFZFl@BdAAd@MbAIZa@~@?VBDXV\\R~@^x@TLFVVv@zAd@dBJNNRN^j@vBn@xAVn@^l@TZZ`@t@`@FFBJNXJLFNPtABNv@pBjA|AX^TXJXCTGPi@`BKn@GNGZSp@E`AG|@EZGLa@Vk@LgAAg@@w@Na@LEBC@KJIJ?f@DL@D\\\\FDdAv@nAl@j@`@PXJRh@~@TXNX\\`@@@`ArAn@|@jA|A\\b@NTZPHDTBt@H`Ab@j@\\l@VrA|@KTmCdDGR?P?TGJ_@Vk@tAWr@Yf@EDSPQHkAb@KF_Az@cCvCCDc@t@_ApALHdAh@dAf@t@Rt@BB?r@BZFt@TJD|@b@JJDL@LMxAOzD@XBPNV~@fALTBBFNDPLfAPdATtAVzAFLPL@?T?~BYf@K\\Cv@MfBYb@Av@FLFr@p@JHZVz@j@vAh@F@|Al@b@NhA^|@b@jAx@b@b@RPTD|@ERAN@RJXVZb@\\ZVZd@^ZLVBhAPxA^f@ZRLl@TnATPFP@b@AbAAFA\\Av@Bl@P|@j@NFLLX^DPDd@LbBBPFRDHFBPH|@R\\NRRVTf@dAPl@Av@KVEVKRQXOHEVAJ@DLZ|@xAF^AH[j@IR@N\\t@FXPPPR\\V\\\\NJLZ?@^fB?BJXXr@Np@v@hB?@^hAJb@BF^dCBT@p@FrFJvFD|ATlDF|@@ZF\\@BJ^@HRZdAbAFH@NCZC|@EVId@IXY^UNEJ?TJLh@hABHCv@OrCA`ACRAJBFVDZVDbAAdAGt@?Fw@hC[|@b@v@x@^VHR@xBM^BR\\Rf@?JFTAPDNLTd@f@?JRxAFN@BX\\d@`@hAj@XXDJ`@|@`@b@TTVPLRNh@F^H\\j@pAFLd@dAXZjA^ZJz@d@`BfANH~@b@HDbA^THBF?BV~@v@pAFHBH?NAFa@NERD^DJTl@Jt@JjAEp@@D@F`@Xb@^JNL^Np@n@nAv@MT@`@Dz@EFF\\bCPb@\\j@RNDBVLB@ZFjCC`@Bd@HNHL@H@TKZQNGZE~@G`@QT[\\i@JMJG|@]r@a@f@{@XWJMHSd@kAZa@TG\\?nBf@fAXNDR?XAnAIf@AF@j@Gl@M@?ZE\\D@?nA`@dAj@r@j@h@j@XTv@PJB^AnASfA]XSHQJaAFOJQRINARDHBhAxAJDJ?FIEeA?WN]DCNKXC`@FVFr@b@XH\\FV?TGPMFQL}@HKVQFAh@Gv@AJ@NNPn@JRNFd@@ZDXPR@f@?pAZPH`@j@d@RNNFFR^F`@?v@DJCPKh@ABSt@Ob@KZ?VBJL^NPl@j@LLPPP`@Pn@TVZRTJ`AVh@X`@`@R^DH`@p@h@t@T\\lAxBHPJN`@z@TdALp@Bd@B|@?D?LEb@[jBCn@?BF\\L\\^n@v@pAZr@FV@@H`@@H?n@?@E\\G~@?lAOjBCRADINQNIDE@_@PUFA@aB^}@VWNUXa@v@t@SZCh@A^DNDJNF\\CTIHe@d@MXIV?\\DTTd@?JEXB^HJVJv@P`@R\\p@DR?bABb@@PETs@zAYj@QdAWf@[ZP`BHZd@x@^vAVv@HLz@\\LJJRDPA^@THPX^BDT`@\\VTJb@DNFHPLZFL@NEr@@LFJPJHLBt@DX\\z@?dAUTMHa@`AELDZd@v@FZKVOb@UTc@RGL?LHNHp@@LFn@j@r@PNXJHJP^\\FH?d@GH@PVHRt@x@FTCJKFQDKJAPNZTh@DV?n@FN^FPJDBRNJXZjANv@M@[EO@KPQ|@EpA@NJj@?ZYjCAFDLZ^b@RPAPK^c@VMVCv@@n@HJHAJi@BQFMH]NMLOVM`@e@ZKRARIV[T[PKR?N?@Rl@HTHLJp@?l@_@`@k@|@_@LM?YSu@k@QFUTKPEZET?b@@r@I\\Kn@CLQb@_@^QXGXe@|@[^]b@m@hB_@HYBaABWFKR?NFf@Gf@BL\\b@J?@K[WEI?QTo@VGNGb@CJ@@JGFUD[PKLBf@JHHJDL?PS|@Mx@C^Pr@ExAWbAA^Bb@LhA?ZEl@Qv@?f@Ln@?TUz@o@Z_@JyAfASHYHEBAFDHnDkA`@GB?AHGDYFWHs@d@U^GRTl@NVRN`A^f@b@LHH@RKDOHKTMNAL@^DdA^HJ^l@DP?RKPOFeARYLY\\EHUP_@V[LOP@r@IRQLER@XDH?TSBMAe@]QAODINKp@Sz@MjAUj@U^i@p@ARBZ?f@Wz@c@jA]b@cATQNCLDVNLl@LTNn@nAHNC^If@QZCVLp@Cr@KR?VFXLVJ^DZ?b@Kd@@j@Jb@PT@HEN]@MF?NTRC^WP@XBFj@bB?j@KPMh@A\\L^\\^FHJl@DFNR`ATNFKh@SHoANgAAYCMASEIBALJL\\XL\\H\\PXhBr@\\Pj@DVCb@FF@HJ"}, "description": "Hosa Rd", "warnings": ["This route has tolls.", "This route includes a highway."], "viewport": {"low": {"latitude": 11.5487581, "longitude": 76.0879516}, "high": {"latitude": 12.932161299999999, "longitude": 77.7422734}}, "travelAdvisory": {}, "localizedValues": {"distance": {"text": "324 km"}, "duration": {"text": "6 hours 47 mins"}, "staticDuration": {"text": "6 hours 47 mins"}}, "routeLabels": ["DEFAULT_ROUTE"], "polylineDetails": {}}], "geocodingResults": {}}