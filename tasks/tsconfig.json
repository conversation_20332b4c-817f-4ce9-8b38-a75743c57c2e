{"compilerOptions": {"target": "ESNext", "lib": ["ESNext", "DOM"], "module": "NodeNext", "moduleResolution": "NodeNext", "resolveJsonModule": true, "typeRoots": ["types", "node_modules/@types"], "strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noUnusedLocals": true, "useUnknownInCatchVariables": true, "outDir": "dist/", "sourceMap": true, "sourceRoot": "scripts/", "allowSyntheticDefaultImports": true, "esModuleInterop": true}, "compileOnSave": true, "include": ["**/*.ts"], "settings": {"import/resolver": {"typescript": {}}}}