// DONOT update the rules directly in the Firebase console.
// Instead, update the rules in the `flutterApp` repo and then commit the chantes to `main` branch.

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

  	// Users can only manage their own documents, however others can read basic profile
    match /users/{userId} {
      allow read:   if request.auth.uid == userId;
      allow create: if request.auth.uid == userId;
      allow write:  if request.auth.uid == userId;
      allow delete: if false;
    }

    // Only authenticated users can read their own revenuecat document.
    match /users/{userId}/rides/{rideId} {
      allow read:   if request.auth.uid == userId;
      allow create: if request.auth.uid == userId;
      allow write:  if request.auth.uid == userId;
      allow delete: if false;
    }

    // Only authenticated users can read their own revenuecat document.
    match /users/{userId}/revenuecat/{rUserId} {
      allow read: if request.auth.uid == userId && request.auth.uid == rUserId;
      allow create, write, delete: if false;
    }

    // Anyone authenticated user can read but only adminId can create or update the document.
    match /rides/{rideId} {
      allow read:   if request.auth != null;
    	allow create: if request.auth != null && request.auth.uid == resource.data.creatorId;
      allow write:  if request.auth != null && request.auth.uid == resource.data.creatorId;
      allow delete: if false;
    }
    // Participants can read all documents but only manipulate theirs.
    match /rides/{rideId}/participants/{userId} {
      allow read:                  if request.auth != null;
      allow create, write, delete: if request.auth.uid == userId;
    }
    // Participants can read all documents but only manipulate theirs.
    match /rides/{rideId}/routes/{routeId} {
      allow read:                  if request.auth != null;
      allow create, write, delete: if request.auth.uid == get(/databases/$(database)/documents/rides/$(rideId)).data.creatorId;
    }

    // Disable create, read, update and delete for all other documents
    match /{document=**} {
      allow create, read, write, delete: if false;
    }
  }
}
